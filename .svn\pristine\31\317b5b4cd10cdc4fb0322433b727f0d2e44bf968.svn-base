<!-- ai听 -->
<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle">
        </div>
        <div class="ai-header">
          <div class="ai-bar">
            <HeadNavigation />
          </div>
        </div>
      </el-header>
      <el-main
        v-loading="loading"
        element-loading-text="请稍后..."
        element-loading-background="rgba(255, 255, 255, 0.7)"
      >
        <div class="LgscCon">
          <div class="fir-title">
            <div style="display: flex; align-items: center">
              <img src="./img/Ai听.png" alt="" />
              <div class="media-time-selector">
                <div class="media-player-container">
                  <!-- 替换原来的audio元素 -->
                  <div class="custom-audio-player">
                    <div class="player-controls">
                      <button @click="togglePlay" class="play-btn">
                        <i
                          :class="
                            isPlaying
                              ? 'el-icon-video-pause'
                              : 'el-icon-video-play'
                          "
                        ></i>
                      </button>
                      <div class="time-display">
                        {{ formatTime(currentTime) }}
                      </div>
                      /
                      <div class="time-display">
                        {{ formatTime(duration) }}
                      </div>
                      <div class="progress-container">
                        <div class="progress-track">
                          <div
                            class="progress-filled"
                            :style="progressStyle"
                          ></div>
                        </div>
                        <input
                          type="range"
                          class="progress-bar"
                          :value="currentTime"
                          :max="duration"
                          @input="seekAudio"
                          @mousedown="onSeekStart"
                          @mouseup="onSeekEnd"
                        />
                      </div>
                      <div class="volume-control">
                        <button @click="toggleMute" class="volume-btn">
                          <i
                            :class="
                              isMuted
                                ? 'el-icon-turn-off-microphone'
                                : 'el-icon-microphone'
                            "
                          ></i>
                        </button>
                        <input
                          type="range"
                          class="volume-bar"
                          v-model="volume"
                          min="0"
                          max="1"
                          step="0.01"
                          @input="changeVolume"
                        />
                      </div>
                      <div class="rate-control">
                        <el-dropdown trigger="click" @command="changeRate">
                          <span class="el-dropdown-link">
                            <i
                              class="el-icon-more-outline"
                              v-if="currentRateFalse == false"
                            ></i>
                            <button
                              class="rate-btn"
                              v-if="currentRateFalse == true"
                            >
                              {{ currentRate }}x
                            </button>
                          </span>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                              v-for="rate in playbackRates"
                              :key="rate"
                              :command="rate"
                              :class="{ 'active-rate': currentRate === rate }"
                            >
                              {{ rate }}x
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                    <!-- 隐藏的audio元素用于实际播放 -->
                    <audio
                      ref="audioPlayer"
                      :src="mediaFile.url"
                      @timeupdate="updateCurrentTime"
                      @play="onAudioPlay"
                      @pause="onAudioPause"
                      @loadedmetadata="onLoadedMetadata"
                      @ended="onAudioEnded"
                      style="display: none"
                    ></audio>
                  </div>

                  <!-- <audio
                    ref="audioPlayer"
                    class="media-element"
                    :src="mediaFile.url"
                    @timeupdate="updateCurrentTime"
                    @play="onAudioPlay"
                    @pause="onAudioPause"
                    controls
                  ></audio> -->
                </div>
              </div>
              <!-- <el-button class="btn-text">识别文字</el-button> -->
              <el-upload
                :show-file-list="false"
                action="#"
                :http-request="uploadFile_shibie"
                ref="upload"
                accept=".pdf,.docx,.doc,.ofd"
              >
                <el-button class="btn-text">识别文字</el-button>
              </el-upload>
              <el-upload
                accept=".mp3,.mp4,.wav,.amr,.wma,.aac,.flac,.m4a,.ogg,.webm,.mpg,.flv,.mkv,.3g2,.avi"
                class="upload-demo"
                action="#"
                :show-file-list="false"
                :http-request="uploadFile"
                drag
                multiple
              >
              </el-upload>
            </div>
          </div>
          <div class="sec-title"></div>
          <div class="nrCon">
            <div class="nrConRight floatLeft">
              <div class="nrConLeftBk" v-if="this.qhsb == true">
                <div class="bktitle">音频语言</div>
                <div class="bkUl">
                  <el-button class="activeBtn">中文（普通话）</el-button>
                  <!-- <el-button plain>英语</el-button>
                  <el-button plain>英语混合</el-button>
                  <el-button plain class="nomgnl">日语</el-button>
                  <el-button plain>韩语</el-button>
                  <el-button plain>粤语</el-button>
                  <el-button plain>河南话</el-button>
                  <el-button plain class="nomgnl">更多</el-button> -->
                </div>
                <div class="bktitle mtp">
                  出稿类型
                  <!-- <span class="ckyl">查看样例</span> -->
                </div>
                <div class="bkUl">
                  <el-button class="activeBtn">文稿</el-button>
                  <!-- <el-button plain disabled>字幕</el-button> -->
                </div>
                <!-- <div class="bktitle">专业领域</div>
                <div class="bkUl">
                  <el-button plain class="activeBtn">通用</el-button>
                </div> -->
              </div>
              <div class="nrConLeftBk nrConLeftBkS" v-if="this.qhsb == false">
                <div class="bkSbjg">
                  <img src="./img/识别结果.png" alt="" />
                </div>
                <!-- 元信息显示 -->
                <div class="nrConLeftSbjgCon">
                  <div
                    class="meta-info"
                    v-if="metaInfo && (metaInfo.ti || metaInfo.ar)"
                  >
                    <div class="meta-info-container"></div>
                    <div v-if="metaInfo.ti" class="meta-item">
                      <span class="meta-label">标题:</span>
                      <span class="meta-value">{{ metaInfo.ti }}</span>
                    </div>
                    <div v-if="metaInfo.ar" class="meta-item">
                      <span class="meta-label">艺术家:</span>
                      <span class="meta-value">{{ metaInfo.ar }}</span>
                    </div>
                    <div class="metaItem">
                      <el-select
                        v-model="metaInfoName"
                        multiple
                        placeholder="请选择"
                        @change="filterLyricsByNames"
                      >
                        <el-option
                          v-for="item in options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                  <div
                    class="nrS"
                    v-loading="Rsloading"
                    element-loading-text="润色中，请稍后..."
                  >
                    <div class="lrc-section" v-if="filteredLyrics.length > 0">
                      <div class="lrc-container" ref="lrcContainer">
                        <div
                          v-for="(line, index) in filteredLyrics"
                          :key="index"
                          :class="{
                            'active-lyric': activeLyricIndex === index,
                            editing: line.editing,
                          }"
                          class="lyric-line"
                          :ref="`lyricLine_${index}`"
                          @click="handleLyricClick(line.time, index)"
                        >
                          <div style="display: flex">
                            <div
                              class="lyric-line-img"
                              :style="{
                                backgroundColor: getRandomColor(line.lang1),
                              }"
                            >
                              <img src="./img/人员.png" alt="" />
                            </div>
                            <div style="width: calc(100% - 36px)">
                              <div class="flexBox">
                                <div
                                  class="lyric-one"
                                  @click="editName(line.lang1, index)"
                                >
                                  {{ line.lang1 }}
                                </div>

                                <div class="time-badge">
                                  {{ formatTime(line.time) }}
                                </div>
                              </div>

                              <div
                                class="lyric-text"
                                v-if="!line.editing"
                                @dblclick="startEditing(index)"
                              >
                                {{ line.lang2 }}
                              </div>
                              <el-input
                                v-else
                                type="textarea"
                                :autosize="{ minRows: 1, maxRows: 10 }"
                                v-model="line.lang2"
                                class="lyric-input"
                                @blur="stopEditing(index)"
                                @keyup.enter="stopEditing(index)"
                                ref="lyricInput"
                                v-focus
                              />
                              <div class="lyric-actions" v-if="!line.editing">
                                <div
                                  class="lyric-action-btn"
                                  @click.stop="startEditing(index)"
                                >
                                  <img src="./img/修改.png" alt="" />修改
                                </div>
                                <!-- 修改此处 -->
                                <!-- <div
                                  class="lyric-action-btn"
                                  @click.stop="RsLyric()"
                                >
                                  <img src="./img/润色.png" alt="" />润色
                                </div> -->
                              </div>
                              <div class="lyricRstext" v-if="line.runseText">
                                {{ line.runseText }}
                              </div>
                              <div class="lyric-actions" v-if="line.runseText">
                                <div
                                  class="lyricActionBtn"
                                  @click.stop="Application(index)"
                                >
                                  应用
                                </div>

                                <div
                                  class="lyricActionBtn"
                                  @click.stop="cancel(index)"
                                >
                                  撤销
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="btnqh" v-if="this.qhsb == false">
                    <!-- <el-button
                    class="activeBtn"
                    v-if="this.qhsb == false"
                    @click="qhClick(1)"
                    >返回</el-button
                  > -->

                    <el-dropdown trigger="click" v-if="this.qhsb == false">
                      <span class="el-dropdown-link">
                        <el-button class="activeBtn">复制</el-button>
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item @click.native="copyFullText"
                          >全文复制</el-dropdown-item
                        >
                        <el-dropdown-item @click.native="copyParsedText"
                          >解析文本复制</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </el-dropdown>
                    <el-button
                      class="activeBtn"
                      v-if="this.qhsb == false"
                      @click="schyjy(1)"
                      >生成会议纪要</el-button
                    >
                    <!-- <div
                                  class="lyric-action-btn"
                                  @click.stop="RsLyric()"
                                >
                                  <img src="./img/润色.png" alt="" />润色
                                </div> -->
                    <el-button
                      class="activeBtn"
                      v-if="this.qhsb == false"
                      @click="RsLyric()"
                      >润色</el-button
                    >
                    <!-- 版本1 -->
                    <!-- <el-button
                      class="activeBtn"
                      v-if="this.qhsb == false"
                      @click="uploadMeeting"
                      >上传知识库</el-button
                    > -->
                  </div>
                </div>

                <!-- <div class="btnqh" v-if="this.btnxs == true && this.qhsb == true">
                  <el-button class="activeBtn" @click="qhClick(2)"
                    >返回</el-button
                  >
                </div> -->
              </div>

              <div
                class="nrConRightHyjy"
                v-loading="dialoading"
                element-loading-text="会议纪要生成中，请稍后..."
              >
                <div class="zcyy"></div>
                <div class="nrConRightHyjyT">
                  <div class="nrConRightHyjyQwjy">
                    <img src="./img/全文纪要.png" alt="" />
                  </div>
                  <div
                    v-if="hyjyStr == ''"
                    style="text-align: center"
                    class="nrConRightHyjyTCon"
                  >
                    暂无内容
                  </div>
                  <div class="nrConRightHyjyTCon" v-else v-html="hyjyStr"></div>
                </div>
              </div>
            </div>
          </div>
          <el-dialog
            title="注册声纹"
            :visible.sync="editNameDialogVisible"
            width="30%"
            v-loading="editLoading"
            element-loading-text="注册声纹中，请稍后..."
          >
            <span
              ><el-input
                v-model="editNameInput"
                placeholder="请输入名称"
              ></el-input
            ></span>
            <span slot="footer" class="dialog-footer">
              <el-button type="primary" @click="editNameAdd">注册</el-button>
              <el-button @click="editNameDialogVisible = false"
                >取 消</el-button
              >
            </span>
          </el-dialog>
          <el-dialog title="识别结果" :visible.sync="sbVisible" width="50%">
            <vue-editor
              class="fir-textarea fir-textarea-max"
              type="textarea"
              placeholder=""
              :editorToolbar="[['']]"
              v-model="text1"
            ></vue-editor>
            <span slot="footer" class="dialog-footer">
              <el-button
                style="
                  font-size: calc(100vw * 10 / 1920);
                  line-height: 10px;
                  padding: 10px 20px;
                  background-color: #2975e6;
                  color: white;
                  margin-top: 35px !important;
                "
                @click="schyjy(2), (sbVisible = false)"
                >生成会议纪要</el-button
              >
            </span>
          </el-dialog>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import { VueEditor } from "vue2-editor";
import HeadNavigation from "../components/HeadNavigation.vue"; // 引入头部导航组件

import {
  getAsr,
  hyjy,
  asr_api1,
  asr_rewrite,
  insert_voice,
  listen_upload_meeting,
  up_shibie,
} from "../api/home.js"; // 接口请求
import store from "../store/index";
// import MediaTimeSelector from "./MediaTimeSelector.vue";
import { mapState, mapActions } from "vuex";
export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id

    username() {
      return this.$store.state.username;
    },
    progressPercentage() {
      return (this.currentTime / this.duration) * 100 || 0;
    },
    progressStyle() {
      return {
        "--progress": `${this.progressPercentage}%`,
      };
    },
  },
  data() {
    return {
      text1: "",
      sbVisible: false,
      result_sb: "",
      navShow: true,
      btn: "",
      file: "",
      filename: "",
      text: "",
      qhsb: true,
      btnxs: false,
      loading: false,
      dialogVisible: false,
      dialoading: false,
      Rsloading: false,
      hyjyStr: "",
      mediaFile: {
        file: null,
        url: "",
        name: "",
        type: "",
      },
      // 选定的时间范围
      selectedRange: {
        startTime: 0,
        endTime: 0,
        duration: 0,
      },
      // 提取的媒体信息
      extractedMedia: {
        url: "",
        name: "",
        blob: null,
      },
      parsedLyrics: [], // 解析后的歌词数据
      currentLyricTime: 0, // 当前歌词时间
      showBilingual: true, // 是否显示双语
      lastActiveIndex: -1, // 上次激活的歌词索引
      isPlaying: false, // 是否正在播放
      lyricUpdateInterval: null, // 歌词更新定时器
      activeLyricIndex: -1, // 当前高亮的歌词行索引
      currentTime: 0, // 当前播放时间
      scrollRequestId: null, // 滚动动画ID
      editingIndex: -1, // 当前正在编辑的行索引
      runseText: "",
      metaInfo: {}, // 添加元信息存储变量
      metaInfoName: "",
      options: [],
      originalLyrics: [], // 存储原始歌词数据
      filteredLyrics: [], // 存储筛选后的歌词数据
      editNameDialogVisible: false,
      editLoading: false,
      audioBuffer: null,
      startTime: 0,
      endTime: 0,
      editNameInput: "",
      aIid: "",
      // 新增的音频控制相关数据
      isMuted: false,
      volume: 1,
      duration: 0,
      isSeeking: false,
      playbackRates: [0.5, 0.75, 1.0, 1.25, 1.5, 2.0], // 可选的倍速选项
      currentRate: 1.0, // 当前播放速度
      showRateMenu: false, // 是否显示倍速菜单
      currentRateFalse: false, // 当前播放速度是否为自定义
    };
  },
  components: {
    VueEditor,
    HeadNavigation,
    // MediaTimeSelector,
  },
  mounted() {
    // this.in();
  },
  methods: {
    async uploadFile_shibie(options) {
      this.loading = true;
      const formData = new FormData();
      formData.append("meeting_file", options.file);
      // this.upsb(formData);
      let res = await up_shibie(formData);
      if (res.data.status_code === 200) {
        this.$message.success(res.data.message);
        this.text1 = res.data.data;
        this.sbVisible = true;
        this.loading = false;
      } else {
        this.$message.error(res.data.message);
        this.loading = false;
      }
    },

    // 新增的音频控制方法
    togglePlay() {
      if (this.filename == "") {
        this.$message.warning("请先选择音频文件");
        return;
      }

      // 确保设置了播放速度
      this.$refs.audioPlayer.playbackRate = this.currentRate;

      if (this.isPlaying) {
        this.$refs.audioPlayer.pause();
      } else {
        this.$refs.audioPlayer.play();
      }
    },

    seekAudio(e) {
      const seekTime = parseFloat(e.target.value);
      this.currentTime = seekTime;
      if (this.$refs.audioPlayer) {
        this.$refs.audioPlayer.currentTime = seekTime;
      }
      // 强制更新填充背景
      this.$forceUpdate();
    },

    onSeekStart() {
      this.isSeeking = true;
      if (this.isPlaying) {
        this.$refs.audioPlayer.pause();
      }
    },

    onSeekEnd() {
      this.isSeeking = false;
      if (this.isPlaying) {
        this.$refs.audioPlayer.play();
      }
    },

    toggleMute() {
      this.isMuted = !this.isMuted;
      this.$refs.audioPlayer.muted = this.isMuted;
    },

    changeVolume() {
      this.$refs.audioPlayer.volume = this.volume;
      this.isMuted = this.volume === 0;
    },

    onAudioEnded() {
      this.isPlaying = false;
      this.currentTime = 0;
    },

    updateDuration() {
      this.duration = this.$refs.audioPlayer.duration || 0;
    },

    // 改变播放速度
    changeRate(rate) {
      this.currentRateFalse = true;
      this.currentRate = rate;
      if (this.$refs.audioPlayer) {
        this.$refs.audioPlayer.playbackRate = rate;
      }
    },

    onLoadedMetadata() {
      this.duration = this.$refs.audioPlayer.duration;
      this.$refs.audioPlayer.playbackRate = this.currentRate;
      // 初始化进度条
      this.$nextTick(() => {
        const progressBar = this.$el.querySelector(".progress-bar");
        if (progressBar) {
          progressBar.max = this.duration;
        }
      });
    },

    editName(name, index) {
      console.log("点击的人名:", name);
      // 获取当前行的时间
      const currentTime = this.parsedLyrics[index].time;
      // 查找下一条歌词的时间（如果有的话）
      let nextTime = null;
      if (index < this.parsedLyrics.length - 1) {
        nextTime = this.parsedLyrics[index + 1].time;
      }
      console.log("当前时间:", currentTime, "下一条时间:", nextTime);
      this.startTime = currentTime;
      this.endTime = nextTime;
      this.editNameDialogVisible = true;

      // 调用截取音频的方法
      // this.extractAudioSegment(currentTime, nextTime);
    },

    // 修改editNameAdd方法，使用WAV Blob上传
    async editNameAdd() {
      if (this.editNameInput == "") {
        this.$message.error("名称不能为空");
        return;
      }
      this.editLoading = true;
      let fd = new FormData();
      fd.append("speaker_name", this.editNameInput);
      fd.append("start_time", this.startTime);
      fd.append("end_time", this.endTime);
      fd.append("uuid_file", this.aIid);
      // 添加WAV文件
      // fd.append("voice_file", this.audioBlob, `${this.editNameInput}.wav`);

      try {
        let data = await insert_voice(fd);
        if (data.status === 200) {
          this.$message.success("音频片段上传成功");
          this.editLoading = false;
          // 1. 更新options数组
          const newOption = {
            value: this.options.length + 1, // 新ID
            label: this.editNameInput,
          };
          this.options.push(newOption);

          // 2. 更新当前选中行的人名
          if (this.activeLyricIndex !== -1) {
            this.$set(
              this.parsedLyrics[this.activeLyricIndex],
              "lang1",
              this.editNameInput
            );

            // 3. 更新原始歌词数据以保持同步
            const originalIndex = this.originalLyrics.findIndex(
              (item) =>
                item.time === this.parsedLyrics[this.activeLyricIndex].time
            );
            if (originalIndex !== -1) {
              this.$set(
                this.originalLyrics[originalIndex],
                "lang1",
                this.editNameInput
              );
            }

            // 4. 如果当前有筛选，更新筛选结果
            if (this.metaInfoName.length > 0) {
              this.filterLyricsByNames(this.metaInfoName);
            }
          }
          this.editNameDialogVisible = false;
          this.editNameInput = "";
        } else {
          this.$message.error("上传失败: " + data.message);
        }
      } catch (error) {
        console.error("上传失败:", error);
        this.$message.error("上传失败");
      }
    },
    // 根据人名获取固定颜色
    getRandomColor(name) {
      // 定义颜色数组
      const colors = [
        "#FF6B6B",
        "#4ECDC4",
        "#45B7D1",
        "#FFA07A",
        "#98D8C8",
        "#F06292",
        "#7986CB",
        "#9575CD",
        "#64B5F6",
        "#4DB6AC",
        "#81C784",
        "#FFD54F",
      ];

      // 如果传入name，则根据name的hash值选择颜色
      if (name) {
        // 计算name的简单hash值
        let hash = 0;
        for (let i = 0; i < name.length; i++) {
          hash = name.charCodeAt(i) + ((hash << 5) - hash);
        }

        // 使用hash值选择颜色
        return colors[Math.abs(hash) % colors.length];
      }

      // 如果没有传入name，则随机返回一个颜色
      return colors[Math.floor(Math.random() * colors.length)];
    },

    /**
     * 格式化时间（将秒转换为 MM:SS 格式）
     */
    formatTime(seconds) {
      if (isNaN(seconds) || seconds < 0) return "00:00:00";
      const hours = Math.floor(seconds / 3600);
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${hours.toString().padStart(2, "0")}:${mins
        .toString()
        .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;

      // return `${mins.toString().padStart(2, "0")}:${secs
      //   .toString()
      //   .padStart(2, "0")}`;
    },

    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },

    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },

    uploadFile(item) {
      console.log(item, "item");

      this.btnxs = false;
      // this.blobToBase64(item.file, (dataurl) => {
      //   this.file = dataurl.split(",")[1];
      //   this.uploadMp(this.file);
      // });
      // 创建URL用于预览
      if (this.mediaFile.url) {
        URL.revokeObjectURL(this.mediaFile.url);
      }
      // 创建对象URL
      const url = URL.createObjectURL(item.file);

      // 设置媒体文件信息
      this.mediaFile = {
        file: item.file,
        url: url,
        name: item.file.name,
        type: item.file.type,
      };

      // 重置提取的媒体
      this.resetExtractedMedia();

      this.uploadMp(item.file);
      this.filename = item.file.name;
    },

    /**
     * 重置提取的媒体
     */
    resetExtractedMedia() {
      if (this.extractedMedia.url) {
        URL.revokeObjectURL(this.extractedMedia.url);
      }

      this.extractedMedia = {
        url: "",
        name: "",
        blob: null,
      };
    },

    async uploadMp(val) {
      // return;
      this.loading = true;
      let fd = new FormData();
      fd.append("file_name", this.filename);
      fd.append("base_string", val);
      fd.append("language", "Chinese");
      let resData = await getAsr(fd);
      console.log(resData);
      // 解析特定格式的LRC
      const parsed = this.parseSpecialLRC(resData.data);
      console.log(parsed, "parsed解析特定格式的LRC");

      this.parsedLyrics = parsed.lyrics;
      this.metaInfo = parsed.meta;

      // 将歌词文本合并到编辑器
      this.text = this.parsedLyrics.map((line) => line.text).join("\n");

      // 显示元信息
      if (this.metaInfo.ti) {
        this.$notify({
          title: "音频信息",
          message: `标题: ${this.metaInfo.ti}${
            this.metaInfo.ar ? "\n艺术家: " + this.metaInfo.ar : ""
          }`,
          duration: 5000,
        });
      }

      if (this.text != "" && this.text != undefined) {
        this.qhsb = false;
        this.loading = false;
        // this.hyjyStr = ""
        // this.dialogVisible = true;
      }
    },

    // 处理LRC歌词内容
    parseSpecialLRC(lrcText) {
      const lines = lrcText.split("\n");
      const result = [];
      const metaInfo = {};
      const timeRegex = /\[(\d{2}):(\d{2})\.(\d{2})\]/g;
      const nameSet = new Set(); // 用于存储去重后的人名

      // 提取第一行作为id
      const id = lines.shift().trim();
      this.aIid = id;

      // 提取元信息
      const metaRegex = /^\[(ti|ar|al):(.*)\]$/;
      for (const line of lines) {
        console.log(line, "line");

        const metaMatch = line.match(metaRegex);
        console.log(metaMatch, "metaMatch");

        if (metaMatch) {
          metaInfo[metaMatch[1]] = metaMatch[2].trim();
        }
      }

      // 解析歌词内容
      for (const line of lines) {
        const times = [];
        let text = line.trim();

        // 提取时间标签
        let match;
        while ((match = timeRegex.exec(line)) !== null) {
          const minutes = parseFloat(match[1]);
          const seconds = parseFloat(match[2]);
          const hundredths = parseFloat(match[3]);
          const time = minutes * 60 + seconds + hundredths / 100;
          times.push(time);
          text = text.replace(match[0], "");
        }

        // 处理有效歌词行
        if (times.length > 0 && text.trim()) {
          // 按所有空格分割文本
          const parts = text.trim().split(/\s+/);
          const name = parts[0] || "";

          // 将人名添加到集合中
          if (name) {
            nameSet.add(name);
          }

          times.forEach((time) => {
            result.push({
              time,
              text: text.trim(), // 完整文本
              parts: parts, // 分割后的数组
              lang1: name, // 第一个空格前的内容
              lang2: parts.slice(1).join(" ") || "", // 剩余内容
              isSecondary: false,
              isActive: false,
              editing: false,
              runseText: "",
            });
          });
        }
      }

      // 将去重后的人名转换为options格式
      this.options = Array.from(nameSet).map((name, index) => ({
        value: index + 1, // 从1开始的数字
        label: name, // 显示人名
      }));

      // 按时间排序
      result.sort((a, b) => a.time - b.time);
      // 保存原始歌词数据
      this.originalLyrics = result.sort((a, b) => a.time - b.time);
      this.filteredLyrics = [...this.originalLyrics]; // 初始时显示全部

      return {
        meta: metaInfo,
        lyrics: this.filteredLyrics,
      };
    },
    // 根据选中的姓名筛选歌词
    filterLyricsByNames(selectedValues) {
      if (selectedValues.length === 0) {
        // 如果没有选中任何选项，显示全部歌词
        this.filteredLyrics = [...this.originalLyrics];
      } else {
        // 获取选中的姓名列表
        const selectedNames = selectedValues
          .map((value) => {
            const option = this.options.find((opt) => opt.value === value);
            return option ? option.label : "";
          })
          .filter((name) => name);

        // 筛选歌词
        this.filteredLyrics = this.originalLyrics.filter((line) =>
          selectedNames.includes(line.lang1)
        );
      }

      // 更新显示的歌词数据
      this.parsedLyrics = this.filteredLyrics;
    },
    // 开始编辑歌词行
    startEditing(index) {
      // 如果已经有正在编辑的行，先保存
      if (this.editingIndex !== -1 && this.editingIndex !== index) {
        this.stopEditing(this.editingIndex);
      }

      // 设置当前行为编辑状态
      this.$set(this.parsedLyrics[index], "editing", true);
      this.editingIndex = index;

      // 下一帧确保DOM更新完成后再聚焦
      this.$nextTick(() => {
        const input = this.$refs.lyricInput && this.$refs.lyricInput[0];
        if (input) {
          input.focus();
          input.select();
        }
      });
    },

    // 停止编辑歌词行
    stopEditing(index) {
      if (this.editingIndex === -1) return;

      // 移除编辑状态
      this.$set(this.parsedLyrics[index], "editing", false);
      this.editingIndex = -1;

      // 这里可以添加保存逻辑，如调用API保存修改
      this.saveLyricChanges(index);
    },

    // 保存歌词修改
    saveLyricChanges(index) {
      const line = this.parsedLyrics[index];
      console.log("保存歌词修改:", line);
      // 这里可以调用API保存修改
      // this.$message.success('歌词修改已保存');
    },
    // 修改此处

    // 原有的方法保持不变
    async RsLyric() {
      this.Rsloading = true;

      // 获取所有行的文本内容并拼接成数组
      const allLinesText = this.parsedLyrics.map((line) => line.lang2);

      // 依次对每一项进行润色
      for (let i = 0; i < allLinesText.length; i++) {
        const currentLineText = allLinesText[i];

        // 模拟调用润色接口，这里只是简单示例，实际中需要替换为真实的接口调用
        const polishedText = await asr_rewrite({ asr_text: currentLineText });
        console.log("应用润色结果", polishedText);

        if (polishedText.status == 200) {
          // 将润色结果存储到当前行的 runseText 字段中
          this.parsedLyrics[i].runseText = polishedText.data.data;
        }
      }

      this.Rsloading = false;
    },

    Application(index) {
      // 应用润色结果到当前行
      this.parsedLyrics[index].lang2 = this.parsedLyrics[index].runseText;
      // 清空润色结果
      this.parsedLyrics[index].runseText = "";
    },
    cancel(index) {
      // 撤销润色结果
      this.parsedLyrics[index].runseText = "";
    },

    // 更新当前播放时间
    updateCurrentTime() {
      if (!this.isSeeking && this.$refs.audioPlayer) {
        this.currentTime = this.$refs.audioPlayer.currentTime;

        // 确保滑块位置同步更新
        const progressBar = this.$el.querySelector(".progress-bar");

        if (progressBar) {
          progressBar.value = this.currentTime;
        }
        console.log("当前播放时间:", this.currentTime);
      }
      this.updateActiveLyric();
    },
    // 更新当前高亮歌词
    updateActiveLyric() {
      if (!this.parsedLyrics.length) return;

      // 找到最后一个时间小于当前时间的歌词行
      let activeIndex = -1;
      for (let i = 0; i < this.parsedLyrics.length; i++) {
        if (this.parsedLyrics[i].time <= this.currentTime) {
          activeIndex = i;
        } else {
          break;
        }
      }

      if (activeIndex !== -1 && activeIndex !== this.activeLyricIndex) {
        this.activeLyricIndex = activeIndex;
        this.scrollToLyric(activeIndex);
      }
    },
    // 处理歌词点击
    handleLyricClick(time, index) {
      // 如果正在编辑，不处理点击事件
      if (this.editingIndex !== -1) return;
      this.activeLyricIndex = index;
      this.jumpToTime(time);
      this.scrollToLyric(index);
    },

    // 跳转到指定时间
    jumpToTime(time) {
      if (this.$refs.audioPlayer) {
        this.$refs.audioPlayer.currentTime = time;
        this.$refs.audioPlayer.play();
      }
    },

    // 音频播放事件
    onAudioPlay() {
      this.isPlaying = true;
      this.updateDuration();
      this.lyricUpdateInterval = setInterval(() => {
        this.updateCurrentTime();
      }, 50); // 更短的间隔时间使动画更流畅
    },

    // 音频暂停事件
    onAudioPause() {
      this.isPlaying = false;
      // 清除定时器
      if (this.lyricUpdateInterval) {
        clearInterval(this.lyricUpdateInterval);
        this.lyricUpdateInterval = null;
      }
    },

    // 滚动到指定歌词行
    scrollToLyric(index) {
      if (this.scrollRequestId) {
        cancelAnimationFrame(this.scrollRequestId);
      }

      const container = this.$refs.lrcContainer;
      const lineElement = this.$refs[`lyricLine_${index}`][0];

      if (!container || !lineElement) return;

      const containerHeight = container.clientHeight;
      const lineTop = lineElement.offsetTop;
      const lineHeight = lineElement.clientHeight;
      const targetScrollTop = lineTop - containerHeight / 2 + lineHeight / 2;

      const startScrollTop = container.scrollTop;
      const distance = targetScrollTop - startScrollTop;
      const duration = 300; // 动画时长(ms)
      let startTime = null;

      const animateScroll = (timestamp) => {
        if (!startTime) startTime = timestamp;
        const elapsed = timestamp - startTime;
        const progress = Math.min(elapsed / duration, 1);

        container.scrollTop = startScrollTop + distance * progress;

        if (progress < 1) {
          this.scrollRequestId = requestAnimationFrame(animateScroll);
        }
      };

      this.scrollRequestId = requestAnimationFrame(animateScroll);
    },

    schyjy(c1) {
      this.hyjyStr = "";
      // this.dialogVisible = true;
      this.hyjyBtn(c1);
    },
    // 全文复制
    copyFullText() {
      console.log("全文复制");

      let fullText = "";
      // 添加标题信息
      if (this.metaInfo.ti) {
        fullText += `标题: ${this.metaInfo.ti}\n`;
      }
      if (this.metaInfo.ar) {
        fullText += `艺术家: ${this.metaInfo.ar}\n`;
      }
      // 添加歌词信息
      this.parsedLyrics.forEach((line) => {
        const time = this.formatTime(line.time);
        fullText += `${time} - ${line.lang1} - ${line.lang2}\n`;
      });
      this.copyToClipboard(fullText);
    },
    // 解析复制
    copyParsedText() {
      console.log("解析复制");
      const parsedText = this.parsedLyrics.map((line) => line.lang2).join("\n");
      this.copyToClipboard(parsedText);
    },
    copyToClipboard(text) {
      const textarea = document.createElement("textarea");
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand("copy");
      document.body.removeChild(textarea);
      this.$message.success("复制成功");
    },

    async hyjyBtn(c1) {
      try {
        this.hyjyStr = "";
        const fd = new FormData();
        if (c1 == 1) {
          fd.append("text", this.text);
        } else if (c1 == 2) {
          fd.append("text", this.text1);
        }
        fd.append("user_id", this.id);

        const response = await hyjy(fd);
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);

        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let buffer = "";
        let inThinkTag = false; // 标记是否在<think>标签内
        let thinkTagEncountered = false; // 标记是否遇到过<think>标签

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");

          for (let i = 0; i < lines.length - 1; i++) {
            const line = lines[i].trim();
            if (!line.startsWith("data: ")) continue;

            const content = line.substring(6).trim();
            if (!content) continue;

            // 处理<think>标签
            if (content.includes("<think>") && !thinkTagEncountered) {
              this.dialoading = true; // 开始loading
              inThinkTag = true;
              thinkTagEncountered = true;
              continue; // 跳过当前内容
            }

            if (content.includes("</think>")) {
              this.dialoading = false; // 结束loading
              inThinkTag = false;
              continue; // 跳过当前内容
            }

            // 如果在<think>标签内，跳过内容
            if (inThinkTag) continue;

            // 处理正常显示内容
            const cleanContent = content
              .replace(/^"+|"+$/g, "")
              .replace(/\\u([\dA-Fa-f]{4})/g, (_, p1) =>
                String.fromCharCode(parseInt(p1, 16))
              )
              .replace(/\\[rn]/g, "\n")
              .replace(/\r?\n/g, "\n");

            console.log("后端返回的内容:", cleanContent);
            this.hyjyStr += cleanContent.replace(/\n/g, "<br>");
            await this.$nextTick(() => {
              const container = this.$el.querySelector(".nrConRightHyjyTCon");
              if (container) container.scrollTop = container.scrollHeight;
            });
          }

          buffer = lines[lines.length - 1];
        }
      } catch (error) {
        console.error("请求出错:", error);
        this.$message.error("会议纪要生成失败");
      } finally {
        this.dialoading = false;
      }
    },
    qhClick(val) {
      if (val == 1) {
        this.qhsb = true;
        this.btnxs = true;
      }
      if (val == 2) {
        this.qhsb = false;
        this.btnxs = false;
      }
    },
  },
  beforeDestroy() {
    // 清理URL对象
    if (this.mediaFile.url) {
      URL.revokeObjectURL(this.mediaFile.url);
    }
    // 清除定时器
    if (this.lyricUpdateInterval) {
      clearInterval(this.lyricUpdateInterval);
    }
    // 取消动画帧
    if (this.scrollRequestId) {
      cancelAnimationFrame(this.scrollRequestId);
    }
  },
};
</script>
<style lang="less" scoped>
.hc_outer {
  margin-left: 20%;
  margin-right: 20%;
}

.hc_inner {
  width: auto;
  height: auto;

  position: absolute;
  align-items: center;
}

.ejdhlTitle {
  width: calc(100vw * 300 / 1920);
  height: calc(100vh * 80 / 1080);
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: calc(100vw * 120 / 1920);
    height: calc(100vh * 48 / 1080);
  }

  p {
    border: 1.54px solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header {
  width: 100%;
  display: flex;
  // justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100vw * 1320 / 1920);

    .ai-bar {
      width: calc(100vw * 1320 / 1920);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vw * 40 / 1920);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: calc(100vw * 1 / 1920) solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vw * 130 / 1920);
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vw * 16 / 1920);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vw * 14 / 1920);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
                rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }
          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
              rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: calc(100vw * 93 / 1920);
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100vh * 1000 / 1080);
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: calc(100vh * 25 / 1080) 10px;
  // overflow: hidden;
}

.LgscCon {
  width: calc(100vw * 1824 / 1920);
  height: calc(100vh * 947 / 1080);
  margin: 0 auto;
  background: #fff;

  .button {
    width: calc(100vw * 108 / 1920);
    height: calc(100vh * 58 / 1080);
    background: #4c91ff;
    border-radius: 0px 100px 100px 0px;
  }

  img {
    width: calc(100vw * 45 / 1920);
    height: calc(100vh * 21 / 1080);
    margin-right: calc(100vw * 46 / 1920);
    margin-left: calc(100vw * 39 / 1920);
  }

  p {
    line-height: 58px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw * 14 / 1920);
  letter-spacing: 0;
  line-height: calc(100vh * 16 / 1080);
  overflow: hidden;
  // margin-top: calc(100vh * 10 / 1080);
  height: calc(100vh * 86 / 1080);
  display: flex;
  align-items: center;
  // 两端对齐
  justify-content: space-between;

  .fir-kuai {
    width: calc(100vw * 6 / 1920);
    height: calc(100vh * 16 / 1080);
    margin-right: calc(100vw * 8 / 1920);
    float: left;
    // margin-top: 2px;
    background: #4081ff;
    border-radius: 1.5px;
  }

  .fir-title-p {
    width: 100%;
    color: #222;
    font-weight: 500;
    font-size: calc(100vw * 16 / 1920);
    letter-spacing: 0;
    line-height: calc(100vh * 16 / 1080);
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.floatLeft {
  float: left;
}

.nrCon {
  // margin-top: 30px;

  .nrConLeft {
    width: 100%;
    height: auto;
    display: flex;
    align-items: center;
  }

  .nrConRight {
    width: 100%;
    height: auto;
    position: relative;
    display: flex;
    align-items: center;
    background: #eaf3fd;

    .btnqh {
      width: 100%;
      position: absolute;
      bottom: 14px;
      // 居中
      left: 50%;
      transform: translateX(-50%);
      .el-button {
        width: calc(100vw * 104 / 1920);
        height: calc(100vh * 36 / 1080);
        line-height: 15px;
        padding: 5px;
        font-size: calc(100vw * 16 / 1920);
        margin-left: 0;
        margin-right: calc(100vw * 14 / 1920);
        font-family: SourceHanSansSC-Regular !important;
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 400;
        background-image: linear-gradient(270deg, #62afff 0%, #4380ec 100%);
        box-shadow: 0px 2px 10px 0px rgba(89, 150, 241, 0.27);
        border-radius: 24px;
      }

      .nomgnl {
        margin: 0;
        margin-top: calc(100vh * 10 / 1080);
      }
    }
  }
}

.nrConLeftSbjgCon {
  width: calc(100% - 40px);
  height: calc(100% - 58px);
  background: #ffffff;
  overflow-y: scroll;
  padding: 15px 28px;
  font-family: SourceHanSansSCVF-Regular;
  font-size: calc(100vw * 16 / 1920);
  color: #060b1a;
  letter-spacing: 0;
  line-height: 26px;
  font-weight: 400;
  margin: 0 auto;
}

.nrConRightHyjy {
  width: calc(100vw * 914 / 1920);
  height: calc(100vh * 860 / 1080);
  background: #eaf3fd;
  display: flex;
  align-items: center;
  .zcyy {
    width: 20px;
    height: 100%;
    background: url(./img/右侧阴影.png) no-repeat center;
  }
  .nrConRightHyjyT {
    width: 100%;
    height: 100%;
    .nrConRightHyjyQwjy {
      height: 58px;
      width: 100%;
      display: flex;
      align-items: center;
      img {
        width: 88px;
        height: 21px;
        margin: 0;
        margin-left: 6px;
      }
    }
    .nrConRightHyjyTCon {
      width: calc(100% - 20px);
      height: calc(100% - 58px);
      background: #ffffff;
      overflow-y: scroll;
      padding: 15px 28px;
      font-family: SourceHanSansSCVF-Regular;
      font-size: calc(100vw * 16 / 1920);
      color: #060b1a;
      letter-spacing: 0;
      line-height: 26px;
      font-weight: 400;
      text-align: left;
    }
  }
}

.nrConLeftBk {
  width: calc(100vw * 906 / 1920);
  height: calc(100vh * 860 / 1080);
  background: #ffffff;
  padding: calc(100vw * 24 / 1920);
  position: relative;
  margin-right: calc(100vw * 7 / 1920);

  .bktitle {
    height: auto;
    text-align: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
    margin-bottom: 10px;
  }

  .bkUl {
    display: flex;
    flex-wrap: wrap;
    height: 70px;

    .el-button {
      width: 104px;
      height: 30px;
      line-height: 15px;
      padding: 5px;
      font-size: calc(100vw * 14 / 1920);
      margin-left: 0;
      margin-right: 9px;
      margin-top: 10px;
      font-family: SourceHanSansSC-Medium !important;
    }

    .nomgnl {
      margin: 0;
      margin-top: 10px;
    }
  }

  .nrS {
    //
    width: 100%;
    height: 88%;
    // background: #f2f4f9;
    // padding: 20px;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 16 / 1920);
    color: #333333;
    letter-spacing: 0;
    line-height: 35px;
    font-weight: 400;
    text-align: left;

    // overflow-y: auto;
    /* 添加滚动条 */
    .fir-textarea {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #f8f9fd;
      border: 1px solid rgba(229, 232, 245, 1);
      border-radius: 4px;
      // margin: 20px;
      margin-bottom: 0;
      height: 158px;
      overflow-y: scroll;

      ::v-deep(.el-textarea__inner) {
        font-size: 14px !important;
        background-color: #f8f9fd !important;
        height: 100% !important;
        font-family: PingFangSC-Regular;
        padding: 13px 18px 33px 16px;
        // overflow: auto;
      }
    }

    .fir-textarea-max {
      height: 100% !important;
    }

    ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
      display: none !important;
    }

    ::v-deep(.ql-blank) {
      display: none !important;
    }

    ::v-deep(.ql-editor) {
      // width: 443px;
    }

    ::v-deep(.ql-container.ql-snow) {
      border: 0;
    }
  }
}
.nrConLeftBkS {
  padding: 0;
  background: #eaf3fd;
}
::v-deep(.el-button) {
  span {
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw * 14 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
}

.mtp {
  margin-top: 42px;
}

.ckyl {
  font-family: PingFangSC-Medium;
  font-size: calc(100vw * 14 / 1920);
  color: #2c68ff;
  letter-spacing: 0;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

::v-deep(.el-upload-dragger) {
  width: calc(100vw * 295 / 1920);
  height: calc(100vh * 54 / 1080);
  background: url(./img/upload.png) no-repeat center;
  background-size: 70%;
  border: none;
}

.upload-demo {
  width: calc(100vw * 295 / 1920);
  height: calc(100vh * 54 / 1080);
  border: 1px solid rgba(0, 126, 255, 1);
  border-radius: 30px;
}

.nrConRightBk {
  height: 238px;
}

.flexLd {
  display: flex;
  justify-content: space-between;
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: 10px;
  height: 10px;
}

.user-menu-content {
  width: calc(100vw * 246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw * 15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw * 12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      // margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/
  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.activeBtn {
  // background: #1a66ff;
  //   border: 1px solid #1f52b0;
  //   border-radius: 4px;
  width: calc(100vw * 160 / 1920);
  height: calc(100vh * 40 / 1080);
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
}

.selected-range-info {
  margin-bottom: 55px;
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
}

.selected-range-info h3 {
  font-size: calc(100vw * 18 / 1920);
  margin-bottom: 15px;
  color: #303133;
}

.range-details {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.range-item {
  flex: 1;
  min-width: 150px;
}

.range-item .label {
  font-size: calc(100vw * 14 / 1920);
  color: #909399;
  margin-right: 8px;
}

.range-item .value {
  font-size: calc(100vw * 16 / 1920);
  color: #303133;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.extracted-section {
  margin-top: 40px;
  padding: 20px;
  background-color: #f0f9eb;
  border-radius: 8px;
}

.extracted-section h2 {
  font-size: calc(100vw * 22 / 1920);
  color: #67c23a;
  margin-bottom: 20px;
  text-align: center;
}

.extracted-media {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.extracted-media video,
.extracted-media audio {
  width: 100%;
  max-width: 600px;
  margin-bottom: 15px;
}

.extracted-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
}

/* 歌词区域样式 */
/* 元信息样式 */
.meta-info {
  margin-bottom: 24px;
  // margin-left: 39px;
  display: flex;
  align-items: center;
  position: relative;
}

.meta-item {
  font-family: SourceHanSansSCVF-Bold;
  font-size: calc(100vw * 20 / 1920);
  color: #000000;
  letter-spacing: 0;
  font-weight: 700;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-weight: bold;
  color: #555;
  min-width: 60px;
}

.meta-value {
  font-weight: 500;
}

.lrc-section {
  height: 100%;
}

.lrc-container {
  height: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;
}

/* 滚动条样式 */
.lrc-container::-webkit-scrollbar {
  background: #ffffff;
  width: 6px;
}

.lrc-container::-webkit-scrollbar-track {
  background: #ffffff;
  border-radius: 3px;
}

.lrc-container::-webkit-scrollbar-thumb {
  background: #c8dcff;
  border-radius: 3px;
}

/* 歌词行样式 - 添加点击效果 */
.lyric-line {
  // display: flex;
  // align-items: center;
  width: 808px;
  // margin-left: 26px;
  margin-bottom: 20px;
  padding: 10px 15px;
  border-radius: 13px;
  transition: all 0.3s;
  cursor: pointer; /* 添加手型光标 */
}

.lyric-line-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #af2323;
  margin-right: 15px;
  img {
    width: auto;
    height: auto;
    margin: 0;
  }
}

.lyric-line:hover {
  background: #f3f4ff;
  border-radius: 13px;
}

.active-lyric {
  background: #f0f7ff;
  transform: scale(1.01); /* 添加轻微放大效果 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
}
.lyric-one {
  font-family: SourceHanSansSCVF-Medium;
  font-size: calc(100vw * 18 / 1920);
  color: #081946;
  letter-spacing: 0;
  font-weight: 500;
  margin-right: 10px;
  cursor: pointer;
}

.time-badge {
  font-family: SourceHanSansSCVF-Regular;
  font-size: calc(100vw * 18 / 1920);
  color: #666666;
  letter-spacing: 0;
  font-weight: 400;
}

.lyric-text {
  line-height: 1.6;
  background: #f3f7ff;
  border: 1px solid rgba(218, 227, 244, 1);
  border-radius: 4px;
  padding: 6px 21px 13px 13px;
  margin-top: 16px;
}
.lyricRstext {
  line-height: 1.6;
  background: #eafcf9;
  border: 1px solid rgba(200, 233, 232, 1);
  border-radius: 4px;
  padding: 6px 21px 8px 13px;
  margin-top: 50px;
}
/* 导出按钮样式 */
.el-button--mini {
  padding: 5px 10px;
  font-size: calc(100vw * 12 / 1920);
}

.media-time-selector {
  width: calc(100vw * 1200 / 1920);
  /* height: 447px; */
  margin: 0 auto;
  margin-right: calc(100vw * 31 / 1920);
}

.media-player-container {
  width: 100%;
}

/* 音频播放器样式修改 */
.media-element {
  width: 100%;
  height: 54px;
  background: #ffffff;
  border: 1px solid rgba(229, 238, 255, 1);
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.11);
  border-radius: 37px;
  -webkit-appearance: none !important;
}

// /* 音频控件面板 - 背景保持白色 */
// audio::-webkit-media-controls-panel {
//   background-color: white !important;
// }

// /* 所有控制元素（播放按钮、时间显示等）变为蓝色 */
// audio::-webkit-media-controls-play-button,
// audio::-webkit-media-controls-current-time-display,
// audio::-webkit-media-controls-time-remaining-display,
// audio::-webkit-media-controls-mute-button,
// audio::-webkit-media-controls-volume-slider-container,
// audio::-webkit-media-controls-fullscreen-button {
//   color: #4081ff !important;
//   filter: none !important;
// }
// /* 图标颜色 - 使用SVG滤镜将黑色图标转为蓝色 */
// /* 播放/暂停按钮 */
// audio::-webkit-media-controls-play-button,
// audio::-webkit-media-controls-timeline,
// audio::-webkit-media-controls-volume-slider,
// audio::-webkit-media-controls-mute-button {
//   filter: brightness(0) saturate(100%) invert(39%) sepia(57%) saturate(2476%)
//     hue-rotate(202deg) brightness(101%) contrast(101%) !important;
// }

/* 歌词行编辑状态样式 */
.lyric-line.editing {
  background-color: #f0f7ff;
  border: 1px dashed #3a8ee6;
}

/* 歌词输入框样式 */
.lyric-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: inherit;
  font-family: inherit;
  width: 732px;
  color: inherit;
  padding: 0;
  margin: 0;
}

/* 歌词操作按钮样式 */
.lyric-actions {
  float: right;
  margin-left: 10px;
  opacity: 0;
  transition: opacity 0.3s;
  display: flex;
}

.lyric-line:hover .lyric-actions {
  opacity: 1;
}

.lyric-actions i {
  margin-left: 8px;
  cursor: pointer;
  color: #999;
  font-size: calc(100vw * 14 / 1920);
}

.lyric-actions i:hover {
  color: #3a8ee6;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: calc(100vw * 12 / 1920);
}
.demonstration {
  display: block;
  color: #8492a6;
  font-size: calc(100vw * 14 / 1920);
  margin-bottom: 20px;
}
.sec-title {
  width: 100%;
  border-bottom: 1px solid rgba(220, 220, 220, 1);
  height: 1px;
}
.bkSbjg {
  width: 100%;
  height: 58px;
  display: flex;
  align-items: center;
  img {
    width: 88px;
    height: 22px;
    margin: 0;
    margin-left: 39px;
  }
}
.meta-info-container {
  width: 7px;
  height: 20px;
  margin-right: 10px;
  background: #4380ec;
  border-radius: 8px;
}
.flexBox {
  display: flex;
  align-items: center;
}
::v-deep(.el-textarea__inner) {
  border: 1px dashed rgba(67, 128, 236, 1);
  border-radius: 4px;
}
.lyric-action-btn {
  width: 80px;
  height: 30px;
  background: #ffffff;
  border: 1px solid rgba(67, 128, 236, 1);
  border-radius: 15px;
  font-family: SourceHanSansSCVF-Regular;
  font-size: calc(100vw * 16 / 1920);
  color: #4380ec;
  letter-spacing: 0;
  font-weight: 400;
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 10px;
  img {
    width: 14px;
    height: 14px;
    margin-left: 12px;
    margin-right: 3px;
  }
}
.lyric-action-btn:hover {
  box-shadow: 0px 2px 10px 0px rgba(89, 150, 241, 0.27);
}
.lyric-action-btn:last-child {
  margin-left: 10px;
}
.lyricActionBtn {
  width: 80px;
  height: 30px;
  background-image: linear-gradient(270deg, #1cd6b8 0%, #22c1a7 100%);
  box-shadow: 0px 2px 10px 0px rgba(89, 207, 241, 0.27);
  border-radius: 24px;
  font-family: SourceHanSansSCVF-Regular;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  margin-bottom: 10px;
}
.lyricActionBtn:last-child {
  margin-left: 10px;
}
.lyricActionBtn:hover {
  background-image: linear-gradient(270deg, #1cd6b8 0%, #0fb095 100%);
}
.metaItem {
  position: absolute;
  right: 0;
  :v-deep(.el-input__inner) {
    background: #ffffff;
    border: 1px solid rgba(67, 128, 236, 1);
    box-shadow: 0px 2px 10px 0px rgba(89, 150, 241, 0.27);
    border-radius: 24px;
    height: 36px;
    line-height: 36px;
    width: 122px;
  }
}

/* 新增的自定义音频播放器样式 */
.custom-audio-player {
  width: 100%;
  height: calc(100vh * 54 / 1080);
  background: #ffffff;
  border: 1px solid rgba(229, 238, 255, 1);
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.11);
  border-radius: 37px;
  display: flex;
  align-items: center;
  padding: 0 7px;
}

.player-controls {
  display: flex;
  align-items: center;
  width: 100%;
}

.play-btn {
  width: calc(100vw * 40 / 1920);
  height: calc(100vh * 40 / 1080);
  border-radius: 50%;
  background: #ffffff;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;

  i {
    font-size: calc(100vw * 18 / 1920);
    color: #4081ff;
  }

  &:hover {
    background: #dadadb;
  }
}

.progress-container {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  height: 6px;
  margin: 0 10px;
}

.progress-track {
  position: absolute;
  left: 0;
  right: 0;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-filled {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: #4081ff;
  border-radius: 3px;
  width: var(--progress, 0%);
  transition: width 0.1s linear;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  outline: none;
  z-index: 2;
  margin: 0;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4081ff;
    cursor: pointer;
    position: relative;
    z-index: 3;
  }

  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4081ff;
    cursor: pointer;
    border: none;
    position: relative;
    z-index: 3;
  }
}

.time-display {
  font-family: SourceHanSansSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #666;
  min-width: 50px;
  text-align: center;
  margin: 0 5px;
}
.volume-control {
  display: flex;
  align-items: center;
  margin-left: 15px;
  margin-right: 15px;
}

.volume-btn {
  background: none;
  border: none;
  color: #4081ff;
  cursor: pointer;
  font-size: calc(100vw * 18 / 1920);
  margin-right: 10px;
  padding: 5px;

  &:hover {
    color: #3a6bc6;
  }
}

.volume-bar {
  width: 80px;
  height: 4px;
  -webkit-appearance: none;
  appearance: none;
  background: #e0e0e0;
  border-radius: 2px;
  outline: none;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4081ff;
    cursor: pointer;
  }

  &::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4081ff;
    cursor: pointer;
    border: none;
  }

  &::-moz-range-progress {
    background: #4081ff;
    height: 4px;
    border-radius: 2px;
  }
}

.rate-control {
  margin-left: 15px;
  margin-right: 15px;
  position: relative;
}

.rate-btn {
  background: none;
  border: none;
  color: #4081ff;
  cursor: pointer;
  font-size: calc(100vw * 14 / 1920);
  padding: 5px 10px;
  border-radius: 4px;
  min-width: 50px;
  text-align: center;

  &:hover {
    background: #f0f7ff;
  }
}

.active-rate {
  color: #4081ff;
  font-weight: bold;
}
.btn-text {
  margin-right: 25px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 #6478d4;
  border-radius: calc(100vh * 20 / 1080);
  color: #fff;
}
.fir-textarea {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
  border-radius: calc(100vh * 4 / 1080);
  margin: calc(100vh * 20 / 1080);
  margin-bottom: 0;
  height: calc(100vh * 158 / 1080);

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: calc(100vh * 13 / 1080) calc(100vw * 18 / 1920)
      calc(100vh * 33 / 1080) calc(100vw * 16 / 1920);
  }
}

.fir-textarea-max {
  height: 95% !important;
}

::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
  display: none !important;
}

::v-deep(.ql-blank) {
  display: none !important;
}

::v-deep(.ql-container.ql-snow) {
  border: 0;
}
</style>