import axios from 'axios'
import {
  Message
} from 'element-ui'
//  import {
//      getToken,
//      setToken,
//      removeToken
//  } from '../utils/token.js'
// import store from '@/store'
import router from '../router'
var instance = axios.create({
  timeout: 36000000, // request timeout
})
const ok = "10000";
// 添加请求拦截器
// respone interceptor
// request interceptor
instance.interceptors.request.use(
  config => {
    // if (store.state.token) {
    //   config.headers['token'] = `${store.state.token}` // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
    // }
    //console.log(config)
    return config
    /* const token = getToken()
    if (token) {
        config.headers['Authorization'] = token // 让每个请求携带token
    }
   return config */
  },
  error => {
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// respone interceptor
instance.interceptors.response.use(
  response => {
    // let reader = new FileReader()//读取上传的公钥文件
    // reader.readAsText(response.data)
    // reader.onload = function () {
    // 	console.log(this.result) 
    // }

    const responseType = response.config.responseType
    //console.log(responseType)
    if (responseType == 'blob') {
      const content = response.data //返回的内容
      //console.log(response.headers.type)
      if (response.headers.type) {

        let disposition = response.headers["content-disposition"]
        //console.log(disposition);
        let filename = decodeURIComponent(disposition.substring(disposition.indexOf("=") + 1, disposition.length))
        //console.log(filename)
        return { 'blob': content, 'filename': filename }
      }
      return content;
    } else {
      //console.log(response)
      const resp = response.data

      // console.log(JSON.stringify(resp))

      const errCode = resp.code

      if (errCode !== undefined) {
        if (errCode === 10002) {
          // Message({
          //     message: '您还未登录',
          //     type: 'error',
          //     duration: 5 * 1000
          // })
          // resetMessage({
          //   message: '您还未登录',
          //   type: 'error',
          //   duration: 5 * 1000
          // })
          // removeToken()
          // router.push("/login")
          //清除TOKEN
          //调整至登录页面
        }
        return resp;
      } else {
        return response
      }
    }

  },
  error => {
    console.log('err' + error) // for debug
    // Message({
    //     message: error.message,
    //     type: 'error',
    //     duration: 5 * 1000
    // })
    // return Promise.reject(error)
  }
)


let messageInstance = null;

const resetMessage = (options) => {
  if (messageInstance) {
    messageInstance.close()
  }
  messageInstance = Message(options)
}

export const createUploadAPI = (url, method, data) => {
  let config = {}
  config.data = data
  config.headers = {
    'Content-Type': 'multipart/form-data;charset=UTF-8'
  }
  config.responseType = 'json'

  return instance({
    url,
    method,
    ...config
  })
}
export const createAPI = (url, method, data) => {
  let config = {}
  if (method === 'get') {
    config.params = data
  } else {
    config.data = data
  }
  return instance({
    url,
    method,
    ...config
  })
}

export const createDownloadAPI = (url, method, data) => {
  let config = {}
  if (method === 'get') {
    config.params = data
  } else {
    config.data = data
  }
  config.headers = {
    // application/ms-excel;charset=utf-8
    // 'Cache-Control': 'no-cache',
    //'Content-Type': 'application/ms-excel'
  }
  config.responseType = 'blob'
  return instance({
    url,
    method,
    ...config
  })
};


export const createFormAPI = (url, method, data) => {
  let config = {}
  config.data = data
  config.headers = {
    'Cache-Control': 'no-cache',
    'Content-Type': 'application/x-www-form-urlencoded'
  }
  config.responseType = 'json'
  config.transformRequest = [
    function (data) {
      let ret = ''
      for (let it in data) {
        ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
      }
      return ret
    }
  ]
  return instance({
    url,
    method,
    ...config
  })
}
export const createXlsxAPI = (url, method, data) => {
  let config = {}
  if (method === 'get') {
    config.params = data
  } else {
    config.data = data
  }
  config.headers = {
    // application/ms-excel;charset=utf-8
    // 'Cache-Control': 'no-cache',
    //'Content-Type': 'application/ms-excel'
  }
  config.responseType = 'arraybuffer'
  return instance({
    url,
    method,
    ...config
  })
}
// 组织架构导出
export const createFileAPI = (url, method, data) => {
  let config = {}
  config.data = data
  config.headers = {
    'Cache-Control': 'no-cache',
    'Content-Type': 'application/x-www-form-urlencoded'
  }
  config.responseType = 'arraybuffer'
  config.transformRequest = [
    function (data) {
      let ret = ''
      for (let it in data) {
        ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
      }
      return ret
    }
  ]
  return instance({
    url,
    method,
    ...config
  })
}
// 员工导出
export const createDown = (url, method, data) => {
  let config = {}
  if (method === 'get') {
    config.params = data
  } else {
    config.data = data
  }
  config.headers = {
    'Cache-Control': 'no-cache',
    'Content-Type': 'application/x-www-form-urlencoded'
  }
  config.responseType = 'blob'
  return instance({
    url,
    method,
    ...config
  })
}
