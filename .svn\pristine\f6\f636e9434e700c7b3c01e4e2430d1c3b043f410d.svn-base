import Vue from 'vue';
import App from './App.vue';
import router from './router';
import ElementUI from 'element-ui';
import VueClipboard from 'vue-clipboard2';
import store from './store';
import { EventBus } from './eventBus'; // 引入事件总线
import 'element-ui/lib/theme-chalk/index.css';
import '../src/style/common.css';
import './assets/font/font.less';
import './assets/font/font1.less';
import './assets/font/font2.less';
import './assets/font/font3.less';
import './assets/font/font4.less';

// 使用 Element UI 和 VueClipboard 插件
Vue.use(ElementUI);
Vue.use(VueClipboard);
// 将 Vuex store 挂载到 Vue 原型上
Vue.prototype.$store = store;

// 将事件总线挂载到 Vue 原型上
Vue.prototype.$bus = EventBus;

Vue.config.productionTip = false;

// 创建 Vue 实例并挂载
var vm = new Vue({
    router,
    store,
    render: h => h(App),
}).$mount('#app');

console.log('25--', vm.$store);