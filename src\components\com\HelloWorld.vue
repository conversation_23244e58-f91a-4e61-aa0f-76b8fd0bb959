<template>
    <!-- <input type="text"/> -->
</template>

<script>
export default {
  name: 'base<PERSON>abel',
  mounted() {
    // // 代码 1
    // const promise = new Promise((resolve, reject) => {  
    //     // 执行异步操作  
    //     setTimeout(() => {    
    //         reject("失败");  
    //     }, 1000);
    // });
    // console.log(promise);



    // 代码 2
    // console.log(1);
    // new Promise((resolve,reject)=>{
    //     console.log(2);
    //       resolve()
    //     console.log(3);
    // }).then(rew =>{
    //     console.log(4);
    // })
    // console.log(5);


    // 1 2  3 5 4




    // 代码 3

    console.log(1);
    new Promise((resolve,reject) => {
        console.log(2);
        setTimeout(() => {
            console.log(6);
            // 产生一个0到10的随机数
            let num = Math.floor(Math.random() * 11)
            // 如果大于等于5就resolve
            if(num >= 5){
                resolve()
            } else {
                // 否则就reject
                reject()
            }
        },1000);
        console.log(3);
    })
    .then(res => {
        console.log(4);
    })
    .catch(err => {
        console.log(7);
    })


    // 代码 4
    // let num = Math.floor(Math.random() * 11)  // 随机数
    // console.log(num)
    // const ps = new Promise ((resolve,reject) => {
    //     // 如果大于等于5就resolve
    //     if(num >= 5){
    //         resolve(num)
    //     } else {
    //         // 否则就reject
    //         reject(num)
    //     }
    // })

    // console.log(ps)





  }
}
</script>
