/* http://meyerweb.com/eric/tools/css/reset/ */
/* v1.0 | 20080212 */

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
font,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
th {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	font-size: 100%;
	/* height: 100%; */
	background: transparent;
	font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
	/*color: white;*/
	box-sizing: border-box;
	text-decoration: none;

}

html,
body {
	height: 100%;
}

body {
	/* line-height: 1; */
	/* line-height: 24px; */
	background: #f1f3f8;
}

ol,
ul {
	list-style: none;
}

blockquote,
q {
	quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
	content: '';
	content: none;
}

/* remember to define focus styles! */
:focus {
	outline: 0;
}

/* remember to highlight inserts somehow! */
ins {
	text-decoration: none;
}

del {
	text-decoration: line-through;
}

/* tables still need 'cellspacing="0"' in the markup */
table {
	border-collapse: collapse;
	border-spacing: 0;
}

::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

::-webkit-scrollbar-track-piece {
	background-color: rgba(0, 0, 0, 0.2);
	-webkit-border-radius: 6px;
}

::-webkit-scrollbar-thumb:vertical {
	height: 5px;
	background-color: rgba(75, 165, 251, 0.5);
	-webkit-border-radius: 6px;
}

::-webkit-scrollbar-thumb:horizontal {
	width: 5px;
	background-color: rgba(75, 165, 251, 0.5);
	-webkit-border-radius: 6px;
}