<template>
  <div class="custom-steps">
    <ol class="steps-container">
      <li
        v-for="(item, index) in steps"
        :key="index"
        :class="{
          active: currentStep === index + 1,
          completed: currentStep > index + 1,
        }"
      >
        <span class="li-title">{{ item.title }}</span>
        <i
          v-if="index != steps.length - 1"
          class="icon-step icon-step-done"
        ></i>
        <!-- <i
          v-else
          class="icon-step icon-step-process"
        ></i> -->
      </li>
    </ol>
  </div>
</template>

<script>
export default {
  name: "CustomSteps",
  props: {
    steps: {
      type: Array,
      required: true,
      default: () => [],
    },
    currentStep: {
      type: Number,
      required: true,
    },
  },
};
</script>

<style lang="less" scoped>
.custom-steps {
  font-size: 0;
  counter-reset: step;
}

.steps-container {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
}

.steps-container li {
  position: relative;
  flex: 1;
  text-align: center;
  font-size: calc(100vw *  16 / 1920);
  color: #606266;
  line-height: 38px;
  span {
    color: #000000;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  14 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }

  &::before {
    content: counter(step);
    counter-increment: step;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #b5bad0;
    color: #fff;
    font-size: calc(100vw *  12 / 1920);
    line-height: 20px;
    text-align: center;
    margin-right: 10px;
    display: inline-block;
  }

  &.active::before,
  &.completed::before {
    background: #4081ff;
    border: 2px solid rgb(255 255 255 / 48%);
  }

  &.active {
    color: #2e62f5;
  }

  .icon-step {
    display: inline-block;
    width: 60px;
    /* height: 0.2px; */
    /* border-radius: 50%; */
    /* background-color: #ccc; */
    position: absolute;
    top: 50%;
    /* transform: translateY(-50%); */
    right: -38px;
    border: 0.5px dashed #ccc;
    /* left: 0px; */
  }

  .icon-step-done {
    // background-color: #2E62F5;
  }
}
</style>