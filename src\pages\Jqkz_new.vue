<!-- ai听 -->
<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle">
          <!-- <img src="../img/logo05.png" width="100%" alt="" /> -->
          <!-- <img src="../assets/lo1.png" width="100%" alt="" /> -->
        </div>
        <div class="ai-header">
          <div class="ai-bar">
            <div class="ai-left-bar">
              <ul class="ai-left-bar-ul">
                <li @click="clickTopsfw" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />文
                </li>
                <!-- <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
                <li @click="clickTopTy" class="ai-left-bar-li">
                  <img src="../assets/icon15-h.png" alt="" />Ai读
                </li>
                <li class="ai-left-bar-li" @click="clickTopAi">
                  <img src="../assets/icon101.png" alt="" />AI写
                </li>
                <!-- <li @click="clickTopNav" class="ai-left-bar-li">
                  <img src="../assets/icon102-h.png" alt="" />
                  AI校对润色
                </li>
                <li @click="clickTopNav" class="ai-left-bar-li">
                  <img src="../assets/icon12-h.png" alt="" />笔墨文库
                </li> -->
                <li @click="clickTopLg" class="ai-left-bar-li">
                  <img src="../assets/icon13-h.png" alt="" />Ai想
                </li>
                <li class="ai-left-bar-li actived">
                  <img src="../assets/icon14.png" alt="" />Ai听
                </li>
                  <!-- <li @click="clickrw" class="ai-left-bar-li">
                  <img src="../assets/check_blue.png" alt="" />核稿
                </li> -->
                <li @click="clickTophy" class="ai-left-bar-li">
                  <img src="../assets/icon202a.png" alt="" />会议记录
                </li>
                <!-- <li @click="clickTopFz" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />辅助定密
                </li> -->
                <li @click="clickTopbmai" class="ai-left-bar-li">
                  <img src="../assets/icon204a.png" alt="" />助手
                </li>
                <!-- <li @click="clickTopsfw" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />收文
                </li>
               <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
              </ul>
            </div>
            <div class="ai-right-bar">
               <!-- <div class="top-button" @click="clickTopNav">
               
                <img src="../assets/icon16.png" alt="" />
              </div> -->

              <!-- <div class="top-button btn" @click="clickTopNav">
               
                <img src="../assets/icon17.png" alt="" />
              </div> -->
              <el-dropdown :hide-on-click="false" trigger="click">
                <div class="ai-avant btn">
                  <img src="../assets/icon18.png" alt="" />
                  <p>{{ username }}</p>
                </div>
                <el-dropdown-menu
                  slot="dropdown"
                  style="height: 50%; width: 20%; margin-right: -50px"
                >
                  <div class="user-menu-content">
                    <div class="user-info">
                      <div class="avatar-wrapper">
                        <img src="../assets/icon_tx.png" alt="" />
                      </div>
                      <div class="name-wrapper">
                        <div class="name">{{ username }}</div>
                        <div class="id">ID：001</div>
                      </div>
                    </div>
                    <div class="divider"></div>
                    <div class="options">
                      <div
                        rel="opener"
                        class="option"
                        href=""
                        target="_blank"
                        @click="togrzx()"
                      >
                        <div class="icon personal-center"></div>
                        <div class="text">个人中心</div>
                      </div>
                      <div
                        rel="opener"
                        class="option1"
                        href=""
                        target="_blank"
                        @click="toxg()"
                      >
                        <div class="el-icon-edit"></div>
                        <div class="text">修改密码</div>
                      </div>
                      <div
                        rel="opener"
                        class="option"
                        href=""
                        target="_blank"
                        @click="wdwd()"
                      >
                        <div class="icon my-document"></div>
                        <div class="text">我的文档</div>
                      </div>
                      <!-- <a rel="opener" class="option" href="" target="_blank">
                        <div class="icon my-favourite"></div>
                        <div class="text">我的收藏</div>
                      </a> -->
                      <a class="option" @click="logout()">
                        <div class="icon logout"></div>
                        <div class="text">退出登录</div>
                      </a>
                    </div>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </el-header>
      <el-main
        v-loading="loading"
        element-loading-text="文字生成中，请稍后..."
        element-loading-background="rgba(255, 255, 255, 0.7)"
      >
        <div class="LgscCon">
          <div class="fir-title">
            <div style="display: flex">
              <div class="fir-kuai"></div>
              <p class="fir-title-p">
                <span>Ai听</span>
              </p>
            </div>

            <!-- <el-upload
              accept=".mp3,.mp4,.wav,.amr,.wma,.aac,.flac,.m4a,.ogg,.webm,.mpg,.flv,.mkv,.3g2,.avi"
              action="#"
              :show-file-list="false"
              :http-request="uploadFile"
              multiple
              ><el-button size="mini" type="primary">上传音视频</el-button>
            </el-upload> -->
          </div>
          <div class="nrCon">
            <div class="nrConLeft floatLeft">
              <div class="time-selector-section">
                <!-- <MediaTimeSelector
                  :mediaUrl="mediaFile.url"
                  :mediaType="
                    mediaFile.type.startsWith('video') ? 'video' : 'audio'
                  "
                  @media-loaded="onMediaLoaded"
                  @range-change="onRangeChange"
                  ref="audioPlayer"
                /> -->
                <div class="media-time-selector">
                  <div class="media-player-container">
                    <audio
                      ref="audioPlayer"
                      class="media-element"
                      :src="mediaFile.url"
                      @timeupdate="updateCurrentTime"
                      @play="onAudioPlay"
                      @pause="onAudioPause"
                      controls
                    ></audio>
                  </div>
                </div>
              </div>
              <el-upload
                accept=".mp3,.mp4,.wav,.amr,.wma,.aac,.flac,.m4a,.ogg,.webm,.mpg,.flv,.mkv,.3g2,.avi"
                class="upload-demo"
                action="#"
                :show-file-list="false"
                :http-request="uploadFile"
                drag
                multiple
              >
              </el-upload>
            </div>
            <div class="nrConRight floatLeft">
              <div class="nrConLeftBk" v-if="this.qhsb == true">
                <div class="btnqh" v-if="this.qhsb == false">
                  <el-button
                    style="background-color: blue; color: white !important"
                    plain
                    v-if="this.qhsb == false"
                    @click="qhClick(1)"
                    >返回</el-button
                  >
                  <el-button
                    style="background-color: blue; color: white !important"
                    plain
                    v-if="this.qhsb == false"
                    @click="schyjy"
                    >生成会议纪要</el-button
                  >
                </div>
                <!-- <div class="bktitle">识别结果</div> -->
                <div class="bktitle">音频语言</div>
                <div class="bkUl">
                  <el-button class="activeBtn">中文（普通话）</el-button>
                  <!-- <el-button plain>英语</el-button>
                  <el-button plain>英语混合</el-button>
                  <el-button plain class="nomgnl">日语</el-button>
                  <el-button plain>韩语</el-button>
                  <el-button plain>粤语</el-button>
                  <el-button plain>河南话</el-button>
                  <el-button plain class="nomgnl">更多</el-button> -->
                </div>
                <div class="bktitle mtp">
                  出稿类型
                  <!-- <span class="ckyl">查看样例</span> -->
                </div>
                <div class="bkUl">
                  <el-button class="activeBtn">文稿</el-button>
                  <!-- <el-button plain disabled>字幕</el-button> -->
                </div>
                <!-- <div class="bktitle">专业领域</div>
                <div class="bkUl">
                  <el-button plain class="activeBtn">通用</el-button>
                </div> -->
              </div>
              <div class="nrConLeftBk" v-if="this.qhsb == false">
                <div class="bktitle">识别结果</div>
                <!-- 元信息显示 -->
                <div
                  class="meta-info"
                  v-if="metaInfo && (metaInfo.ti || metaInfo.ar)"
                >
                  <div v-if="metaInfo.ti" class="meta-item">
                    <span class="meta-label">标题:</span>
                    <span class="meta-value">{{ metaInfo.ti }}</span>
                  </div>
                  <div v-if="metaInfo.ar" class="meta-item">
                    <span class="meta-label">艺术家:</span>
                    <span class="meta-value">{{ metaInfo.ar }}</span>
                  </div>
                </div>
                <div class="nrS">
                  <!-- {{ text }} -->
                  <!-- <el-input class="put1" type="textarea" :rows="4" v-model="text">{{ text }}</el-input> -->
                  <!-- <vue-editor
                    class="fir-textarea fir-textarea-max"
                    v-model="text"
                    ref="editor"
                  ></vue-editor> -->
                  <div class="lrc-section" v-if="parsedLyrics.length > 0">
                    <div class="lrc-container" ref="lrcContainer">
                      <div
                        v-for="(line, index) in parsedLyrics"
                        :key="index"
                        :class="{ 'active-lyric': activeLyricIndex === index }"
                        class="lyric-line"
                        :ref="`lyricLine_${index}`"
                        @click="handleLyricClick(line.time, index)"
                      >
                        <div class="time-badge">
                          {{ formatTime(line.time) }}
                        </div>
                        <div class="lyric-text">
                          {{ line.text }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="btnqh" v-if="this.qhsb == false">
                <el-button
                  class="activeBtn"
                  v-if="this.qhsb == false"
                  @click="qhClick(1)"
                  >返回</el-button
                >
                <el-button
                  class="activeBtn"
                  v-if="this.qhsb == false"
                  @click="schyjy"
                  >生成会议纪要</el-button
                >
              </div>
              <div class="btnqh" v-if="this.btnxs == true">
                <el-button class="activeBtn" @click="qhClick(2)"
                  >返回</el-button
                >
              </div>
              <div class="nrConRightHyjy">
                <div class="nrConRightHyjyT">
                  <div
                    v-if="hyjyStr == ''"
                    style="
                      font-family: PingFangSC-Regular;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      height: 100%;
                    "
                  >
                    暂无内容
                  </div>
                  <div v-else v-html="hyjyStr"></div>
                </div>
              </div>

              <el-dialog
                title="会议纪要"
                :visible.sync="dialogVisible"
                width="40%"
                :before-close="handleClose"
                v-loading="dialoading"
              >
                <!-- <div class="hc_outer"> -->
                <!--  -->
                <!-- <span v-if="hyjyStr == ''">暂无内容</span> -->
                <!-- <span v-else>{{ hyjyStr }}</span> -->

                <!-- <div class="hc_inner">{{ hyjyStr }}</div>
                </div> -->

                <span slot="footer" class="dialog-footer">
                  <el-button class="activeBtn" @click="dialogVisible = false"
                    >取 消</el-button
                  >
                  <!-- <el-button type="primary" @click="hyjyBtn">生成会议纪要</el-button> -->
                </span>
              </el-dialog>
            </div>
          </div>
        </div>
        <!-- Media Time Selector Component -->
        <!-- <div
          style="
            display: flex;
            position: absolute;
            bottom: 35px;
            left: 50%;
            transform: translateX(-50%);
          "
        >
          <div class="time-selector-section">
            <MediaTimeSelector
              :mediaUrl="mediaFile.url"
              :mediaType="
                mediaFile.type.startsWith('video') ? 'video' : 'audio'
              "
              @media-loaded="onMediaLoaded"
              @range-change="onRangeChange"
              ref="audioPlayer"
            />
          </div>
          <div style="width: 491px">
            <div class="selected-range-info">
              <h3>已选择时间段</h3>
              <div class="range-details">
                <div class="range-item">
                  <span class="label">开始时间:</span>
                  <span class="value">{{
                    formatTime(selectedRange.startTime)
                  }}</span>
                </div>
                <div class="range-item">
                  <span class="label">结束时间:</span>
                  <span class="value">{{
                    formatTime(selectedRange.endTime)
                  }}</span>
                </div>
                <div class="range-item">
                  <span class="label">时长:</span>
                  <span class="value">{{
                    formatTime(selectedRange.duration)
                  }}</span>
                </div>
              </div>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="playSelectedRange">
                <i class="el-icon-video-play"></i> 播放选中片段
              </el-button>
              <el-button type="success" @click="extractSelectedRange">
                <i class="el-icon-scissors"></i> 提取选中片段
              </el-button>
              <el-button type="warning" @click="downloadSelectedSegment">
                <i class="el-icon-download"></i> 下载选中片段
              </el-button>
            </div>
          </div> -->

        <!-- Extracted Media Section -->
        <!-- <div class="extracted-section" v-if="extractedMedia.url">
            <h2>提取结果</h2>
            <div class="extracted-media">
              <div class="extracted-info">
                <span>{{ extractedMedia.name }}</span>
               
              </div>
            </div>
          </div>
        </div> -->
      </el-main>
    </el-container>
  </div>
</template>
<script>
import { VueEditor } from "vue2-editor";
import { getAsr, hyjy, asr_api1 } from "../api/home.js"; // 接口请求
import store from "../store/index";
import MediaTimeSelector from "./MediaTimeSelector.vue";

// import marked from "../api/marked";
export default {
  computed: {
    username() {
      return this.$store.state.username;
    },
  },
  data() {
    return {
      navShow: true,
      btn: "",
      file: "",
      filename: "",
      text: "",
      qhsb: true,
      btnxs: false,
      loading: false,
      dialogVisible: false,
      dialoading: false,
      hyjyStr: "",
      mediaFile: {
        file: null,
        url: "",
        name: "",
        type: "",
      },
      // 选定的时间范围
      selectedRange: {
        startTime: 0,
        endTime: 0,
        duration: 0,
      },
      // 提取的媒体信息
      extractedMedia: {
        url: "",
        name: "",
        blob: null,
      },
      parsedLyrics: [], // 解析后的歌词数据
      currentLyricTime: 0, // 当前歌词时间
      showBilingual: true, // 是否显示双语
      lastActiveIndex: -1, // 上次激活的歌词索引
      isPlaying: false, // 是否正在播放
      lyricUpdateInterval: null, // 歌词更新定时器
      activeLyricIndex: -1, // 当前高亮的歌词行索引
      currentTime: 0, // 当前播放时间
      scrollRequestId: null, // 滚动动画ID
    };
  },
  components: {
    VueEditor,
    MediaTimeSelector,
  },
  mounted() {},
  methods: {
    /**
     * 媒体加载完成处理函数
     */
    onMediaLoaded(data) {
      console.log("媒体加载完成，总时长:", data.duration);
    },
    /**
     * 时间范围变化处理函数
     */
    onRangeChange(range) {
      console.log("时间范围变化:", range);

      this.selectedRange = {
        ...range,
        duration: range.endTime - range.startTime,
      };
    },
    /**
     * 播放选定的时间范围
     */
    playSelectedRange() {
      if (this.$refs.audioPlayer) {
        this.$refs.audioPlayer.playSelectedRange();
      }
    },

    /**
     * 提取选定的时间范围
     */
    extractSelectedRange() {},

    /**
     * 下载提取的媒体
     */
    downloadExtractedMedia() {},

    /**
     * 下载选中片段
     * 使用前端技术实现媒体文件的切片功能
     */
    async downloadSelectedSegment() {},
    /**
     * 格式化时间（将秒转换为 MM:SS 格式）
     */
    formatTime(seconds) {
      if (isNaN(seconds) || seconds < 0) return "00:00";

      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);

      return `${mins.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    },
    clickpb() {
      this.$router.push("/pb_manage");
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    toxg() {
      this.$router.push("/grzx4");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "111111111111111111111111111111111111");
      this.hyjyStr = html;
      // return html;
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickTopAi() {
      this.$router.push("/index");
    },
    clickTopLg() {
      this.$router.push("/lgsc");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    clickTopsfw() {
      this.$router.push("/sfw");
    },
    // beforeAvatarUpload(file) {
    //   console.log(file);
    //   const isMP3 = file.type === "audio/mpeg";
    //   const isMP4 = file.type === "video/mp4";
    //   if (!isMP3 && !isMP4) {
    //     this.$message.error("上传音频、视频只能是 MP3/MP4 格式!");
    //   }
    //   return isMP3 || isMP4;
    // },
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },

    uploadFile(item) {
      console.log(item, "item");

      this.btnxs = false;
      // this.blobToBase64(item.file, (dataurl) => {
      //   this.file = dataurl.split(",")[1];
      //   this.uploadMp(this.file);
      // });
      // 创建URL用于预览
      if (this.mediaFile.url) {
        URL.revokeObjectURL(this.mediaFile.url);
      }
      // 创建对象URL
      const url = URL.createObjectURL(item.file);

      // 设置媒体文件信息
      this.mediaFile = {
        file: item.file,
        url: url,
        name: item.file.name,
        type: item.file.type,
      };

      // 重置提取的媒体
      this.resetExtractedMedia();

      this.uploadMp(item.file);
      this.filename = item.file.name;
    },

    /**
     * 重置提取的媒体
     */
    resetExtractedMedia() {
      if (this.extractedMedia.url) {
        URL.revokeObjectURL(this.extractedMedia.url);
      }

      this.extractedMedia = {
        url: "",
        name: "",
        blob: null,
      };
    },

    async uploadMp(val) {
      // return;
      this.loading = true;
      let fd = new FormData();
      fd.append("file_name", this.filename);
      fd.append("base_string", val);
      fd.append("language", "Chinese");
      let resData = await getAsr(fd);
      console.log(resData);
      // 解析特定格式的LRC
      const parsed = this.parseSpecialLRC(resData.data);
      this.parsedLyrics = parsed.lyrics;
      this.metaInfo = parsed.meta;

      // 将歌词文本合并到编辑器
      this.text = this.parsedLyrics.map((line) => line.text).join("\n");

      // 显示元信息
      if (this.metaInfo.ti) {
        this.$notify({
          title: "音频信息",
          message: `标题: ${this.metaInfo.ti}${
            this.metaInfo.ar ? "\n艺术家: " + this.metaInfo.ar : ""
          }`,
          duration: 5000,
        });
      }

      if (this.text != "" && this.text != undefined) {
        this.qhsb = false;
        this.loading = false;
        // this.hyjyStr = ""
        // this.dialogVisible = true;
      }
    },

    // 处理LRC歌词内容
    parseSpecialLRC(lrcText) {
      const lines = lrcText.split("\n");
      const result = [];
      const metaInfo = {};
      const timeRegex = /\[(\d{2}):(\d{2})\.(\d{2})\]/g;

      // 先提取元信息
      const metaRegex = /^\[(ti|ar|al):(.*)\]$/;
      for (const line of lines) {
        const metaMatch = line.match(metaRegex);
        if (metaMatch) {
          metaInfo[metaMatch[1]] = metaMatch[2].trim();
        }
      }

      // 解析歌词内容
      for (const line of lines) {
        const times = [];
        let text = line.trim();

        // 提取时间标签
        let match;
        while ((match = timeRegex.exec(line)) !== null) {
          const minutes = parseFloat(match[1]);
          const seconds = parseFloat(match[2]);
          const hundredths = parseFloat(match[3]);
          const time = minutes * 60 + seconds + hundredths / 100;
          times.push(time);
          text = text.replace(match[0], "");
        }

        // 处理有效歌词行
        if (times.length > 0 && text.trim()) {
          // 处理可能存在的双语分隔符
          const [mainText, translation] = text.split("|").map((t) => t.trim());

          times.forEach((time) => {
            result.push({
              time,
              text: mainText,
              lang1: mainText,
              lang2: translation || "",
              isSecondary: false,
              isActive: false, // 添加高亮状态属性
            });
          });
        }
      }

      // 按时间排序
      result.sort((a, b) => a.time - b.time);

      return {
        meta: metaInfo,
        lyrics: result,
      };
    },
    // 更新当前播放时间
    updateCurrentTime() {
      if (!this.$refs.audioPlayer) return;

      this.currentTime = this.$refs.audioPlayer.currentTime;
      this.updateActiveLyric();
    },
    // 更新当前高亮歌词
    updateActiveLyric() {
      if (!this.parsedLyrics.length) return;

      // 找到最后一个时间小于当前时间的歌词行
      let activeIndex = -1;
      for (let i = 0; i < this.parsedLyrics.length; i++) {
        if (this.parsedLyrics[i].time <= this.currentTime) {
          activeIndex = i;
        } else {
          break;
        }
      }

      if (activeIndex !== -1 && activeIndex !== this.activeLyricIndex) {
        this.activeLyricIndex = activeIndex;
        this.scrollToLyric(activeIndex);
      }
    },
    // 处理歌词点击
    handleLyricClick(time, index) {
      this.activeLyricIndex = index;
      this.jumpToTime(time);
      this.scrollToLyric(index);
    },

    // 跳转到指定时间
    jumpToTime(time) {
      if (this.$refs.audioPlayer) {
        this.$refs.audioPlayer.currentTime = time;
        this.$refs.audioPlayer.play();
      }
    },

    // 音频播放事件
    onAudioPlay() {
      this.isPlaying = true;
      // 启动歌词更新定时器
      this.lyricUpdateInterval = setInterval(() => {
        this.updateCurrentTime();
      }, 200);
    },

    // 音频暂停事件
    onAudioPause() {
      this.isPlaying = false;
      // 清除定时器
      if (this.lyricUpdateInterval) {
        clearInterval(this.lyricUpdateInterval);
        this.lyricUpdateInterval = null;
      }
    },

    // 滚动到指定歌词行
    scrollToLyric(index) {
      if (this.scrollRequestId) {
        cancelAnimationFrame(this.scrollRequestId);
      }

      const container = this.$refs.lrcContainer;
      const lineElement = this.$refs[`lyricLine_${index}`]?.[0];

      if (!container || !lineElement) return;

      const containerHeight = container.clientHeight;
      const lineTop = lineElement.offsetTop;
      const lineHeight = lineElement.clientHeight;
      const targetScrollTop = lineTop - containerHeight / 2 + lineHeight / 2;

      const startScrollTop = container.scrollTop;
      const distance = targetScrollTop - startScrollTop;
      const duration = 300; // 动画时长(ms)
      let startTime = null;

      const animateScroll = (timestamp) => {
        if (!startTime) startTime = timestamp;
        const elapsed = timestamp - startTime;
        const progress = Math.min(elapsed / duration, 1);

        container.scrollTop = startScrollTop + distance * progress;

        if (progress < 1) {
          this.scrollRequestId = requestAnimationFrame(animateScroll);
        }
      };

      this.scrollRequestId = requestAnimationFrame(animateScroll);
    },

    // 查找当前激活的歌词索引
    findActiveLyricIndex() {
      if (!this.parsedLyrics.length || !this.$refs.audioPlayer) return -1;

      const currentTime = this.currentTime;
      let activeIndex = -1;

      // 查找最后一个时间小于当前时间的歌词
      for (let i = 0; i < this.parsedLyrics.length; i++) {
        if (this.parsedLyrics[i].time <= currentTime) {
          activeIndex = i;
        } else {
          break;
        }
      }

      return activeIndex;
    },
    // 判断当前歌词是否激活
    isLyricActive(time) {
      if (!this.$refs.audioPlayer) return false;
      console.log("当前歌词是否激活", time);

      const currentTime = time;
      const nextTime = this.findNextLyricTime(time);
      console.log(currentTime, nextTime);
      console.log(
        currentTime >= time && (nextTime === null || currentTime < nextTime)
      );
      this.isIFGl =
        currentTime >= time && (nextTime === null || currentTime < nextTime);
    },
    // 查找下一句歌词的时间
    findNextLyricTime(currentTime) {
      for (let i = 0; i < this.parsedLyrics.length; i++) {
        if (this.parsedLyrics[i].time > currentTime) {
          return this.parsedLyrics[i].time;
        }
      }
      return null;
    },
    schyjy() {
      this.hyjyStr = "";
      // this.dialogVisible = true;
      this.hyjyBtn();
    },
    async hyjyBtn() {
      this.dialoading = true;
      this.loading = true;
      let params = {
        text: this.text,
      };
      let resData2 = await hyjy(params);
      // console.log(resData2.data.status_code,'会议假药按钮');
      // if (resData2.status == 200) {
      if (resData2.data.status_code == 200) {
        this.$message({
          message: resData2.data.message,
          type: "success",
        });
        this.dialoading = false;
        this.loading = false;
        // this.hyjyStr = resData2.data;
        this.marked(resData2.data.data);
      } else if (resData2.data.status_code == 500) {
        this.$message({
          message: resData2.data.message,
          type: "error",
        });
      } else if (resData2.data.status_code == 10001) {
        this.$message({
          message: resData2.data.message,
          type: "error",
        });
      } else if (resData2.data.status_code == 10002) {
        this.$message({
          message: resData2.data.message,
          type: "error",
        });
      }
    },
    qhClick(val) {
      if (val == 1) {
        this.qhsb = true;
        this.btnxs = true;
      }
      if (val == 2) {
        this.qhsb = false;
        this.btnxs = false;
      }
    },
  },
  beforeDestroy() {
    // 清理URL对象
    if (this.mediaFile.url) {
      URL.revokeObjectURL(this.mediaFile.url);
    }
    // 清除定时器
    if (this.lyricUpdateInterval) {
      clearInterval(this.lyricUpdateInterval);
    }
    // 取消动画帧
    if (this.scrollRequestId) {
      cancelAnimationFrame(this.scrollRequestId);
    }
  },
};
</script>
<style lang="less" scoped>
.hc_outer {
  margin-left: 20%;
  margin-right: 20%;
}

.hc_inner {
  width: auto;
  height: auto;

  position: absolute;
  align-items: center;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: linear-gradient(
  //   76deg,
  //   #07389c 0%,
  //   #3d86d1 0%,
  //   #3448b3 100%
  // );

  img {
    width: 120px;
    height: 48px;
  }

  p {
    border: 1.54px solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vw * 40 / 1920);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: calc(100vw * 1 / 1920) solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vw * 130 / 1920);
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vw * 16 / 1920);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vw * 14 / 1920);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
                rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }
          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
              rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw *  12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 /1080);
  padding: 20px 10px;
  // overflow: hidden;
}

.LgscCon {
  width: 1500px;
  height: auto;
  margin: 0 auto;

  .button {
    width: 108px;
    height: 58px;
    background: #4c91ff;
    border-radius: 0px 100px 100px 0px;
  }

  img {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }

  p {
    line-height: 58px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw *  14 / 1920);
  letter-spacing: 0;
  line-height: 16px;
  overflow: hidden;
  margin-top: 30px;
  height: 20px;
  display: flex;
  align-items: center;
  // 两端对齐
  justify-content: space-between;

  .fir-kuai {
    width: 6px;
    height: 16px;
    margin-right: 8px;
    float: left;
    // margin-top: 2px;
    background: #4081ff;
    border-radius: 1.5px;
  }

  .fir-title-p {
    width: 100%;
    color: #222;
    font-weight: 500;
    font-size: calc(100vw *  14 / 1920);
    letter-spacing: 0;
    line-height: 16px;
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.floatLeft {
  float: left;
}

.nrCon {
  margin-top: 30px;

  .nrConLeft {
    width: 100%;
    height: auto;
    display: flex;
    align-items: center;
  }

  .nrConRight {
    width: 100%;
    height: auto;
    position: relative;
    display: flex;
    align-items: center;

    .btnqh {
      position: absolute;
      bottom: 35px;
      right: 330px;

      .el-button {
        width: 104px;
        height: 30px;
        line-height: 15px;
        padding: 5px;
        font-size: calc(100vw *  14 / 1920);
        margin-left: 0;
        margin-right: 9px;
        margin-top: 10px;
        font-family: SourceHanSansSC-Medium !important;
      }

      .nomgnl {
        margin: 0;
        margin-top: 10px;
      }
    }
  }
}
.nrConRightHyjy {
  width: 280px;
  height: 600px;
  background: #ffffff;
  box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.13);
  border-radius: 12px;
  padding: 24px;
  .nrConRightHyjyT {
    width: 100%;
    height: 100%;
    background: #f2f4f9;
    overflow-y: scroll;
  }
}

.nrConLeftBk {
  width: 1200px;
  height: 600px;
  background: #ffffff;
  box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.13);
  border-radius: 12px;
  padding: 24px;
  position: relative;
  margin-right: 20px;

  .bktitle {
    height: auto;
    text-align: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
    margin-bottom: 10px;
  }

  .bkUl {
    display: flex;
    flex-wrap: wrap;
    height: 70px;

    .el-button {
      width: 104px;
      height: 30px;
      line-height: 15px;
      padding: 5px;
      font-size: calc(100vw *  14 / 1920);
      margin-left: 0;
      margin-right: 9px;
      margin-top: 10px;
      font-family: SourceHanSansSC-Medium !important;
    }

    .nomgnl {
      margin: 0;
      margin-top: 10px;
    }
  }

  .nrS {
    //
    width: 100%;
    height: 84%;
    // background: #f2f4f9;
    // padding: 20px;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    line-height: 35px;
    font-weight: 400;
    text-align: left;

    // overflow-y: auto;
    /* 添加滚动条 */
    .fir-textarea {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #f8f9fd;
      border: 1px solid rgba(229, 232, 245, 1);
      border-radius: 4px;
      // margin: 20px;
      margin-bottom: 0;
      height: 158px;
      overflow-y: scroll;

      ::v-deep(.el-textarea__inner) {
        font-size: 14px !important;
        background-color: #f8f9fd !important;
        height: 100% !important;
        font-family: PingFangSC-Regular;
        padding: 13px 18px 33px 16px;
        // overflow: auto;
      }
    }

    .fir-textarea-max {
      height: 100% !important;
    }

    ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
      display: none !important;
    }

    ::v-deep(.ql-blank) {
      display: none !important;
    }

    ::v-deep(.ql-editor) {
      // width: 443px;
    }

    ::v-deep(.ql-container.ql-snow) {
      border: 0;
    }
  }
}

::v-deep(.el-button) {
  span {
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw *  14 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
}

.mtp {
  margin-top: 42px;
}

.ckyl {
  font-family: PingFangSC-Medium;
  font-size: calc(100vw *  14 / 1920);
  color: #2c68ff;
  letter-spacing: 0;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

::v-deep(.el-upload-dragger) {
  width: 280px;
  height: 94px;
  background: url(../assets/upload.png) no-repeat center;
  background-size: 60%;
  box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.13);
  margin-bottom: 15px;
  border-radius: 12px;
  border: none;
}

.upload-demo {
  width: 100%;
  height: auto;
  border: none;
}

.nrConRightBk {
  height: 238px;
}

.flexLd {
  display: flex;
  justify-content: space-between;
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw *  12 / 1920);
  right: 10px;
  height: 10px;
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      // margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/
  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.activeBtn {
  // background: #1a66ff;
  //   border: 1px solid #1f52b0;
  //   border-radius: 4px;
  width: calc(100vw * 160 / 1920);
  height: calc(100vh * 40/ 1080);
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw *  16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
}
.time-selector-section {
  margin-bottom: 20px;
}

.selected-range-info {
  margin-bottom: 55px;
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
}

.selected-range-info h3 {
  font-size: calc(100vw *  18 / 1920);
  margin-bottom: 15px;
  color: #303133;
}

.range-details {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.range-item {
  flex: 1;
  min-width: 150px;
}

.range-item .label {
  font-size: calc(100vw *  14 / 1920);
  color: #909399;
  margin-right: 8px;
}

.range-item .value {
  font-size: calc(100vw *  16 / 1920);
  color: #303133;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.extracted-section {
  margin-top: 40px;
  padding: 20px;
  background-color: #f0f9eb;
  border-radius: 8px;
}

.extracted-section h2 {
  font-size: calc(100vw *  22 / 1920);
  color: #67c23a;
  margin-bottom: 20px;
  text-align: center;
}

.extracted-media {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.extracted-media video,
.extracted-media audio {
  width: 100%;
  max-width: 600px;
  margin-bottom: 15px;
}

.extracted-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
}

/* 歌词区域样式 */
/* 元信息样式 */
.meta-info {
  background: #f0f7ff;
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 3px solid #3a8ee6;
}

.meta-item {
  display: flex;
  margin-bottom: 6px;
  font-size: calc(100vw *  14 / 1920);
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-weight: bold;
  color: #555;
  min-width: 60px;
}

.meta-value {
  color: #333;
}

.lrc-section {
  height: 100%;
}

.lrc-container {
  height: 100%;
  overflow-y: auto;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  scroll-behavior: smooth;
}

/* 滚动条样式 */
.lrc-container::-webkit-scrollbar {
  width: 6px;
}

.lrc-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.lrc-container::-webkit-scrollbar-thumb {
  background: #999999;
  border-radius: 3px;
}

.lrc-container::-webkit-scrollbar-thumb:hover {
  background: #f0eeee;
}
/* 歌词行样式 - 添加点击效果 */
.lyric-line {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  margin: 5px 0;
  border-radius: 4px;
  transition: all 0.3s;
  cursor: pointer; /* 添加手型光标 */
}

.lyric-line:hover {
  background: #f5f5f5;
}

.active-lyric {
  background: #f0f7ff;
  border-left: 3px solid #3a8ee6;
  transform: scale(1.02); /* 添加轻微放大效果 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
}

.time-badge {
  min-width: 60px;
  font-size: calc(100vw *  12 / 1920);
  color: #888;
  margin-right: 15px;
}

.lyric-text {
  flex: 1;
  text-align: left;
  line-height: 1.6;
}

/* 导出按钮样式 */
.el-button--mini {
  padding: 5px 10px;
  font-size: calc(100vw *  12 / 1920);
}

.media-time-selector {
  width: 1200px;
  /* height: 447px; */
  margin: 0 auto;
  margin-right: 20px;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.media-player-container {
  width: 100%;
  /* margin-bottom: 20px; */
  border-radius: 4px;
  overflow: hidden;
  /* background-color: #000; */
}

.media-element {
  width: 100%;
  display: block;
}
</style>