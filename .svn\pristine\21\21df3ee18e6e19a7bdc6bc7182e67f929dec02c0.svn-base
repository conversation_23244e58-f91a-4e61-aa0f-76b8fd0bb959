import { createAPI, createUploadAPI, createDownloadAPI } from './request'
var BASE_URL = '/api'
// var BASE_URL2 = '/api2'
// var BASE_URL1 = '/api-v2/'
// 3
export const getSecDtDatas = data => createAPI(BASE_URL + "/receive_string", 'post', data)
// 4.
export const getSuccessInfo = data => createAPI(BASE_URL + "/test_2", 'post', data)
// 5.
export const getSuccessInfoItem = data => createAPI(BASE_URL + "/test_3", 'post', data)
export const getTitle = data => createAPI(BASE_URL + "/get_title_1", 'get', data)
export const getTitle2 = data => createAPI(BASE_URL + "/get_title_2", 'post', data)
export const getBody = data => createAPI(BASE_URL + "/get_body", 'post', data)
export const getAsr = data => createAPI(BASE_URL + "/asr_api", 'post', data)
export const getOcr = data => createAPI(BASE_URL + "/ocr_api", 'post', data)
export const getOcrtxt = data => createAPI(BASE_URL + "/ocr_pic_txt", 'post', data)
export const getLabel1 = data => createAPI(BASE_URL + "/get_label_1", 'get', data)
export const getLabel333 = data => createAPI(BASE_URL + "/get_label_3", 'get', data)
// getLabel1_new
export const getLabel1_new = data => createAPI(BASE_URL + "/refurbish", 'get', data)
export const getLabel2 = data => createAPI(BASE_URL + "/get_label_2", 'post', data)
export const testcheck = data => createAPI(BASE_URL + "/test_check", 'post', data)
// *** 上传word等的接口
export const getAsr1 = data => createAPI(BASE_URL + "/str_dispose/", 'post', data)
//章节速览
export const summary_string = data => createAPI(BASE_URL + "/summary_string", 'post', data)
// 心得体会
export const reflections = data => createAPI(BASE_URL + "/reflections", 'post', data)
// 会议纪要
// export const hyjy = data => createAPI(BASE_URL1 + "/meeting_minute", 'post', data)
export const hyjy = async function (formData, signal) {
    return fetch('http://62.234.166.176:8010/api-v2/meeting_minute', {
        method: 'POST',
        body: formData,
        signal, // 将 signal 传递给 fetch
    })
}
// ai写，领导讲话
export const ldjh = data => createAPI(BASE_URL + "/leader_speech", 'post', data)
// ai写，工作方案
export const gzfa = data => createAPI(BASE_URL + "/work_plan", 'post', data)
// ai写，宣传材料
export const xccl = data => createAPI(BASE_URL + "/promotional_materials", 'post', data)
// ai写，调研报告生成大纲,3
export const dybgscdg = data => createAPI(BASE_URL + "/receive_dybg_string", 'post', data)
// ai写，5.调研报告生成文章
export const dybgscwz = data => createAPI(BASE_URL + "/dybg_string", 'post', data)
// ai写，调研报告，获取调研对象热门推荐
export const getLabel1_dybg = data => createAPI(BASE_URL + "/get_dybg_label_1", 'get', data)
// ai写，调研报告，获取调研热门关键词推荐
export const getLabel2_dybg = data => createAPI(BASE_URL + "/get_dybg_label_2", 'post', data)
// ai写，调研报告，拼接大纲生成所需的数据，4
export const dybg_scdg = data => createAPI(BASE_URL + "/dybg_scdg", 'post', data)
// 登录接口 1234！
// export const login = data => createAPI(BASE_URL + "/layout_03_01/login/login", 'post', data)
// 获取用户信息
export const getUserInfo = data => createAPI(BASE_URL + "/user/getUserInfo", 'get', data)
// 保存文章
export const bcwz = data => createAPI(BASE_URL + "/save_wz", 'post', data)
// 获取文章
export const getwz = data => createAPI(BASE_URL + "/search_wz_all", 'post', data)
// ai读校验word
export const check_word = data => createAPI(BASE_URL + "/word_api", 'post', data)
// ai读下载word
export const download_word = data => createDownloadAPI(BASE_URL + "/download_docx", 'post', data)
// 右键菜单改写文章，1表示继续写 ，2表示扩写 ，3表示缩写 ，4表示润色 
// /rewrite
export const rewrite = data => createAPI(BASE_URL + "/rewrite", 'post', data)
//我的文档下载
export const d_w = data => createDownloadAPI(BASE_URL + "/write_to_word", 'post', data)
// 角色
export const js = data => createAPI(BASE_URL + "/user_role_information", 'post', data)
// 写作接口
export const xz = data => createAPI(BASE_URL + "/user_writing_information", 'post', data)
//词云图接口
// select_user_information
export const cyt = data => createAPI(BASE_URL + "/select_user_information", 'post', data)
// 上传范文接口
export const fwsc = data => createUploadAPI(BASE_URL + "/insert_knowledge_base", 'post', data)
// 我的界面获取人员信息
export const get_user = data => createAPI(BASE_URL + "/select_user_role", 'post', data)
// 我的界面获取写作信息
export const getwrite = data => createAPI(BASE_URL + "/select_user_write", 'post', data)
// 文章风格
export const getlable3 = data => createAPI(BASE_URL + "/get_article_style", 'get', data)
// 句式特点
export const getlable4 = data => createAPI(BASE_URL + "/get_sentence_features", 'get', data)
// 受众群体
export const getlable5 = data => createAPI(BASE_URL + "/get_audience_groups", '', data)
// label2刷新
// api/refurbish2
export const sx2 = data => createAPI(BASE_URL + "/refurbish2", 'post', data)
// refurbish_dybg
// 调研报告第一个刷新
export const dybg_sx1 = data => createAPI(BASE_URL + "/refurbish_dybg", 'get', data)
// refurbish2_dybg
export const dybg_sx2 = data => createAPI(BASE_URL + "/refurbish2_dybg", 'post', data)
// get_login_handle_info
// 管理员获取用户信息
export const get_login_user = data => createAPI(BASE_URL + "/get_login_handle_info", 'get', data)
// 
// updatePassWord
export const xg = data => createAPI(BASE_URL + "/updatePassWord", 'post', data)
// 
// initPassWord，初始化
export const initPassWord = data => createAPI(BASE_URL + "/initPassWord", 'post', data)
// 禁用（批量）
export const disabled = data => createAPI(BASE_URL + "/disabled", 'post', data)
// 启用（批量）
// remove_disabled
export const remove_disabled = data => createAPI(BASE_URL + "/remove_disabled", 'post', data)
// add_Single_User，新增单个用户
export const add_Single_User = data => createAPI(BASE_URL + "/add_Single_User", 'post', data)
// select_handle_all,获取所有用户
export const select_handle_all = data => createAPI(BASE_URL + "/select_handle_all", 'get', data)
// (批量)删除用户
// delete_User
export const delete_User = data => createAPI(BASE_URL + "/delete_User", 'post', data)
// /select meeting，获取会议列表
export const select_meeting = data => createAPI(BASE_URL + "/select_meeting", 'get', data)
// 上传会议文件
// upload meeting
export const upload_meeting = data => createUploadAPI(BASE_URL + "/upload_meeting", 'post', data)
// insert_meeting
export const insert_meeting = data => createUploadAPI(BASE_URL + "/insert_meeting", 'post', data)
// select_meetingById 获取回显pdf
export const select_meetingById = data => createAPI(BASE_URL + "/select_meetingById", 'get', data)
// 下载pdf
// download_pdf_meetingById
export const download_pdf_meetingById = data => createAPI(BASE_URL + "/download_pdf_meetingById", 'get', data)
// 修改
// update_meeting
export const update_meeting = data => createAPI(BASE_URL + "/update_meeting", 'post', data)
// 删除
// delete_meeting
export const delete_meeting = data => createAPI(BASE_URL + "/delete_meeting", 'post', data)
// export_meeting
// 导出会议记录
export const export_meeting = data => createDownloadAPI(BASE_URL + "/export_meeting", 'post', data)
// /update_Single_User
export const update_Single_User = data => createAPI(BASE_URL + "/update_Single_User", 'post', data)
// insert_handle
export const insert_handle = data => createAPI(BASE_URL + "/insert_handle", 'post', data)
export const selectPage_knowledge_base_list = data => createAPI(BASE_URL + "/selectPage_knowledge_base_list", 'post', data)
// delete_knowledge_base_list 删除知识库文件
export const delete_knowledge_base_list = data => createAPI(BASE_URL + "/delete_knowledge_base_list", 'post', data)
// download_knowledge_base_list
// 下载知识库文件
export const download_knowledge_base_list = data => createDownloadAPI(BASE_URL + "/download_knowledge_base_list", 'post', data)
// api/insert_genre，新增文种
export const insert_genre = data => createAPI(BASE_URL + "/insert_genre", 'post', data)
// api/get_genre_list，获取文种列表
export const get_genre_list = data => createAPI(BASE_URL + "/get_genre_list", 'get', data)
// api/get_genre_list，根据文种id查询单个文种
export const get_genre_info = data => createAPI(BASE_URL + "/get_genre_info", 'get', data)
// 删除文种
export const delete_genre = data => createAPI(BASE_URL + "/delete_genre", 'post', data)
// upload_ModelfileTogenre,上传范文
export const upload_ModelfileTogenre = data => createUploadAPI(BASE_URL + "/upload_ModelfileTogenre", 'post', data)
// 上传关联范文
// modelfile_key_information
export const insert_modelfile_key_information = data => createAPI(BASE_URL + "/insert_modelfile_key_information", 'post', data)
// 查询所有文种
// getdocument_type
export const getdocument_type = data => createAPI(BASE_URL + "/getdocument_type", 'get', data)
// 查询所有字体 getfont
export const getfont = data => createAPI(BASE_URL + "/getfont", 'get', data)
// 查询所有字号 getfontsize
export const getfont_size = data => createAPI(BASE_URL + "/getfont_size", 'get', data)
// 查询所有纸张大小
export const getpaper_size = data => createAPI(BASE_URL + "/getpaper_size", 'get', data)
// 查询所有预设项
export const getpreset = data => createAPI(BASE_URL + "/getpreset", 'get', data)
// 新增模板 insert_template
export const insert_template = data => createAPI(BASE_URL + "/insert_template", 'post', data)
// select_templates_BytypeId 根据文种id查询模板
export const select_templates_BytypeId = data => createAPI(BASE_URL + "/select_templates_BytypeId", 'post', data)
// select_template_info 根据模板id查询模板详情 select_template_BytemplateId
export const select_template_BytemplateId = data => createAPI(BASE_URL + "/select_template_BytemplateId", 'get', data)
// upload_check_file 上传文件
export const upload_check_file = data => createUploadAPI(BASE_URL + "/upload_check_file", 'post', data)
// getoutline_Level
export const getoutline_Level = data => createAPI(BASE_URL + "/getoutline_Level", 'get', data)
export const getqtgs = data => createAPI(BASE_URL + "/getqtgs", 'get', data)
export const getqtgs2 = data => createAPI(BASE_URL + "/getqtgs2", 'get', data)

export const getqtdl = data => createAPI(BASE_URL + "/getqtdl", 'get', data)

// 新增核稿记录 insert_check
export const insert_check = data => createAPI(BASE_URL + "/insert_check", 'post', data)
// select_checkBypage 获取核稿列表
export const select_checkBypage = data => createAPI(BASE_URL + "/select_checkBypage", 'get', data)
// 轮询接口 
export const selectPolling = data => createAPI(BASE_URL + "/selectPolling", 'get', data)
// download_checkResult
// 下载核稿结果
export const download_checkResult = data => createDownloadAPI(BASE_URL + "/download_checkResult", 'post', data)
// delete_check 删除、批量删除核稿记录
export const delete_check = data => createAPI(BASE_URL + "/delete_check", 'post', data)
// select_templateBypage 查询模板列表
export const select_templateBypage = data => createAPI(BASE_URL + "/select_templateBypage", 'get', data)
// delete_template 删除模板
export const delete_template = data => createAPI(BASE_URL + "/delete_template", 'post', data)
// ai读下载识别结果
export const download_identify = data => createDownloadAPI(BASE_URL + "/download_identify", 'post', data)
// 根据id查询模板信息 select_template_BytemplateId2
export const select_template_BytemplateId2 = data => createAPI(BASE_URL + "/select_template_BytemplateId2", 'get', data)
// update_template 修改模板
export const update_template = data => createAPI(BASE_URL + "/update_template", 'post', data)
// recommended_outline 推荐大纲
export const recommended_outline = data => createAPI(BASE_URL + "/recommended_outline", 'post', data)
// select_checkById 查看失败原因
export const select_checkById = data => createAPI(BASE_URL + "/select_checkById", 'post', data)
// recheck 重新核稿 
export const recheck = data => createAPI(BASE_URL + "/recheck", 'post', data)
// ******************公文写作模块接口********************************
// 获取文种接口 select_write_type
export const select_write_type = data => createAPI(BASE_URL + "/select_write_type", 'get', data)
// recommend_firstLevel 获取一级推荐
export const recommend_firstLevel = data => createAPI(BASE_URL + "/recommend_firstLevel", 'post', data)
// recommended_outline 生成大纲
// export const recommended_outline = data => createAPI(BASE_URL + "/recommended_outline", 'post', data)
// regenerate_outline_part 重新生成大纲
export const regenerate_outline_part = data => createAPI(BASE_URL + "/regenerate_outline_part", 'post', data)
// add_number_to_outline 大纲分段
export const add_number_to_outline = data => createAPI(BASE_URL + "/add_number_to_outline", 'post', data)
// identify_content_document 上传素材
export const identify_content_document = data => createUploadAPI(BASE_URL + "/identify_content_document", 'post', data)
// add_correspondence 新增对应关系接口
export const add_correspondence = data => createAPI(BASE_URL + "/add_correspondence", 'post', data)
//  上传参考文本 upload_reference_material
export const upload_reference_material = data => createUploadAPI(BASE_URL + "/upload_reference_material", 'post', data)
// generate_article 公文写作生成文章
export const generate_article = data => createAPI(BASE_URL + "/generate_article", 'post', data)
// prepare_write_basic text2等价 
export const prepare_write_basic = data => createAPI(BASE_URL + "/prepare_write_basic", 'post', data)
// get_outline_reference_info 查询素材的匹配记录
export const get_outline_reference_info = data => createAPI(BASE_URL + "/get_outline_reference_info", 'get', data)
// 删除记录 delete_outline_reference_info
export const delete_outline_reference_info = data => createAPI(BASE_URL + "/delete_outline_reference_info", 'post', data)
// 预览记录
// select_outline_reference_info
export const select_outline_reference_info = data => createAPI(BASE_URL + "/select_outline_reference_info", 'post', data)
// 修改记录 update_outline_reference_info
export const update_outline_reference_info = data => createAPI(BASE_URL + "/update_outline_reference_info", 'post', data)
// delete_write_basic_related_information 清除相关信息
export const delete_write_basic_related_information = data => createAPI(BASE_URL + "/delete_write_basic_related_information", 'post', data)
// select_write_referencefiles_Bypage 查询素材
export const select_write_referencefiles_Bypage = data => createAPI(BASE_URL + "/select_write_referencefiles_Bypage", 'get', data)
// delete_write_referencefile 删除素材 json
export const delete_write_referencefile = data => createAPI(BASE_URL + "/delete_write_referencefile", 'post', data)
// select_write_referencefile 根据id查询素材信息
export const select_write_referencefile = data => createAPI(BASE_URL + "/select_write_referencefile", 'post', data)
// *****整理会议材料接口*****
// 获取所有处室 select_handles
export const select_handles = data => createAPI(BASE_URL + "/select_handles", 'get', data)
// 获取处室下的所有人 select_userByhandle_id
export const select_userByhandle_id = data => createAPI(BASE_URL + "/select_userByhandle_id", 'get', data)
// 上传会议文件 upload_material_file
export const upload_material_file = data => createUploadAPI(BASE_URL + "/upload_material_file", 'post', data)
//  新增一条会议记录 insert_meetMaterial
export const insert_meetMaterial = data => createUploadAPI(BASE_URL + "/insert_meetMaterial", 'post', data)
//  分页条件查询会议 select_meetMaterial_taskByPage
export const select_meetMaterial_taskByPage = data => createAPI(BASE_URL + "/select_meetMaterial_taskByPage", 'get', data)
//  delete_meetMaterial_task
export const delete_meetMaterial_task = data => createAPI(BASE_URL + "/delete_meetMaterial_task", 'post', data)
//  select_meetMaterial_task 查看任务详情
export const select_meetMaterial_task = data => createAPI(BASE_URL + "/select_meetMaterial_task", 'get', data)
// preview_file 预览会议文件
export const preview_file = data => createAPI(BASE_URL + "/preview_file", 'get', data)
// download_file 下载会议文件
export const download_file = data => createDownloadAPI(BASE_URL + "/download_file", 'get', data)
// 查询主题下拉 select_materialThemeByMeetId
export const select_materialThemeByMeetId = data => createAPI(BASE_URL + "/select_materialThemeByMeetId", 'get', data)
// identify_file 上传文件并识别结果
export const identify_file = data => createUploadAPI(BASE_URL + "/identify_file", 'post', data)
//  保存修改后的内容 upload_materialByTheme
export const upload_materialByTheme = data => createAPI(BASE_URL + "/upload_materialByTheme", 'post', data)
// extract_aterialByTheme 整理材料 
export const extract_aterialByTheme = data => createAPI(BASE_URL + "/extract_aterialByTheme", 'post', data)
// select_meet_taskRecord 编辑时查看详情
export const select_meet_taskRecord = data => createAPI(BASE_URL + "/select_meet_taskRecord", 'get', data)
// 保存修改 update_meetMaterial
export const update_meetMaterial = data => createAPI(BASE_URL + "/update_meetMaterial", 'post', data)
//  summary_meetMaterial 下载材料整理结果
export const summary_meetMaterial = data => createDownloadAPI(BASE_URL + "/summary_meetMaterial", 'get', data)
// 删除会议主题 delete_meetThemeContent
export const delete_meetThemeContent = data => createAPI(BASE_URL + "/delete_meetThemeContent", 'get', data)
// 结果预览 result_preview
export const result_preview = data => createAPI(BASE_URL + "/result_preview", 'get', data)
// 查看 details_preview
export const details_preview = data => createAPI(BASE_URL + "/details_preview", 'get', data)
// 修改  update_summaryByfileId
export const update_summaryByfileId = data => createAPI(BASE_URL + "/update_summaryByfileId", 'post', data)
//  查看任务情况  log_results
export const log_results = data => createAPI(BASE_URL + "/log_results", 'get', data)
// 删除整理结果 delete_summaryByfileId
export const delete_summaryByfileId = data => createAPI(BASE_URL + "/delete_summaryByfileId", 'get', data)
// 结束编辑 endEditing
export const endEditing = data => createAPI(BASE_URL + "/endEditing", 'get', data)
// downloadByfileId 下载整理结果
export const downloadByfileId = data => createDownloadAPI(BASE_URL + "/downloadByfileId", 'get', data)
//  startEditing 开始编辑
export const startEditing = data => createAPI(BASE_URL + "/startEditing", 'get', data)
//  反馈查询 select_check_result
export const select_check_result = data => createAPI(BASE_URL + "/select_check_result", 'get', data)
//  新增错别字 insert_check_result
export const insert_check_result = data => createAPI(BASE_URL + "/insert_check_result", 'post', data)
// 删除错别字 delete_check_result
export const delete_check_result = data => createAPI(BASE_URL + "/delete_check_result", 'get', data)
// 修改错别字 update_check_result
export const update_check_result = data => createAPI(BASE_URL + "/update_check_result", 'post', data)
// 开始训练接口
export const start_train = data => createAPI(BASE_URL + "/start_train", 'get', data)
// 查询修订接口 select_comments
export const select_comments = data => createAPI(BASE_URL + "/select_comments", 'get', data)
// save_comments 保存修订接口
export const save_comments = data => createAPI(BASE_URL + "/save_comments", 'post', data)
//  select_comments_logs  查询历史记录
export const select_comments_logs = data => createAPI(BASE_URL + "/select_comments_logs", 'get', data)
//  select_comments_bycommentId 查询修改内容
export const select_comments_bycommentId = data => createAPI(BASE_URL + "/select_comments_bycommentId", 'get', data)
// 发布接口 release_check
export const release_check = data => createAPI(BASE_URL + "/release_check", 'post', data)
// 修订打开文件接口  selectFilePath
export const selectFilePath = data => createAPI(BASE_URL + "/selectFilePath", 'get', data)
// 下载文档  downloadReviseFile
export const downloadReviseFile = data => createDownloadAPI(BASE_URL + "/downloadReviseFile", 'get', data)
// 上传音视频 
export const asr_api1 = data => createDownloadAPI(BASE_URL + "/asr_api1", 'post', data)
// 润色接口
export const asr_rewrite = data => createAPI(BASE_URL + "/asr_rewrite", 'post', data)
export const insert_voice = data => createAPI(BASE_URL + "/insert_voice", 'post', data)
export const listen_upload_meeting = data => createAPI(BASE_URL + "/listen_upload_meeting", 'post', data)

// 一键排版模板查询接口
export const getByPage = data => createAPI(BASE_URL + "/getCheckTemplateByPage", 'get', data)

// 获取术语类别接口
export const selectTermtype = data => createAPI(BASE_URL + "/selectTermtype", 'get', data)
// 分页查询术语接口
export const selectWordsByPage = data => createAPI(BASE_URL + "/selectWordsByPage", 'post', data)
// 新增术语接口
export const insertTermtype = data => createAPI(BASE_URL + "/insertTermtype", 'post', data)
// 删除术语接口
export const deleteTermtype = data => createAPI(BASE_URL + "/deleteTermWords", 'post', data)
// 修改术语接口·
export const updateTermWords = data => createAPI(BASE_URL + "/updateTermWords", 'post', data)
// 对话接口
export const llmChat = async function (formData, signal) {
    return fetch('http://62.234.166.176:8010/api/v1/llm_chat/llm_chat_web', {
        // return fetch('/api2/api/v1/llm_chat/llm_chat_web', {
        method: 'POST',
        body: formData,
        signal, // 将 signal 传递给 fetch
    })
}
// 选择知识库后调用的对话接口
export const kbChat = async function (formData, signal) {
    return fetch('http://62.234.166.176:8010/api/v1/kb_chat/kb_chat_web', {
        method: 'POST',
        body: formData,
        signal, // 将 signal 传递给 fetch
    })
}
// 知识库列表获取接口
export const getKbList = async function (formData) {
    return fetch('http://62.234.166.176:8010/api/v1/llm_chat/get_kblist_web', {
        method: 'POST',
        body: formData,
    })
}
// 清空历史对话接口
export const delete_his = async function (formData) {
    return fetch('http://62.234.166.176:8010/api/v1/llm_chat/delete_history_web', {
        method: 'POST',
        body: formData,
    })
}

// 获取引用的接口
export const getChatMsg = async function (formData) {
    return fetch('http://62.234.166.176:8010/api/v1/llm_chat/getChatMsg', {
        method: 'POST',
        body: formData,
    })
}

export const upfile = data => createAPI(BASE_URL + '/diff_file', 'post', data)
// 纪检监察接口
export const start_log = async function (formData) {
    return fetch('http://192.168.1.120:8010/api/v1/llm_chat/start_log', {
        method: 'POST',
        body: formData,
    })
}
export const stop_log = async function (formData) {
    return fetch('http://192.168.1.120:8010/api/v1/llm_chat/stop_log', {
        method: 'POST',
        body: formData,
    })
}
// 查询剩余使用时间接口 
export const get_time = data => createAPI(BASE_URL + "/get_time", 'post', data)
// 登录接口  
export const login = data => createAPI(BASE_URL + "/layout_03_01/login/login", 'post', data)
// 知识库管理下载模板
export const excel_download = data => createDownloadAPI(BASE_URL + '/download_template', 'get', data)
// 获取翻译语言类型接口
export const getTranslateType = data => createAPI(BASE_URL + '/getTranslateType', 'get', data)
// 翻译接口
export const translate_article = data => createAPI(BASE_URL + '/translate_article', 'post', data)
// ai听上传文件识别接口
export const up_shibie= data => createUploadAPI(BASE_URL + "/meeting_file ", 'post', data)

