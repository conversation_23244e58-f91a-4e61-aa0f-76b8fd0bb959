<template>
  <!-- 模板管理 -->
  <div class="work-container" v-loading="con_loading">
    <el-container>
      <div
        :style="
          navShow
            ? 'width: -calc(100vw * 10 / 1920)'
            : 'width: calc(100vw * 10 / 1920)'
        "
        class="ejdhl"
      >
        <div
          class="ai-nav"
          :style="
            navShow
              ? 'width: calc(100vw * 300 / 1920)'
              : 'width: calc(100vw * 64 / 1920)'
          "
        >
          <div class="nav-list">
            <div
              @click="clickgrzx()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon204a.png" alt="" />
              <p v-if="navShow">我的信息</p>
            </div>

            <div
              @click="clickzhgl()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon201a.png" alt="" />
              <p v-if="navShow">账户管理</p>
            </div>
            <div
              @click="clickgosygl()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/术语.png" alt="" />
              <p v-if="navShow">术语管理</p>
            </div>
            <div
              @click="clickmb()"
              class="nav-item choose"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon-jj-hover.png" alt="" />

              <p v-if="navShow">模板管理</p>
            </div>
            <!-- <div
              @click="clickpb()"
              class="nav-item"
              :style="navShow ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'"
            >
              <img src="../assets/icon-jj.png" alt="" />
              <p v-if="navShow">排板管理</p>
            </div> -->
            <!-- <div
              @click="clickrw()"
              class="nav-item"
              :style="navShow ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'"
            >
              <img src="../assets/check.png" alt="" />
              <p v-if="navShow"> 核稿管理</p>
            </div> -->
            <div
              @click="clickzskgl()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon202a.png" alt="" />
              <p v-if="navShow">范文管理</p>
            </div>
            <div
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
              @click="clickTopLd()"
            >
              <img src="../assets/icon17.png" alt="" />
              <p v-if="navShow">返回系统</p>
            </div>
            <!-- <div
              @click="clickszbg()"
              class="nav-item"
              :style="navShow ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'"
            >
              <img src="../assets/icon202a.png" alt="" />
              <p v-if="navShow">写作</p>
            </div> -->
          </div>
        </div>
      </div>
      <el-header class="el-header1">
        <div class="ai-header">
          <div class="flex">
            <div class="ai-left-bar-li actived"></div>
          </div>
        </div>
      </el-header>
      <el-main style="transition: ease-out 0.4s; display: flex">
        <div
          style="
            margin-top: calc(100vh * 15 / 1080);
            height: calc(100vh * 944 / 1080);
            width: 100%;
          "
        >
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-left: calc(100vw * 35 / 1920);
              height: calc(100vh * 35 / 1080);
            "
          >
            <div>
              <button
                @click="adddia = true"
                data-v-819514c6=""
                aria-disabled="false"
                type="button"
                class="el-button el-button--primary el-button--default"
              >
                <i class="el-icon"
                  ><svg
                    viewBox="0 0 1024 1024"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill="currentColor"
                      d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                    ></path></svg></i
                ><span class="">新建模板</span>
              </button>

              <button
                @click="batchDelete"
                data-v-5317e0d1=""
                aria-disabled="false"
                type="button"
                class="el-button el-button--danger el-button--default"
              >
                <i class="el-icon"
                  ><svg
                    viewBox="0 0 1024 1024"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill="currentColor"
                      d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                    ></path></svg></i
                ><span class="">批量删除</span>
              </button>
            </div>
            <div
              data-v-3685a8a8=""
              class="el-input el-input--default el-input-group el-input-group--append el-input--suffix w-50 m-2"
              style="width: calc(100vw * 300 / 1920)"
            >
              <!-- input --><!-- prepend slot --><!--v-if-->
              <div tabindex="-1">
                <!-- prefix slot --><!--v-if-->
                <input
                  class="el-input__inner"
                  type="text"
                  autocomplete="off"
                  tabindex="0"
                  placeholder="请输入模板名"
                  id="el-id-4263-16"
                  v-model="inputvalyhm"
                />
                <span
                  v-if="inputvalyhm != ''"
                  class="el-icon-circle-close"
                  @click="clearInput"
                  style="
                    cursor: pointer;
                    position: absolute;
                    left: calc(100% - 80px);
                    top: 50%;
                    transform: translateY(-50%);
                  "
                >
                </span>
              </div>
              <!-- <div  @click:sea(this.inputvalyhm)>查询</div> -->
              <!-- append slot -->
              <div class="el-input-group__append" @click="sea(inputvalyhm)">
                <div class="el-icon-search"></div>
              </div>
            </div>
          </div>
          <div
            style="
              margin-left: calc(100vw * 35 / 1920);
              margin-top: calc(100vh * 10 / 1080);
              height: calc(100vh * 850 / 1080);
              width: calc(100% - calc(100vw * 35 / 1920)); /* 减去左右边距 */
              overflow: hidden; /* 防止内容溢出 */
            "
          >
            <div style="height: 100%; display: flex; flex-direction: column">
              <el-table
                stripe
                border
                @selection-change="handleSelectionChange"
                :default-sort="{ prop: 'date', order: 'descending' }"
                style="width: 100%"
                height="calc(100vh * 850 / 1080)"
                :data="this.tableData1"
                :header-cell-style="{
                  background: '#ebf2fb',
                }"
              >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column label="序号" sortable type="index" width="80">
                </el-table-column>
                <el-table-column prop="template_name" sortable label="模板名">
                </el-table-column>
                <el-table-column prop="type_name" sortable label="文种名">
                </el-table-column>
                <el-table-column prop="cjr_name" sortable label="创建人">
                </el-table-column>
                <el-table-column prop="cjsj" sortable label="创建时间">
                </el-table-column>
                <el-table-column prop="gxsj" sortable label="最后修改时间">
                </el-table-column>

                <el-table-column
                  prop=""
                  label="操作"
                  min-width="150"
                  align="center"
                >
                  <template v-slot="aa">
                    <el-button
                      style="margin-right: 0px"
                      type="text"
                      size="medium"
                      @click="
                        look(aa.row.template_id), (dialogVisiblelook = true)
                      "
                      >查看</el-button
                    >
                    <el-button
                      style="margin-right: 0px"
                      type="text"
                      size="medium"
                      @click="getch(aa.row), (dialogVisibleedit = true)"
                      >修改</el-button
                    >
                    <el-button
                      style="margin-right: 10px; color: red"
                      type="text"
                      size="medium"
                      @click="showdia3(aa.row)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-pagination
              style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="this.total"
            >
            </el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>

    <el-dialog
      title="新增模板"
      :visible.sync="adddia"
      @close="handleCloseadd"
      width="55%"
    >
      <el-form ref="userForm" :model="form" :label-width="formLabelWidth">
        <el-form-item label="模板名(必填)" prop="userName">
          <el-input
            v-model="form.userName"
            placeholder="请输入模板名"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="文种名(必选)">
          <el-select v-model="form.re1" placeholder="请选择文种">
            <el-option
              v-for="option in options"
              :key="option.type_id"
              :label="option.type_name"
              :value="option.type_id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文字格式设置">
          <div>
            <el-table :data="tableData">
              <el-table-column prop="region" label="大纲级别">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.region" placeholder="大纲级别">
                    <el-option
                      v-for="option in optionsle"
                      :key="option.outline_Level_id"
                      :label="option.outline_Level_name"
                      :value="option.outline_Level_id"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="字体设置">
                <template slot-scope="scope">
                  <div style="position: relative; width: 100%">
                    <!-- 字体选择 -->
                    <el-select
                      v-model="scope.row.font"
                      placeholder="选择字体"
                      clearable
                      filterable
                      width="100%"
                    >
                      <el-option
                        v-for="option in options3"
                        :key="option.font_id"
                        :label="option.font_name"
                        :value="option.font_id"
                      >
                      </el-option>
                    </el-select>

                    <!-- 字号选择和输入 -->
                    <div style="position: relative">
                      <el-select
                        v-model="scope.row.fontSize"
                        placeholder="选择字号"
                        filterable
                      >
                        <el-option
                          v-for="option in optionssi"
                          :key="option.font_size_name"
                          :label="option.font_size_name"
                          :value="option.font_size_name"
                        >
                        </el-option>
                      </el-select>
                      <!-- <el-input
                        v-model="scope.row.fontSize"
                        placeholder="手动输入字号"
                        clearable
                        @input="handleInputChange(scope.row)"
                      /> -->
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="checkList" label="其他字体设置">
                <template slot-scope="scope">
                  <!-- <el-checkbox-group v-model="scope.row.checkList"> -->
                  <el-checkbox-group
                    v-model="scope.row.checkList"
                    @change="handleCheckboxChange(scope.row)"
                  >
                    <el-checkbox
                      v-for="city in others"
                      :label="city"
                      :key="city"
                      >{{ city }}</el-checkbox
                    >
                  </el-checkbox-group>
                </template>
              </el-table-column>
              <el-table-column
                prop="lineSpace"
                label="段落设置(单位：磅)"
                min-width="100"
              >
                <template slot-scope="scope">
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      margin-bottom: calc(100vh * 5 / 1080);
                    "
                  >
                    <span style="margin-right: calc(100vw * 14 / 1920)"
                      >行距：</span
                    >
                    <el-input-number
                      size="mini"
                      v-model="scope.row.lineSpace"
                    ></el-input-number>
                  </div>

                  <div
                    style="
                      display: flex;
                      align-items: center;
                      margin-bottom: calc(100vh * 5 / 1080);
                    "
                  >
                    <span>段前距：</span>
                    <el-input-number
                      size="mini"
                      v-model="scope.row.spaceBefore"
                    ></el-input-number>
                  </div>

                  <div style="display: flex; align-items: center">
                    <span>段后距：</span>
                    <el-input-number
                      size="mini"
                      v-model="scope.row.spaceAfter"
                    ></el-input-number>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="checkList_dl"
                label="其他段落设置"
                width="120"
              >
                <template slot-scope="scope">
                  <!-- <el-color-picker v-model="scope.row.color"></el-color-picker> -->
                  <el-checkbox-group v-model="scope.row.checkList_dl">
                    <el-checkbox
                      v-for="city in others_dl"
                      :label="city"
                      :key="city"
                      >{{ city }}</el-checkbox
                    >
                  </el-checkbox-group>
                </template>
              </el-table-column>
              <el-table-column label="" width="90">
                <template v-slot="scope">
                  <!-- <el-button  type="text" @click="addRow()">+</el-button>
            <el-button  type="text" @click="removeRow(scope.$index)" v-if="tableData.length > 1">-</el-button> -->
                  <el-button
                    type="text"
                    @click="addRow()"
                    class="el-icon-circle-plus-outline"
                    style="font-size: calc(100vw * 24 / 1920)"
                  ></el-button>
                  <el-button
                    type="text"
                    @click="removeRow(scope.$index)"
                    v-if="tableData.length > 1"
                    class="el-icon-remove-outline"
                    style="font-size: calc(100vw * 24 / 1920)"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <!-- 其他部分保持不变 -->
        <el-form-item label="页面大小">
          <el-select placeholder="请选择页面大小" v-model="form.page_size">
            <el-option
              v-for="option in options1"
              :key="option.font_paper_size_id"
              :label="option.font_paper_size_name"
              :value="option.font_paper_size_id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="页边距设置(单位:cm)">
          <div
            style="
              display: flex;
              align-items: center;
              font-size: calc(100vw * 14 / 1920);
            "
          >
            <div style="margin-right: calc(100vw * 16 / 1920)">
              上边距：<el-input-number
                size="mini"
                v-model="num1"
              ></el-input-number>
            </div>
            <div style="margin-right: calc(100vw * 16 / 1920)">
              下边距：<el-input-number
                size="mini"
                v-model="num2"
              ></el-input-number>
            </div>
            <div style="margin-right: calc(100vw * 16 / 1920)">
              左边距：<el-input-number
                size="mini"
                v-model="num3"
              ></el-input-number>
            </div>
            <div>
              右边距：<el-input-number
                size="mini"
                v-model="num4"
              ></el-input-number>
            </div>
          </div>
        </el-form-item>

        <!-- <el-form-item>
  <div style="display: flex; align-items: center;"> 
    <span style="margin-left: 90px;margin-right: 120px;font-weight: 700;">其他设置</span> 
    <el-checkbox-group v-model="checkedCities">
      <el-checkbox v-for="city in options2" :label="city" :key="city">{{ city }}</el-checkbox>
    </el-checkbox-group>
  </div>
</el-form-item> -->
        <el-form-item label="其他设置">
          <div style="display: flex; align-items: center">
            <!-- 标签 -->
            <el-checkbox-group v-model="checkedCities">
              <el-tooltip
                v-for="city in options2"
                :key="city.preset_value"
                effect="dark"
                :content="city.preset_desc"
                placement="top"
              >
                <el-checkbox :label="city.preset_value">{{
                  city.preset_value
                }}</el-checkbox>
              </el-tooltip>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            adddia = false;
            resetForm();
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="add_mb()">保 存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改模板" :visible.sync="dialogVisibleedit" width="55%">
      <el-form :model="form1" :label-width="formLabelWidth">
        <el-form-item label="模板名" prop="userName">
          <el-input
            v-model="form1.userName"
            placeholder="请输入模板名"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="文种选择">
          <el-select v-model="form1.re1" placeholder="请选择文种">
            <el-option
              v-for="option in options"
              :key="option.type_id"
              :label="option.type_name"
              :value="option.type_id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文字格式设置">
          <div>
            <el-table :data="tableDatach">
              <el-table-column prop="region" label="大纲级别">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.region" placeholder="大纲级别">
                    <el-option
                      v-for="option in optionsle"
                      :key="option.outline_Level_id"
                      :label="option.outline_Level_name"
                      :value="option.outline_Level_id"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="字体设置">
                <template slot-scope="scope">
                  <div style="position: relative; width: 100%">
                    <!-- 字体选择 -->
                    <el-select
                      v-model="scope.row.font"
                      placeholder="选择字体"
                      clearable
                      filterable
                      style="width: 100%"
                    >
                      <el-option
                        v-for="option in options3"
                        :key="option.font_id"
                        :label="option.font_name"
                        :value="option.font_id"
                      >
                      </el-option>
                    </el-select>

                    <!-- 字号选择和输入 -->
                    <div style="position: relative">
                      <el-select
                        v-model="scope.row.fontSize"
                        placeholder="选择字号"
                        filterable
                      >
                        <el-option
                          v-for="option in optionssi"
                          :key="option.font_size_name"
                          :label="option.font_size_name"
                          :value="option.font_size_name"
                        >
                        </el-option>
                      </el-select>
                      <!-- <el-input
                        v-model="scope.row.fontSize"
                        placeholder="手动输入字号"
                        style="
                          position: absolute;
                          top: 0;
                          left: 69%;
                          width: 60%;
                          z-index: 1;
                        "
                        clearable
                        @input="handleInputChange(scope.row)"
                      /> -->
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="checkList" label="其他字体设置">
                <template slot-scope="scope">
                  <!-- <el-color-picker v-model="scope.row.color"></el-color-picker> -->
                  <el-checkbox-group
                    v-model="scope.row.checkList"
                    @change="handleCheckboxChange(scope.row)"
                  >
                    <el-checkbox
                      v-for="city in others"
                      :label="city"
                      :key="city"
                      >{{ city }}</el-checkbox
                    >
                  </el-checkbox-group>
                </template>
              </el-table-column>
              <el-table-column label="段落设置(单位：磅)" min-width="100">
                <template slot-scope="scope">
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      margin-bottom: calc(100vh * 5 / 1080);
                    "
                  >
                    <span style="margin-right: 10px">行 距</span>
                    <el-input-number
                      size="mini"
                      v-model="scope.row.lineSpace"
                    ></el-input-number>
                  </div>

                  <div
                    style="
                      display: flex;
                      align-items: center;
                      margin-bottom: calc(100vh * 5 / 1080);
                    "
                  >
                    <span>段前距：</span>
                    <el-input-number
                      size="mini"
                      v-model="scope.row.spaceBefore"
                    ></el-input-number>
                  </div>

                  <div style="display: flex; align-items: center">
                    <span>段后距：</span>
                    <el-input-number
                      size="mini"
                      v-model="scope.row.spaceAfter"
                    ></el-input-number>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="checkList_dl"
                label="其他段落设置"
                width="120"
              >
                <template slot-scope="scope">
                  <el-checkbox-group v-model="scope.row.checkList_dl">
                    <el-checkbox
                      v-for="city in others_dl"
                      :label="city"
                      :key="city"
                      >{{ city }}</el-checkbox
                    >
                  </el-checkbox-group>
                </template>
              </el-table-column>

              <el-table-column label="" width="90">
                <template v-slot="scope">
                  <el-button
                    type="text"
                    @click="addRowch()"
                    class="el-icon-circle-plus-outline"
                    style="font-size: calc(100vw * 24 / 1920)"
                  ></el-button>
                  <el-button
                    type="text"
                    @click="removeRowch(scope.$index)"
                    v-if="tableDatach.length > 1"
                    class="el-icon-remove-outline"
                    style="font-size: calc(100vw * 24 / 1920)"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <!-- 其他部分保持不变 -->
        <el-form-item label="页面大小">
          <el-select placeholder="请选择页面大小" v-model="form1.page_size">
            <el-option
              v-for="option in options1"
              :key="option.font_paper_size_id"
              :label="option.font_paper_size_name"
              :value="option.font_paper_size_id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="页边距设置(单位:cm)">
          <div
            style="
              display: flex;
              align-items: center;
              font-size: calc(100vw * 14 / 1920);
            "
          >
            <div style="margin-right: calc(100vw * 16 / 1920)">
              上边距：<el-input-number
                size="mini"
                v-model="num1ch"
              ></el-input-number>
            </div>
            <div style="margin-right: calc(100vw * 16 / 1920)">
              下边距：<el-input-number
                size="mini"
                v-model="num2ch"
              ></el-input-number>
            </div>
            <div style="margin-right: calc(100vw * 16 / 1920)">
              左边距：<el-input-number
                size="mini"
                v-model="num3ch"
              ></el-input-number>
            </div>
            <div>
              右边距：<el-input-number
                size="mini"
                v-model="num4ch"
              ></el-input-number>
            </div>
          </div>
        </el-form-item>

        <!-- <el-form-item>
  <div style="display: flex; align-items: center;"> 
    <span style="margin-left: 90px;margin-right: 120px;font-weight: 700;">其他设置</span> 
    <el-checkbox-group v-model="checkedCitiesch">
      <el-checkbox v-for="city in options2_change" :label="city" :key="city">{{ city }}</el-checkbox>
    </el-checkbox-group>
  </div>
</el-form-item> -->
        <el-form-item label="其他设置">
          <div style="display: flex; align-items: center">
            <!-- 标签 -->
            <el-checkbox-group v-model="checkedCitiesch">
              <el-tooltip
                v-for="city in options2"
                :key="city.preset_value"
                effect="dark"
                :content="city.preset_desc"
                placement="top"
              >
                <el-checkbox :label="city.preset_value">{{
                  city.preset_value
                }}</el-checkbox>
              </el-tooltip>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="(dialogVisibleedit = false), resetFormch()"
          >取 消</el-button
        >
        <el-button type="primary" @click="change_mb()">保 存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="校验项详情" :visible.sync="dialogVisiblelook">
      <el-descriptions title="">
        <el-descriptions-item
          label="文字格式校验："
          style="margin-right: 10px"
          :label-style="{
            fontWeight: 'bold',
            fontSize: '15px',
            color: '#409EFF',
          }"
        >
          <div>
            <div
              v-for="item in characterslook"
              :key="item.text_color"
              style="
                margin-bottom: 10px;
                border: 1px solid #ccc;
                padding: 5px;
                border-radius: 4px;
              "
            >
              {{ item.description }}
            </div>
          </div>
        </el-descriptions-item>

        <el-descriptions-item
          label="页面格式校验："
          style="margin-right: 10px"
          :label-style="{
            fontWeight: 'bold',
            fontSize: '15px',
            color: '#409EFF',
          }"
        >
          <!-- {{this.page_format}} -->
          <div style="margin-top: 5px">{{ this.page_formatlook }}</div>
        </el-descriptions-item>
        <!-- <el-descriptions-item label="其他项目校验：">
      <div style="margin-top: 5px;">{{this.qtys}}</div>
    </el-descriptions-item> -->
        <el-descriptions-item
          label="其他项目校验："
          :label-style="{
            fontWeight: 'bold',
            fontSize: '15px',
            color: '#409EFF',
          }"
        >
          <div style="margin-top: 5px">
            <div v-for="item in qtyslook" :key="item.name">
              {{ item.name }}: {{ item.value }}
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>
<script>
import App from "../App";
import Wordcloud from "../components/Wordcloud.vue";
import { VueEditor } from "vue2-editor";
import CustomSteps from "../components/Step.vue";
//   import store from '../store/index'
import { mapState } from "vuex";
// ****************************
import Quill from "quill";
let BlockEmbed = Quill.import("blots/block/embed");
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute(
      "style",
      "padding: 20px; border: 1px dashed #488aff; color:red; position: relative;"
    );
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement("button");
    insertButton.innerHTML = "应用";
    insertButton.className = "btn-insert";
    insertButton.style.position = "absolute";
    // 按钮颜色
    insertButton.style.backgroundColor = "#488aff";
    // 字体为白色
    insertButton.style.color = "white";
    // insertButton.style.top = '10px';
    insertButton.style.right = "70px";
    insertButton.addEventListener("click", () => {
      // console.log(value.editorClass, '编辑器')
      // console.log(value.newText, '续写内容！')
      // // 这里的位置，是替换的传position，是插入的传position加length
      // console.log(value.Position, '位置')
      // console.log(value.selectedTextlength, '长度？？？？？？？？')
      // console.log(value.editorClass.getSelection(), '光标位置？？？？')
      if (value.flag == 1) {
        value.editorClass.insertText(
          value.Position + value.selectedTextlength,
          value.newText
        );
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement("button");
    cancelButton1.innerHTML = "取消";
    cancelButton1.className = "btn-cancel";
    cancelButton1.style.position = "absolute";
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = "#e64721";
    // 字体为白色
    cancelButton1.style.color = "white";
    cancelButton1.style.right = "10px";
    cancelButton1.addEventListener("click", () => {
      node.remove();
      // alert('取消按钮点击');
      // // 这里可以添加取消按钮的逻辑
    });
    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = "customBlock";
CustomBlock.tagName = "div";

Quill.register({
  "formats/customBlock": CustomBlock,
});
import store from "../store/index";
import {
  insert_handle,
  update_Single_User,
  select_handle_all,
  add_Single_User,
  js,
  fwsc,
  getSecDtDatas,
  getSuccessInfo,
  getSuccessInfoItem,
  getLabel1,
  getLabel2,
  testcheck,
  bcwz,
  rewrite,
  xz,
  cyt,
  get_user,
  getwrite,
  get_login_user,
  initPassWord,
  disabled,
  remove_disabled,
  delete_User,
  getdocument_type,
  getfont,
  getfont_size,
  getpaper_size,
  getpreset,
  getoutline_Level,
  getqtgs,
  insert_template,
  select_templateBypage,
  delete_template,
  select_template_BytemplateId2,
  select_template_BytemplateId,
  update_template,
  getqtdl,
} from "../api/home.js"; // 接口请求
export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id

    username() {
      return this.$store.state.username;
    },
    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 600}px`,
      };
    },
    id() {
      return this.$store.state.id;
    },
    imageSrc() {
      return `data:image/jpeg;base64,${this.base64Image}`; // 注意根据实际的MIME类型替换'image/jpeg'
    },
  },
  data() {
    return {
      tableDatach: [
        // 每一行的数据结构需要设置具体字段
        {
          region: "",
          font: "",
          fontSize: "",
          color: "",
          checkList: [],
          checkList_dl: [],
          lineSpace: 1.0,
          spaceBefore: 1.0,
          spaceAfter: 1.0,
        }, // 一行数据
      ],
      page_formatlook: "请先选择文种和模板",
      qtyslook: "请先选择文种和模板",

      characterslook: [
        {
          description: "请先选择文种和模板",
          text_color: "请先选择文种和模板",
        },
      ],
      chid: "",
      color1: "#409EFF",
      tableData: [
        // 每一行的数据结构需要设置具体字段
        {
          region: "正文",
          font: "仿宋_GB2312",
          fontSize: "三号",
          color: "#000000",
          checkList: [],
          checkList_dl: [],
          lineSpace: 12,
          spaceBefore: 0,
          spaceAfter: 0,
        }, // 一行数据
      ],
      tableData1: [],
      tableDatach: [
        // 每一行的数据结构需要设置具体字段
        {
          region: "",
          font: "",
          fontSize: "",
          color: "",
          checkList: [],
          checkList_dl: [],
          lineSpace: 1.0,
          spaceBefore: 1.0,
          spaceAfter: 1.0,
        }, // 一行数据
      ],
      checkedCities: [],
      checkedCitiesch: [],
      lineSpace: 1.0,
      spaceBefore: 1.0,
      spaceAfter: 1.0,
      num1: 2.54,
      num2: 2.54,
      num3: 3.18,
      num4: 3.18,
      marginsArray: [],
      num1ch: 1.0,
      num2ch: 1.0,
      num3ch: 1.0,
      num4ch: 1.0,
      marginsArraych: [],
      options: [], // 用于存储后端返回的数据
      options1: [],
      options2: [],
      options3: [],
      optionsle: [],
      optionssi: [],

      selectedUsers: [], // 用于存储选中的模板名字
      departments: [], // 用于存储从后端获取的处室数据
      adddia: false,
      adddiacs: false,

      inputvalyhm: "",
      sure_passw: "",
      new_passw: "",
      old_passw: "",

      form: {
        region: "正文",
        re1: null,
        page_size: null,
      },

      form1: {
        region: null,
        re1: null,
        page_size: null,
      },
      formLabelWidth: "100px",
      dialogVisible: false,
      dialogVisiblelook: false,
      lookid: "",
      dialogVisibleedit: false,
      up_img: false,
      nan_img: false,
      nv_img: false,
      backgroundImage: "", // 默认背景图
      tags: [], // 存储从后端获取的“句式特点”标签
      tags1: [], // 存储从后端获取的“句式特点”标签
      tags2: [], // 存储从后端获取的“句式特点”标签

      base64Image: "",
      xb: "",
      xm: "",
      szdw: "",
      ssgw: "",
      sjgzly: "",
      ssqy: "",
      age: "",
      wcdata: [],
      others: [],
      others_dl: [],

      // 定义变量存词云的返回值，然后用vuex传给词云组件
      fzqwnr: "",
      newText: "",
      newText1: "",
      newText2: "",
      newText3: "",
      xxdialog: false,
      xxcontent: "",
      kxdialog: false,
      sxdialog: false,
      rsdialog: false,
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      selectedText: "",
      con_loading: false,
      loading1: false,
      correctionList: [],
      jydis: true,
      navShow: true,
      textarea: "",
      textarea2: [],
      textarea21: [],
      textarea22: [],

      textarea3: "",
      textarea4: "",
      textarea4: "",
      dialogShow: false,
      dialog2Show: false,
      gzbj: [],
      stepActived: 1,
      loading: false,
      curIndex: "2",
      articles: "",
      artShow: false,
      mask: false,
      maskAll: false,
      /**
       * @description:  功能性按钮
       * @return {*}    传入组件->FunctionTable
       */
      steps: [
        { title: "录入角色" },
        { title: "录入写作风格" },
        { title: "生成词云" },
      ],

      currentStep: 2, // 初始化当前步骤为第二个步骤
      secRecommend: [], // 第二步推荐
      dynamicTags: [],
      inputVisible: true,
      inputValue: "",
      resDataItem: [],
      progressPercent: 0,
      fetchResPrams: [],
      buttonShow: true,
      drawer: false,
      direction: "ltr",
      activeNames: [],
      currentPage: 1, // 当前页
      pageSize: 10, // 每页显示条数
      total: 0,

      // 表单验证规则
      formRules: {
        userName: [
          { required: true, message: "模板名不能为空", trigger: "blur" },
        ],
        passWord: [
          { required: true, message: "密码不能为空", trigger: "blur" },
        ],
        handle_name: [
          { required: true, message: "所在处室不能为空", trigger: "change" },
        ],
        // gwmc: [
        //   { required: true, message: '最后修改时间不能为空', trigger: 'blur' }
        // ]
      },
      // };
    };
  },
  components: {
    App,
    // 注册组件
    CustomSteps,
    VueEditor,
    Wordcloud,
  },
  mounted() {
    // this.getuser(1, 10);
    // this.getszcs();
    this.getqtgs();
    this.getqtdl();

    this.getoutline_Level(), this.getdocument_type();
    this.getfont();
    this.getfont_size();
    this.getpaper_size();
    this.getpreset();
    this.getuser();
  },
  methods: {
    clickpb() {
      this.$router.push("/pb_manage");
    },
    handleCheckboxChange(row) {
      // 如果选中的项包含互斥选项
      if (row.checkList.includes("不校验")) {
        // 移除其他选项
        row.checkList = ["不校验"];
      } else {
        // 允许选中其他项
        // 此处你可以添加更多的逻辑，看是否需要处理其他情况
      }
    },
    // }
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    async look(value) {
      let params = {
        template_id: value,
      };
      let res = await select_template_BytemplateId(params); // 调用接口发送数据

      this.characterlook = res.data.data.character;
      // console.log(this.character, "我是character");
      this.page_formatlook = res.data.data.page_format;
      this.qtyslook = res.data.data.qtys;
      this.characterslook = res.data.data.character;
    },
    async getch(c1) {
      this.tableDatach = [];
      this.chid = c1.template_id;
      let params = {
        template_id: c1.template_id,
      };
      let res = await select_template_BytemplateId2(params);
      // console.log(res.data, "获取模板");
      let receivedData = res.data.data.character_1;
      console.log(receivedData, "获取模板");
      receivedData.forEach((row, index) => {
        if (this.tableDatach[index]) {
          // 确保目标行存在
          this.tableDatach[index].region = row.outline_level; // 使用 outline_level 作为 region
          this.tableDatach[index].font = row.font_id; // 使用 font_id 作为 font
          this.tableDatach[index].fontSize = row.font_size; // 使用 font_size 作为 fontSize
          // this.tableDatach[index].color = row.color;          // 使用 color 字段
          this.tableDatach[index].checkList = row.qtgs; // 使用 qtgs 作为 checkList
          this.tableDatach[index].checkList_dl = row.qtdl; // 使用 qtgs 作为 checkList
          // this.tableDatach[index].checkList = row.qtgs;     // 使用 qtgs 作为 checkList
          this.tableDatach[index].lineSpace = row.lineSpace;
          this.tableDatach[index].spaceBefore = row.spaceBefore;
          this.tableDatach[index].spaceAfter = row.spaceAfter;
        } else {
          // 如果目标行不存在，可以选择添加新行（如果需要）
          this.tableDatach.push({
            region: row.outline_level,
            font: row.font_id,
            fontSize: row.font_size,
            color: row.color,
            checkList: row.qtgs,
            checkList_dl: row.qtdl,
            lineSpace: row.lineSpace,
            spaceBefore: row.spaceBefore,
            spaceAfter: row.spaceAfter,
          });
        }
      });
      this.form1.userName = res.data.data.template_name;
      this.form1.re1 = res.data.data.type_id;
      this.form1.page_size = res.data.data.paper_size;
      this.form1.marginsArraych = res.data.data.ybj;
      this.num1ch = res.data.data.ybj[0];
      this.num2ch = res.data.data.ybj[1];
      this.num3ch = res.data.data.ybj[2];
      this.num4ch = res.data.data.ybj[3];
      this.checkedCitiesch = res.data.data.qtys;
    },
    //        async getch(c1) {
    //   this.chid = c1.template_id;
    //   let params = {
    //     'template_id': c1.template_id
    //   };
    //   let res = await select_template_BytemplateId2(params);
    //   // console.log(res.data, "获取模板");
    //   let receivedData = res.data.data.character_1;
    //   console.log(receivedData, "获取模板");

    //   receivedData.forEach((row, index) => {
    //     if (row) { // 检查每一行是否为空
    //       if (this.tableDatach[index]) { // 确保目标行存在
    //         this.tableDatach[index].region = row.outline_level; // 使用 outline_level 作为 region
    //         this.tableDatach[index].font = row.font_id;         // 使用 font_id 作为 font
    //         this.tableDatach[index].fontSize = row.font_size;   // 使用 font_size 作为 fontSize
    //         // this.tableDatach[index].color = row.color;          // 使用 color 字段
    //         this.tableDatach[index].checkList = row.qtgs;     // 使用 qtgs 作为 checkList
    //         // this.tableDatach[index].checkList = row.qtgs;     // 使用 qtgs 作为 checkList
    //         this.tableDatach[index].lineSpace = row.lineSpace;
    //         this.tableDatach[index].spaceBefore = row.spaceBefore;
    //         this.tableDatach[index].spaceAfter = row.spaceAfter;
    //       } else {
    //         // 如果目标行不存在，可以选择添加新行（如果需要）
    //         this.tableDatach.push({
    //           region: row.outline_level,
    //           font: row.font_id,
    //           fontSize: row.font_size,
    //           color: row.color,
    //           checkList: row.qtgs,
    //           lineSpace: row.lineSpace,
    //           spaceBefore: row.spaceBefore,
    //           spaceAfter: row.spaceAfter
    //         });
    //       }
    //     } else {
    //       // 如果接收到的数据行为空，可以选择删除对应的行（如果需要）
    //       if (this.tableDatach[index]) {
    //         this.tableDatach.splice(index, 1);
    //       }
    //     }
    //   });
    //   this.form1.userName = res.data.data.template_name;
    //   this.form1.re1 = res.data.data.type_id;
    //   this.form1.page_size = res.data.data.paper_size;
    //   this.form1.marginsArraych = res.data.data.ybj;
    //   this.num1ch = res.data.data.ybj[0];
    //   this.num2ch = res.data.data.ybj[1];
    //   this.num3ch = res.data.data.ybj[2];
    //   this.num4ch = res.data.data.ybj[3];
    //   this.checkedCitiesch = res.data.data.qtys;
    // },

    showdia3(c1) {
      let params = {
        template_id: [c1.template_id],
      };
      this.$confirm("此操作将永久删除该模板，是否继续？")
        .then(() => {
          delete_template(params)
            .then((res) => {
              if (res.data.status_code == 200) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                });
                this.getuser(this.currentPage, this.pageSize);
              } else {
                this.$message({
                  message: "删除失败",
                  type: "error",
                });
              }
            })
            .catch((error) => {
              console.error("删除时出现错误:", error);
              this.$message({
                message: "删除失败，请稍后重试",
                type: "error",
              });
            });
        })
        .catch(() => {
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async getuser(c1, c2, c3) {
      //
      let params = {
        template_name: c3,
        pageNo: c1,
        pageSize: c2,
      };
      let res = await select_templateBypage(params);
      this.tableData1 = res.data.data;
      this.total = res.data.total_records;
    },
    async add_mb() {
      const character_1 = this.tableData.map((row) => {
        return {
          outline_level: row.region === "正文" ? 1 : row.region, // 判断 outline_level，如果是 '正文' 则赋值为 1
          font_id: row.font === "仿宋_GB2312" ? 6 : row.font, // 判断 font_id，如果是 '仿宋_GB2312' 则赋值为 6
          font_size: row.fontSize, // 字号
          // color: row.color, // 颜色
          qtgs: row.checkList, // 其他设置
          qtdl: row.checkList_dl, // 其他设置
          lineSpace: row.lineSpace,
          spaceBefore: row.spaceBefore,
          spaceAfter: row.spaceAfter,
        };
      });
      this.marginsArray = [this.num1, this.num2, this.num3, this.num4];
      //  console.log(this.marginsArray, "marginsArray");
      // console.log(character_1, "格式化数据");
      let params = {
        cjrid: this.id,
        template_name: this.form.userName,
        type_id: this.form.re1,
        character_1: character_1,
        font_paper_size_id: this.form.page_size,
        ybj: this.marginsArray,
        qtys: this.checkedCities,
      };
      console.log(params, "参数");
      let res = await insert_template(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "新增成功",
          type: "success",
        });
        this.adddia = false;
        this.getuser(this.currentPage, this.pageSize);
        this.resetForm();
      } else {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    async change_mb() {
      const character_1 = this.tableDatach.map((row) => {
        return {
          outline_level: row.region === "正文" ? 1 : row.region, // 判断 outline_level，如果是 '正文' 则赋值为 1
          font_id: row.font === "仿宋_GB2312" ? 6 : row.font,
          font_size: row.fontSize, // 字号
          // color: row.color, // 颜色
          qtgs: row.checkList, // 其他设置
          qtdl: row.checkList_dl, // 其他设置
          lineSpace: row.lineSpace,
          spaceBefore: row.spaceBefore,
          spaceAfter: row.spaceAfter,
        };
      });
      //  this.marginsArraych=[this.num1ch,this.num2ch,this.num3ch,this.num4ch];

      let params = {
        cjrid: this.id,
        template_id: this.chid,
        template_name: this.form1.userName,
        type_id: this.form1.re1,
        character_1: character_1,
        font_paper_size_id: this.form1.page_size,
        ybj: [this.num1ch, this.num2ch, this.num3ch, this.num4ch],
        qtys: this.checkedCitiesch,
      };
      console.log(params, "参数");
      let res = await update_template(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "修改成功",
          type: "success",
        });
        this.dialogVisibleedit = false;
        this.getuser(this.currentPage, this.pageSize);
        this.resetFormch();
      } else {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    handleClick(row) {
      console.log(row, "点击行");
      this.form.userName = row.yhm;
      this.form.passWord = row.passWord;
      this.form.handle_name = row.handle_name;
    },
    async getqtgs() {
      let res = await getqtgs();
      this.others = res.data.data;
      // console.log(res.data.data, "qita1");
    },
    async getqtdl() {
      let res = await getqtdl();
      this.others_dl = res.data.data;
      // console.log(res.data.data, "qita1");
    },
    addRow() {
      this.tableData.push({
        name: "",
        font: null,
        fontSize: null,
        checkList: [],
        checkList_dl: [],
      });
      //  { name: '', font: null,  fontSize: null, color:'#000000',checkList:[] } // 一行数据
    },
    addRowch() {
      this.tableDatach.push({
        name: "",
        font: null,
        fontSize: null,
        checkList: [],
        checkList_dl: [],
      });
      //  { name: '', font: null,  fontSize: null, color:'#000000',checkList:[] } // 一行数据
    },
    removeRow(index) {
      if (this.tableData.length > 1) {
        this.tableData.splice(index, 1);
      }
    },
    removeRowch(index) {
      if (this.tableDatach.length > 1) {
        this.tableDatach.splice(index, 1);
      }
    },
    async getoutline_Level() {
      let res = await getoutline_Level();
      this.optionsle = res.data.data;
      console.log(this.optionsle[0].outline_Level_id, "大纲级别");
    },

    removeRow() {
      if (this.tableData.length > 0) {
        this.tableData.pop();
      }
    },
    // }
    async getdocument_type() {
      let res = await getdocument_type();
      //  this.options = responseData;
      this.options = res.data.data;
      console.log(this.options[0].type_id, "文种");
    },
    async getfont() {
      let res = await getfont();
      this.options3 = res.data.data;
      console.log(this.options3[0].font_id, "字体");
    },
    async getfont_size() {
      let res = await getfont_size();
      this.optionssi = res.data.data;
    },
    async getpaper_size() {
      let res = await getpaper_size();
      this.options1 = res.data.data;
    },
    async getpreset() {
      let res = await getpreset();
      //  this.options2_change = res.data.data.map(item => item.preset_value);
      this.options2 = res.data.data;
    },
    // },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    clickgosygl() {
      this.$router.push("/sygl");
    },

    handleCloseadd() {
      this.adddia = false; // 确保关闭对话框
      this.resetForm(); // 重置表单
    },
    handleClosecs() {
      // 关闭对话框时重置表单

      this.formcs = {
        csm: "",
      };
      console.log(this.formcs.csm, "什么玩应");
      this.formcs.csm = "";
      this.adddiacs = false; // 确保关闭对话框
    },
    addcs() {
      let params = {
        csm: this.formcs.csm,
      };
      let res = insert_handle(params);

      // console.log(res,'新增处室接口的res')
      let x = res.then((data) => {
        // console.log(data.data.status_code);
        if (data.data.status_code == 200) {
          this.$message({
            message: "新增处室成功",
            type: "success",
          });
          this.getszcs();

          this.adddiacs = false;
          this.getuser(this.currentPage, this.pageSize);
          this.formcs.csm = "";
          // this.getszcs();
          // this.getuser(this.currentPage, this.pageSize);
        } else {
          this.$message({
            message: "新增处室失败",
            type: "error",
          });
        }
      });
    },
    // },
    clearInput() {
      this.inputvalyhm = ""; // 清空输入框内容
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    handleSelectionChange(selected) {
      // selected 是当前选中的模板数组
      this.selectedUsers = selected.map((user) => user.template_id); // 更新 selectedUsers 数组
    },
    submitForm() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          this.adduser(); // 在验证通过后调用添加模板的函数
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    async adduser() {
      let params = {
        userName: this.form.userName,
        passWord: this.form.passWord,
        handle_name: this.form.handle_name,
        gwmc: this.form.gwmc,
        yhlx: this.form.yhlx,
      };
      let res = await add_Single_User(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "新增成功",
          type: "success",
        });
        this.adddia = false;
        this.getuser(this.currentPage, this.pageSize);
      } else {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    //  },
    async handleSwitchChange(row) {
      const status = row.disabled === "禁用" ? "正常" : "禁用"; // 取反状态
      const params = {
        yhm: row.yhm, // 假设每行有个 yhm 字段
      };

      try {
        let res;

        if (status === "禁用") {
          // 调用启用接口
          res = await remove_disabled(params);
        } else {
          // 调用禁用接口
          res = await disabled(params);
        }

        if (res.data.data.code === 10000) {
          this.$message({
            message: status === "禁用" ? "启用成功" : "禁用成功",
            type: "success",
          });
          row.disabled = status; // 更新行状态
        } else {
          this.$message({
            message: res.data.data.message || "操作失败",
            type: "error",
          });
        }
        // 刷新模板列表
        this.getuser(this.currentPage, this.pageSize);
      } catch (error) {
        console.error("状态更新失败", error);
        this.$message({
          message: "状态更新失败",
          type: "error",
        });
      }
    },

    clickzhgl() {
      this.$router.push("/grzx2");
    },
    // 获取所在处室
    async getszcs() {
      // let params = {
      //   yhm: c3,
      //   page: c1,
      //   pageSize: c2,
      // };
      let rescs = await select_handle_all();
      // alert(rescs.data);
      this.departments = rescs.data.data;
      // this.tableData1 = res2.data.data;
      // console.log(res2.data.total_records, "分页");
      // this.total = res2.data.total_records;
    },
    async sea(c4) {
      // let pam = {
      //   yhm: c4,
      //   pageSize: '',
      //   page: '',
      // };
      this.getuser("", "", c4);
      // this.inputvalyhm = "";
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.getuser(this.currentPage, val);
      // console.log(`每页 ${val} 条`);
      this.pageSize = val;
    },
    clickgrzx() {
      this.$router.push("/grzx1");
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.getuser(val, this.pageSize);
      this.currentPage = val;
    },

    // 修改
    async showdiaedit() {
      this.dialogVisibleedit = true;

      let params = {
        id: this.formedit.id,
      };
      let res = await update_Single_User(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "修改成功",
          type: "success",
        });
        this.dialogVisibleedit = false;
        this.getuser(this.currentPage, this.pageSize);
      } else {
        this.$message({
          message: "修改失败",
          type: "error",
        });
      }
    },

    async batchDelete() {
      // 检查是否有选中的模板
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择模板！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的模板, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            template_id: this.selectedUsers, // 将选中的文件名数组发送给后端
          };

          try {
            let res = await delete_template(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的模板
              this.getuser(1, this.pageSize); // 刷新模板列表
              this.currentPage = 1; // 刷新当前页
            } else {
              this.$message({
                message: "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },

    changePwd() {
      // alert('sss')
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    async getcyt() {
      // this.currentStep = 3;
      let params = {
        name: this.username,
      };
      let res = await cyt(params);
      this.wcdata = res.data.data;
      console.log("词云数据:", this.wcdata);
      this.$store.dispatch("updatecy", this.wcdata);
    },
    clickzskgl() {
      this.$router.push("/zsk");
    },
    resetForm() {
      this.form = {
        userName: "",
        re1: "", // 文种选择
        page_size: "", // 页面大小
        // 添加表单中其他属性的初始值
      };

      // 如果需要重置tableData，可以在这里进行
      this.tableData.forEach((row) => {
        row.region = "正文"; // 可以设置为默认值
        row.font = "仿宋 GB2312"; // 默认字体
        row.fontSize = "三号"; // 默认字号
        row.color = ""; // 默认颜色
        row.checkList = []; // 清空其他设置
        row.checkList_dl = []; // 清空其他设置
        row.lineSpace = 12; // 默认行间距
        row.spaceBefore = 0; // 默认段前间距
        row.spaceAfter = 0; // 默认段后间距
      });
      // 也可以重置边距值
      this.num1 = 2.54;
      this.num2 = 2.54;
      this.num3 = 3.18;
      this.num4 = 3.18;
      this.checkedCities = []; // 清空复选框选项
    },
    resetFormch() {
      this.form1 = {
        userName: "",
        re1: "", // 文种选择
        page_size: "", // 页面大小
        // 添加表单中其他属性的初始值
      };

      // 如果需要重置tableData，可以在这里进行
      this.tableDatach.forEach((row) => {
        row.region = ""; // 可以设置为默认值
        row.font = ""; // 默认字体
        row.fontSize = ""; // 默认字号
        row.color = ""; // 默认颜色
        row.checkList = []; // 清空其他设置
        row.checkList_dl = []; // 清空其他设置
        row.lineSpace = 1; // 默认行间距
        row.spaceBefore = 1; // 默认段前间距
        row.spaceAfter = 1; // 默认段后间距
      });

      // 也可以重置边距值
      this.num1ch = 0;
      this.num2ch = 0;
      this.num3ch = 0;
      this.num4ch = 0;
      this.checkedCitiesch = []; // 清空复选框选项
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    togrzx() {
      // alert(1)
      this.$router.push("/grzx1");
    },
    // async getuser(c1) {
    //   let params = {
    //     page: c1,
    //   };
    //   let res2 = await get_login_user(params);
    //   this.tableData1 = res2.data.data;
    //   // console.log(res2.data.total_records, "分页");
    //   this.total = res2.data.total_records;
    // },
    async get_write() {
      let params = {
        name: this.username,
      };
      let res2 = await getwrite(params);
      // console.log(res2.data.data.szqt, "受众群体");
      this.tags = res2.data.data.jstd
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
      this.tags1 = res2.data.data.wzfg
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
      this.tags2 = res2.data.data.szqt
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
    },

    showContextMenu(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener("click", this.hideContextMenu);
      }
    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener("click", this.hideContextMenu);
    },

    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "************************************");
      // this.textarea3 = html;
      // return html;
    },
    getPlainText(html) {
      const div = document.createElement("div");
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    },
    //     getHtmlContent(html) {
    //   const div = document.createElement('div');
    //   div.innerHTML = html;
    //   return div.innerHTML;
    // },

    onCopySuccess() {
      console.log("复制成功");
    },
    onCopyError() {
      console.log("复制失败");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    ignore() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
    },
    highlightChange(text1, text2) {
      console.log(this.textarea4);
      let changeData = text2;
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.textarea4 = this.removetext(this.textarea4);
      this.textarea4 = this.textarea4.replace(text1, changeData);
    },
    highlightError(error, word) {
      this.textarea4 = this.removetext(this.textarea4);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.textarea4 = this.textarea4.replace(error, a);
      console.log(this.textarea4);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(this.$refs.editor.$el, "editorIframe");
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    removetextp(text) {
      let str = text;
      let reg1 = new RegExp(`<p>`, "g");
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</p>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    onCopySuccess() {
      this.$message({
        message: "内容已复制到剪贴板！",
        type: "success",
      });
    },
    onCopyError() {
      this.$message({
        message: "复制失败，请手动复制!",
        type: "warning",
      });
    },

    async jiaoyan() {
      this.textarea4 = this.removetext(this.textarea4);
      this.con_loading = true;
      // let text = this.removetextp(this.textarea4)
      testcheck({
        test_all: this.textarea4,
      }).then(async (data) => {
        this.correctionList = data.data.data;
        this.con_loading = false;
        if (this.correctionList.length == 0) {
          this.jydis = true;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          console.log(data, "619");
          this.jydis = false;
        }
      });
    },
    handleClosedrawer() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    // async jiaoyan(){
    //   this.jydis = false
    // },
    save() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.jydis = true;
    },
    fanhui() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    handleStepClick(index) {
      this.lock++;
      this.activeIndex = index; // 更新activeIndex的值
    },
    upShow() {
      this.navShow = !this.navShow;
    },
    async textareaFocus() {
      let data = await getLabel1();
      this.gzbj = data.data;
      this.dialogShow = true;
    },
    closeDialog() {
      this.dialogShow = false;
    },
    textarea2Focus() {
      this.dialog2Show = true;
    },
    closeDialog2() {
      this.dialog2Show = false;
    },
    async getText(e) {
      console.log(e);
      let params = {
        label: e,
      };
      let data = await getLabel2(params);
      console.log(data, "583");
      this.secRecommend = data.data;
      this.textarea = e;
      this.textarea2 = [];
    },
    getText2(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea2.push(e);
        this.dynamicTags.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm(e);
      } else {
        console.log(111);
      }
    },
    getText21(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea21.push(e);
        this.dynamicTags1.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm1(e);
      } else {
        console.log(111);
      }
    },
    getText22(e) {
      if (!this.dynamicTags2.includes(e)) {
        this.textarea22.push(e);
        this.dynamicTags2.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm2(e);
      } else {
        console.log(111);
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickszbg() {
      this.$router.push("/xz");
    },

    //角色
    clickTopld() {
      this.$router.push("/grzx");
    },
    clickTopLd() {
      this.$router.push("/sfw");
    },
    //   提示词
    clickToptsc() {
      this.$router.push("/tsc");
    },
    firstNextStep() {
      if (this.textarea == "") {
        this.$notify({
          title: "提示",
          message: "请填写工作背景",
          type: "warning",
        });
      } else if (this.textarea2.length == 0) {
        this.$notify({
          title: "提示",
          message: "请填写工作要点关键词",
          type: "warning",
        });
      } else {
        this.mask = true;
        if (this.mask == true) {
          this.getInfo(this.textarea, this.dynamicTags);
        }
      }
    },
    async getInfo(c1, c2) {
      this.loading = true;
      let params = {
        test_1: c1,
        test_2: c2,
      };
      let res = await getSecDtDatas(params);
      // console.log(res.data.status_code,'我是状态码11111')
      // console.log(res.data.message,'我是提示33332222')
      if (res.data.status_code == 200) {
        // 弹出提示
        this.$message({
          message: res.data.message,
          type: "success",
        });
        this.textarea3 = "";

        // console.log(res.data, 55555555);
        this.textarea3 = res.data.data;
        // console.log(this.textarea3,66666);
        // 这里放置需要执行的逻辑或调用其他方法
        this.buttonShow = true;
        // this.stepActived = 2;
        this.currentStep = 2;
        this.loading = false;
        this.mask = false;
        this.curIndex = "2";
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10001) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10002) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    repeatStep() {
      this.loading = true;
      this.mask = true;
      if (this.mask == true) {
        this.getInfo(this.textarea, this.dynamicTags);
      }
    },
    success() {
      this.artShow = true;
      this.loading = true;
      // 这里放置需要执行的逻辑或调用其他方法
      if (this.textarea3 == "") {
        this.$notify({
          title: "提示",
          message: "大纲内容为空，无法生成",
          type: "warning",
        });
      } else {
        this.progressPercent = 0;
        this.getSuccessInfo(this.textarea3, this.textarea);
      }
    },
    // 依次请求接口
    async fetchResData(index, nextIndex) {
      if (index >= this.fetchResPrams.length) {
        this.fetchData(nextIndex);
        return;
      }
      console.log(this.fetchResPrams[index], "675");
      let pams = {
        string: this.fetchResPrams[index],
        id_name: this.textarea,
      };
      const response = await getSuccessInfoItem(pams);
      if (response.data.status_code == 200) {
        // this.$message({
        //   message: response.data.message,
        //   type: 'success'
        // });
        this.textarea4 += response.data.data;

        let totalLength = 0;
        let allSubArrays = this.resDataItem.filter(
          (item) => typeof item != "string"
        );
        allSubArrays.forEach((subArray) => {
          totalLength += subArray.length;
        });
        let kuai = 100 / totalLength;
        this.progressPercent += Math.floor(kuai);
        await this.fetchResData(index + 1, nextIndex);
      } else if (response.data.status_code == 500) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10001) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10002) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      }
    },
    // 一段式生成
    async fetchData(index) {
      if (index >= this.resDataItem.length) {
        this.progressPercent = 100;
        this.maskAll = false;
        this.buttonShow = false;
        return;
      }
      if (typeof this.resDataItem[index] == "string") {
        this.textarea4 += this.resDataItem[index];
        await this.fetchData(index + 1);
      } else {
        this.fetchResPrams = this.resDataItem[index];
        this.fetchResData(0, index + 1);
      }
      // this.stepActived = 3;
      this.currentStep = 3;
      this.loading = false;
    },
    async getSuccessInfo(c1, c2) {
      this.loading = true;
      this.maskAll = true;
      let params = {
        string: c1,
        work: c2,
      };
      let res = await getSuccessInfo(params);
      if (res.data.status_code == 200) {
        // this.$message({
        //   message: res.data.message,
        //   type: 'success'
        // })
        this.textarea4 = "";
        this.resDataItem = res.data.data;
        this.fetchData(0);
        this.currentStep = 3;
        // this.stepActived = 3;
        this.loading = false;
        // }
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    clickTab(i) {
      this.curIndex = i;
      // }
    },
  },
  watch: {
    xb(newVal) {
      if (newVal) {
        this.backgroundImage = "../img/bga.png"; // 男背景图
      } else {
        this.backgroundImage = "../img/hahu3.png"; // 女背景图
      }
    },
  },
  // };
};
</script>
<style lang="less" scoped>
.dChangePwd .footerInline .tsFont {
  width: 100%;
  line-height: 30px;
  margin-top: 0;
  color: #f60;
}

.ml {
  margin-left: 100px;
}
.ml1 {
  margin-left: 120px;
}
.ml2 {
  margin-left: 100px;
}
::v-deep .el-descriptions-item__label.has-colon::after {
  content: "";
  margin-top: 20px;
}

.el-tag {
  background: #f5f8fc;
  border-radius: 12px;
  padding: 4px;
  // padding-left: 2px;
  // padding-right: 5px;
  // height: 32px;
  // line-height: 32px;
}

.btnr {
  margin-top: 190px;
}
.btn-group {
  background: url(../img/bga.png) no-repeat center;
  background-size: 45%;
}
.btn-group1 {
  // margin-right: 500px;
  background: url(../img/hahu3.png) no-repeat center;
  background-size: 45%;
}
// .btn-group1{
//     background: url(../img/hahu3.png) no-repeat left;
//     background-size: 40%;
//     margin-left: -500px;
// }
// .btn-group {
//   // background: url(../img/bga.png) no-repeat center;
//   background: url(this.backgroundImage) no-repeat center;
//   background-size: 45%;
// }
.left1 {
  background: #ffffff;
  border-radius: 8px;
}
.next_but {
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  width: 130px;
  height: calc(100vh * 40/ 1080);
  border-radius: 20px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 600;
  &:hover {
    color: #ffffff;
  }
  &:active {
    color: #ffffff;
  }
}
.right {
  background: url(../img/bga.png) no-repeat center;
  // width: 88%;
  // height: 100%;
  background-size: 70%;
  //  background: #FFFFFF;
  // border-radius: 8px;
}
.el-select {
  width: 100%;
  font-size: calc(100vw * 14 / 1920);
}

::v-deep(.el-input) {
  font-size: calc(100vw * 14 / 1920);
}
::v-deep(.el-input__inner) {
  height: calc(100vh * 40 / 1080);
  line-height: calc(100vh * 40 / 1080);
  padding: 0 calc(100vw * 10 / 1920);
}
::v-deep(.el-checkbox__label) {
  font-size: calc(100vw * 14 / 1920);
}
::v-deep(.el-input__icon) {
  display: flex;
  align-items: center;
}
::v-deep(.el-checkbox) {
  font-size: calc(100vw * 14 / 1920);
  margin-right: calc(100vw * 30 / 1920);
}
::v-deep(.el-checkbox__inner) {
  width: calc(100vw * 14 / 1920);
  height: calc(100vh * 14 / 1080);
}
::v-deep(.el-input-number--mini) {
  width: calc(100vw * 130 / 1920);
  height: calc(100vh * 28 / 1080);
  line-height: calc(100vh * 28 / 1080);
}
::v-deep(.el-input-number--mini .el-input-number__decrease) {
  width: calc(100vw * 28 / 1920);
  height: calc(100vh * 26 / 1080);
  font-size: calc(100vw * 14 / 1920);
  top: calc(100vh * 1 / 1080);
  left: calc(100vw * 1 / 1920);
}
::v-deep(.el-input--mini .el-input__inner) {
  height: calc(100vh * 28 / 1080);
  line-height: calc(100vh * 28 / 1080);
  padding-left: calc(100vw * 35 / 1920);
  padding-right: calc(100vw * 35 / 1920);
}
::v-deep(.el-input-number--mini .el-input-number__increase) {
  width: calc(100vw * 28 / 1920);
  height: calc(100vh * 26 / 1080);
  font-size: calc(100vw * 14 / 1920);
  top: calc(100vh * 1 / 1080);
  right: calc(100vw * 1 / 1920);
}

.fir-kuai2 {
  margin-top: px;
  margin-right: 10px;

  width: 6px;
  height: 16px;
  float: left;
  background: #4081ff;
  border-radius: 1.5px;
}

.el-upload__tip {
  margin-top: -20px;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}
// .avatar-uploader .el-upload {
//   border: 1px #d9d9d9 dashed !important;
//   border-radius: 6px;
//   cursor: pointer;
//   position: relative;
//   overflow: hidden;
// }
.parent-container .avatar-uploader .el-upload {
  border: 1px #d9d9d9 dashed !important;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: calc(100vw * 28 / 1920);
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.ai-left-bar-li {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh *  50 / 1080);
  width: calc(100vw *  1000/ 1920);
  // color: #000;

  line-height: 16px;
  white-space: nowrap;
  cursor: pointer;
  // z-index: 9999;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 19 / 1920);
  // color: #000000;
  letter-spacing: 0;
  text-align: center;
  background: url(../assets/jstext.png) no-repeat center;
}

.actived {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  // color: #fff;
}
.context-menu {
  margin-left: -200px;
  position: absolute;
  // background: white;
  background: #fff;

  // border: 1px dashed #ccc;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
  // margin-left:-200px;
}

.context-menu li {
  padding: 8px 16px;
  cursor: pointer;
  // margin-left:-200px;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}

.fr {
  float: right;
}

.work-container {
  height: 100%;

  .result-container {
    padding: 30px;

    .elcol-title {
      display: flex;
      overflow: hidden;
      width: 100%;
      height: 48px;

      .elcol-title-text {
        // float: left;
        padding-left: 10px;
        text-align: left;
        width: 40%;
        height: 100%;
      }

      .elcol-title-text2 {
        font-size: calc(100vw * 14 / 1920);
        // color: #d1d7de;
        // float: left;
        width: 75px;
      }

      .elcol-input {
        float: left;
        width: calc(60% - 75px);
        border: none !important;
      }
    }
  }
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

.ejdhl {
  // margin-top: 43px;
  margin-top: calc(100vh * 43 / 1080);
  background-color: #fff;
  height: calc(100vh - (100vh * 43 / 1080));
  //  background: url(../assets/img-left.png) no-repeat center;
  // background-size: cover;
  // background-position: center;
  // transition: ease-out 0.4s;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(
    76deg,
    #07389c 0%,
    #3d86d1 0%,
    #3448b3 100%
  );

  img {
    width: 120px;
    height: 48px;
  }

  p {
    border: 1.54px dashed rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header1 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  background: url(../assets/jsbg.png) no-repeat center;
  background-color: #fff;
  .ai-header {
    width: 100%;
    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: 40px;
        height: calc(100vh * 40/ 1080);
        border-radius: 20px;
        // background: #f4f6f8;
        border: 1px dashed #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 42px;
            width: 130px;
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: 16px;
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: 16px;
              height: 16px;
              margin-right: calc(100vw * 14 / 1920);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: 20px;
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: calc(100% - calc(100vw * 300 / 1920));
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: calc(100vh * 20 / 1080) calc(100vw * 10 / 1920);
  overflow: hidden;

  .ai-gai {
    position: fixed;
    /* top: 0; */
    left: 0;
    bottom: 0;
    height: 91%;
    // background: rgba(122, 151, 255, 0.6);
    z-index: 999;
  }

  .ai-body {
    transition: ease-out 0.4s;
    width: 100%;
    height: 100%;
    // background: red;
    float: left;
    overflow: hidden;

    .ai-body-left {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      width: 444px;
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-left: 10px;
      float: left;

      .ai-body-left-top {
        width: 100%;
        height: calc(100% - calc(100vh * 80 / 1080));
        overflow-y: scroll;
      }

      .ai-body-left-bottom {
        width: 100%;
        height: 90px;

        // padding-top: 30px;
        .ai-body-left-bottom-button {
          height: calc(100vh * 40/ 1080);
          // font-weight: 600;
          font-size: calc(100vw * 14 / 1920);
          letter-spacing: 0px;
          flex-grow: 1;
          color: #fff;
          margin: 0 25px;
          cursor: pointer;
          line-height: calc(100vh * 40/ 1080);
          margin-top: 30px;
          background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 20px;
          font-family: PingFangSC-Regular;
        }
      }

      .ai-body-left-bottom2 {
        width: 100%;
        height: calc(100vh * 80 / 1080);
        padding-top: 30px;

        .repeat {
          width: 188px;
          float: left;
          height: calc(100vh * 40/ 1080);
          font-size: calc(100vw * 14 / 1920);
          flex-grow: 1;
          margin-left: 25px;
          cursor: pointer;
          line-height: calc(100vh * 40/ 1080);
          background: #f4f6f8;
          border: 1px dashed rgba(230, 230, 230, 1);
          border-radius: 20px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #666666;
          letter-spacing: 0;
          text-align: center;
          // font-weight: 600;
        }

        .ai-body-left-bottom-button {
          width: 188px;
          float: left;
          height: calc(100vh * 40/ 1080);
          flex-grow: 1;
          margin-left: 14px;
          cursor: pointer;
          line-height: calc(100vh * 40/ 1080);
          background-image: linear-gradient(102deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 20px;
          margin-top: 0px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          // font-weight: 600;
        }
      }

      .ai-step {
        margin-top: 20px;
        width: calc(100% - 40px);
        margin: 0 20px;
        margin-top: 5px;
        height: calc(100vh * 40/ 1080);
      }
    }

    .ai-body-right {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      width: calc(100% - 480px);
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-left: 20px;
      float: left;

      .ai-body-start {
        width: 100%;
        height: 100%;

        .pic_bkg1 {
          // width: calc(100vw * 670 / 1920);
          // height: calc(100vw * 590 / 1920);
          width: 670px;
          height: 590px;
          background: url(../assets/img-bg1.png) no-repeat center;
          // background: url(../assets/img-bg1.png) no-repeat center;
          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 70px;
          position: relative;
        }

        .pic_bkg {
          width: 528px;
          height: 518px;
          // z-index: 15;
          background: url(../assets/img-ai.png) no-repeat center;
          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 118px;
          position: relative;
        }

        .pic_font {
          position: absolute;
          margin-left: auto;
          margin-right: auto;
          left: 0;
          right: 0;
          bottom: 116px;
          width: 255px;
          height: calc(100vh * 40/ 1080);
          border: 1.54px dashed rgba(0, 0, 0, 0);
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 28 / 1920);
          color: #000000;
          text-align: center;
          font-weight: 600;
        }

        .title_message {
          position: absolute;
          margin-left: auto;
          margin-right: auto;
          left: 0;
          right: 0;
          bottom: 82px;
          text-align: center;
          line-height: 16px;
          margin-top: 10px;
          height: 22px;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw * 14 / 1920);
          color: #999999;
          letter-spacing: 0;
          font-weight: 400;
        }

        .pic_step {
          width: 551px;
          height: 142px;
          z-index: 15;
          background: url("../assets/pic_step.png");
          background-size: contain;
          margin: auto;
        }
      }

      .ai-body-art {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;

        .over {
          overflow: auto;
        }

        .fir-textarea {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 96.5%;
          background: #f8f9fd;
          border: 1px dashed rgba(229, 232, 245, 1);
          border-radius: 4px;
          margin: 20px;
          margin-bottom: 0;
          height: 158px;

          ::v-deep(.el-textarea__inner) {
            font-size: calc(100vw * 14 / 1920) !important;
            background-color: #f8f9fd !important;
            height: 100% !important;
            font-family: PingFangSC-Regular;
            padding: 13px 18px 33px 16px;
          }
        }

        .fir-textarea-max {
          height: 95% !important;
        }

        ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
          display: none !important;
        }

        ::v-deep(.ql-blank) {
          display: none !important;
        }

        ::v-deep(.ql-container.ql-snow) {
          border: 0;
        }
      }
    }

    .ai-tab {
      width: 230px;
      height: calc(100vh * 40/ 1080);
      margin: 0 auto;
      margin-top: 30px;
      background: #f4f6f8;
      border: 1px dashed #eeeff0;
      border-radius: 20px;

      .tab-item {
        width: 50%;
        height: calc(100vh * 40/ 1080);
        // line-height: 16px;
        float: left;
        line-height: calc(100vh * 40/ 1080);
        cursor: pointer;

        font-family: PingFangSC-Semibold;
        font-size: calc(100vw * 14 / 1920);
        color: #9094a5;
        letter-spacing: 0;
        text-align: center;
      }

      .activedTab {
        border-radius: 20px;

        background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
        box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
        color: #ffffff;
      }
    }

    .tab-item-fir {
      width: 100%;
      height: 536px;
      padding: 0 25px;

      .fir-title {
        color: #222;
        font-weight: 500;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 16px;
        overflow: hidden;
        margin-top: 20px;
        margin-bottom: 20px;
        height: 20px;

        .fir-kuai {
          width: 6px;
          height: 16px;
          margin-right: 8px;
          float: left;
          // margin-top: 2px;
          background: #4081ff;
          border-radius: 1.5px;
        }

        .fir-title-p {
          line-height: 16px;
          float: left;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw * 14 / 1920);
          color: #333333;
          letter-spacing: 0;
          font-weight: 400;
        }
      }

      .fir-alert {
        margin-top: 10px;
        height: 35px;
      }

      .ai-dialog {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        row-gap: 1px;
        width: 100%;
        height: -moz-fit-content;
        height: 290px;
        max-height: 294px;
        padding: 7px;
        box-shadow: 0 20px 40px 4px #e4e4e524;
        margin-top: 10px;
        transition: ease-out 0.4s;
        background: #ace9ff;
        border: 1px dashed rgba(90, 206, 255, 1);
        border-radius: 4px;

        .ai-d-title {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          width: 100%;
          height: -moz-fit-content;
          height: fit-content;
          margin: 0;
          padding: 1px 3px 2px 2px;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw * 14 / 1920);
          color: #313733;
          letter-spacing: 0;
          font-weight: 400;

          .ai-d-title-p {
            flex-grow: 1;
            line-height: 16px;
            text-align: left;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #313733;
            letter-spacing: 0;
            font-weight: 400;
            margin-bottom: 10px;
          }

          img {
            width: 18px;
            height: 18px;
            cursor: pointer;
          }
        }

        .ai-d-body {
          width: 100%;
          height: calc(100% - 44px);
          overflow: hidden;
          background: #ffffff;
          border-radius: 4px;

          .hints-control {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
            padding: 14px 14px 0;
            height: 30px;

            .hint-icon {
              flex-grow: 0;
              flex-shrink: 0;
              width: 20px;
              height: 20px;
              margin-right: 6px;
              background-size: contain;
              background-image: url("../assets/icon_fire.png");
            }

            .hint-description {
              font-weight: 600;
              line-height: 14px;
              font-family: SourceHanSansSC-Bold;
              font-size: calc(100vw * 14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 700;
            }
          }

          .ai-tj-body {
            width: 100%;
            // height: 100%;
            // overflow: hidden;
            height: 200px;
            overflow-y: auto;

            /* 垂直滚动条 */
            .ai-tj-item {
              padding: 14px 14px 0;
              line-height: 12px;
              width: 50%;
              text-align: left;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              float: left;
              cursor: pointer;
              height: 30px;
              font-family: PingFangSC-Regular;
              font-size: calc(100vw * 14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 400;
              // &:hover {

              // }
            }
          }
        }
      }

      // ***滚动条样式
      .ai-tj-body::-webkit-scrollbar {
        width: 6px;
        /* 滚动条的宽度 */
      }

      .ai-tj-body::-webkit-scrollbar-track {
        background: #fff;
        /* 滚动条的背景色 */
      }

      .ai-tj-body::-webkit-scrollbar-thumb {
        background: #488aff;
        /* 滚动条的滑块颜色 */
      }

      .fir-textarea {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        margin-top: 14px;
        height: 158px;
        background: #f8f9fd;
        // border: 1px dashed rgba(229, 232, 245, 1);
        border-radius: 4px;

        ::v-deep(.el-textarea__inner) {
          font-size: calc(100vw * 14 / 1920) !important;
          background-color: #f8f9fd !important;
          height: 100% !important;
          font-family: PingFangSC-Regular;
        }
      }

      .fir-textarea-height {
        height: 460px !important;
      }
    }
  }
}

::v-deep(.el-textarea__inner) {
  padding: 16px;
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: 10px;
  height: 10px;
}

.ai-nav {
  // width: 180px;
  // height: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  transition: ease-out 0.4s;

  .title {
    width: 300px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    letter-spacing: 0px;
    height: 20px;
    line-height: 20px;
    padding-top: 20px;
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    width: 100%;
    padding-top: 45px;
    height: calc(100% - calc(100vh * 80 / 1080));
  }
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px dashed rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw * 16 / 1920);
        font-size: calc(100vw * 15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw * 12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px dashed #f0f3fa;
  }

  .options {
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;
      cursor: pointer;

      :first-child {
        margin-top: 0px;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

// 导航项
.nav-item {
  height: calc(100vh * 40 / 1080);
  background: transparent;
  line-height: calc(100vh * 20 / 1080);
  white-space: pre-wrap;
  // background-image: linear-gradient(270deg,
  //     rgba(30, 75, 202, 0.39) 0%,
  //     rgba(59, 130, 234, 0.28) 100%);
  // border: 1px dashed rgba(255, 255, 255, 0.2);
  // border-radius: 4px;
  background: #f4f6f8;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
  cursor: pointer;
  // margin: 0 auto;
  margin-top: calc(100vh * 20 / 1080);
  margin-left: calc(100vw * 100 / 1920);
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #9094a5;
  border-radius: 20px;
  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-family: PingFangSC-Semibold;
    letter-spacing: 0;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.choose {
  border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  color: #ffffff;
}

.clbutton {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 40px;
}

.clbutton1 {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}

.clbutton2 {
  left: 180px;
}

.clbutton12 {
  // left: 200px;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 280px;
}

.showUp {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 60px;
  height: calc(100vh * 40/ 1080);
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.showUp1 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 6px;
  height: calc(100vh * 40/ 1080);
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 130px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-drawer__header) {
  display: none;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-notification__group) {
  height: auto !important;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: calc(100vw * 14 / 1920) !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.progressClass {
  width: 100%;
  height: 26px;
  position: fixed;
  bottom: 48px;
  right: 0;
  z-index: 999;

  .progress {
    position: absolute;
    right: 52px;
    transition: ease-out 0.4s;
  }
}

::v-deep(.el-progress-bar__innerText) {
  padding: 7px !important;
  color: #fff !important;
}

.menu_label {
  width: 100%;
  border-radius: 4px;
  margin-top: 10px;
  min-height: 158px;
  height: auto;
  padding: 15px 16px;
  // max-height: 158px;
  background: #f8f9fd;
  border: 1px dashed rgba(229, 232, 245, 1);
  border-radius: 4px;
  padding: 15px 0px;
}

.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: 383px;
  word-wrap: break-word;
  text-align: left;
  margin-left: 5px;
  margin-bottom: 5px;
  float: left;
}

.el-main .ai-body .tab-item-fir .menu_label:active {
  background: #fff;
  border: 1px dashed #4170f6;
}

// .el-main .ai-body .tab-item-fir .menu_label:focus {
//   background: #fff;
//   border: 1px dashed #4170f6;
// }

.pass_input {
  // float: left;
  width: 100%;
  height: calc(100vh * 40/ 1080);
}

::v-deep(.el-alert__title) {
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-drawer__open .el-drawer.ltr) {
  width: 100% !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0;
}

::v-deep(.el-alert) {
  padding: 8px 8px;
}

.fir-textarea1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: 1px dashed rgba(229, 232, 245, 1);
  border-radius: 4px;
  margin: 20px;
  margin-bottom: 0;
  height: 158px;

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: 13px 18px 33px 16px;
  }
}

::v-deep(.el-collapse) {
  border: 0px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: 1px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40/ 1080);
  border-radius: 4px;
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: 4px;
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: 4px;
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: -10px;
    top: 2px;
    width: 4px;
    height: 15px;
    background: #5585f0;
    border-radius: 2px;
  }
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: 6px;
  /* 滚动条的宽度 */
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
  /* 滚动条的背景色 */
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
  /* 滚动条的滑块颜色 */
}

.elcol-title-left {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 20px;
  margin-left: 10px;
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 48px;
}

.elcol-title-text {
  // float: left;
  padding-left: 10px;
  text-align: left;
  width: 40%;
  height: 100%;
}

.elcol-title-text2 {
  font-size: calc(100vw * 14 / 1920);
  color: #d1d7de;
  // float: left;
  width: 75px;
}

.elcol-input {
  float: left;
  width: calc(60% - 75px);
  border: none !important;
}

/* 整个滚动条 */
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 10px;
  /* 对应横向滚动条的宽度 */
  height: 10px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: gray;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #fff;
  border-radius: 32px;
}
.el-icon {
  --color: inherit;
  height: 1em;
  width: 1em;
  line-height: 1em;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  fill: currentColor;
  color: var(--color);
  font-size: inherit;
  margin-right: 10px;
}
.el-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  height: calc(100vh * 32 / 1080);
  white-space: nowrap;
  cursor: pointer;
  // padding: calc(100vh * 15 / 1080) calc(100vw * 20 / 1920);
  font-size: calc(100vw * 14 / 1920);
  // color: var(--el-button-text-color);
  // text-align: center;
  // box-sizing: border-box;
  // outline: 0;
  // transition: .1s;
  // font-weight: var(--el-button-font-weight);
  // -webkit-user-select: none;
  // -moz-user-select: none;
  // -ms-user-select: none;
  // user-select: none;
  // vertical-align: middle;
  // -webkit-appearance: none;
  // background-color: var(--el-button-bg-color);
  // border: var(--el-border);
  // border-color: var(--el-button-border-color);
  // padding: 8px 15px;
  // font-size: var(--el-font-size-base);
  // border-radius: var(--el-border-radius-base);
}
.el-input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  height: calc(100vh * 32 / 1080);
  line-height: calc(100vh * 32 / 1080);
  font-size: calc(100vw * 14 / 1920);
  outline: 0;
  padding: 0 calc(100vw * 15 / 1920);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
// .el-dialog__header{
// display: flex;
//     flex-direction: row;
//     flex-shrink: 0;
//     font-size: 1em;
//     font-weight: 700;

//     background-color: #f5f7fa;
//     border-radius: var(--vxe-border-radius) var(--vxe-border-radius) 0 0;
//     -webkit-user-select: none;
//     -moz-user-select: none;
//     user-select: none;
// }
// .el-dialog__header {
//     padding: 20px 20px 10px;
//     background-color: red !important;
// }
:deep(.el-dialog__header) {
  padding: 15px 20px 10px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  font-weight: 700;
  // border-bottom: 1px solid var(--vxe-modal-border-color);
  // background-color: var(--vxe-modal-header-background-color);
  border-radius: var(--vxe-border-radius) var(--vxe-border-radius) 0 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
:deep(.el-dialog__title) {
  font-size: calc(100vw * 15 / 1920);
  color: #606266;
}
:deep(.el-form .el-form-item__label) {
  font-weight: 700;
  // margin-right:-100px;
}
:deep(.el-form-item) {
  margin-bottom: 20px;
}
:deep(.el-form-item__label) {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: calc(100vw * 14 / 1920);
  color: #606266;
  line-height: calc(100vh * 40/ 1080);
  box-sizing: border-box;
}
.el-form-item {
  position: relative;
}
.el-form-item .username.el-input {
  position: absolute;
  top: 1px;
  left: 25px;
  width: 150px;
}
.el-form-item .username.el-input input {
  // position: absolute;
  // top: 1px;
  // left: 25px;
  // width: 150px;
  border: none;
  height: 34px;
  line-height: 34px;
  padding: 0 15px;
  // font-size: calc(100vw * 14 / 1920);
}
::v-deep(.el-table .cell) {
  line-height: calc(100vh * 23 / 1080) !important;
  padding-left: calc(100vw * 10 / 1920) !important;
  padding-right: calc(100vw * 10 / 1920) !important;
}
::v-deep .el-table .el-table__cell {
  padding: calc(100vh * 12 / 1080) 0;
  font-size: calc(100vw * 14 / 1920);
}
</style>