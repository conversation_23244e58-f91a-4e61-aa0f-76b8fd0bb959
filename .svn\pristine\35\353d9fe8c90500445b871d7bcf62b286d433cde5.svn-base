<!-- 本页为管理员的账户管理-->
<template>
  <div class="work-container" v-loading="con_loading">
    <el-container>
      <div
        :style="
          navShow
            ? 'width: -calc(100vw * 10 / 1920)'
            : 'width: calc(100vw * 10 / 1920)'
        "
        class="ejdhl"
      >
        <div
          class="ai-nav"
          :style="
            navShow
              ? 'width: calc(100vw * 300 / 1920)'
              : 'width: calc(100vw * 64 / 1920)'
          "
        >
          <div class="nav-list">
            <div
              @click="clickgrzx()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon204a.png" alt="" />
              <p v-if="navShow">我的信息</p>
            </div>
            <div
              @click="clickzhgl()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon201a.png" alt="" />
              <p v-if="navShow">账户管理</p>
            </div>
            <div
              @click="clickgosygl()"
              class="nav-item choose"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/术语1.png" alt="" />
              <p v-if="navShow">术语管理</p>
            </div>
           <div
              @click="clickmb()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon-jj.png" alt="" />
              <p v-if="navShow">模板管理</p>
            </div> 
            <!-- <div
              @click="clickpb()"
              class="nav-item"
              :style="navShow ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'"
            >
              <img src="../assets/icon-jj.png" alt="" />
              <p v-if="navShow">排板管理</p>
            </div> -->
            <!-- <div
              @click="clickrw()"
              class="nav-item"
              :style="navShow ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'"
            >
              <img src="../assets/check.png" alt="" />
              <p v-if="navShow"> 核稿管理</p>
            </div> -->
            <div
              @click="clickzskgl()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon202a.png" alt="" />

              <p v-if="navShow">范文管理</p>
            </div>
            <div
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
              @click="clickTopLd()"
            >
              <img src="../assets/icon17.png" alt="" />
              <p v-if="navShow">返回系统</p>
            </div>
            <!-- <div
              @click="clickszbg()"
              class="nav-item"
              :style="navShow ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'"
            >
              <img src="../assets/icon202a.png" alt="" />
              <p v-if="navShow">写作</p>
            </div> -->
          </div>
        </div>
      </div>
      <el-header class="el-header1">
        <div class="ai-header">
          <div class="flex">
            <div class="ai-left-bar-li actived"></div>
          </div>
        </div>
      </el-header>
      <el-main style="transition: ease-out 0.4s; display: flex">
        <div
          style="
            margin-top: calc(100vh * 15 / 1080);
            height: calc(100vh * 944 / 1080);
            width: 100%;
          "
        >
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-left: calc(100vw * 35 / 1920);
              height: calc(100vh * 35 / 1080);
            "
          >
            <div>
              <button
                @click="adddia = true"
                data-v-819514c6=""
                aria-disabled="false"
                type="button"
                class="el-button el-button--primary el-button--default"
              >
                <i class="el-icon"
                  ><svg
                    viewBox="0 0 1024 1024"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill="currentColor"
                      d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                    ></path></svg></i
                ><span class="">新增术语</span>
              </button>
              <!-- <button @click="adddiacs = true" data-v-819514c6="" aria-disabled="false" type="button"
              class="el-button el-button--primary el-button--default">
              <i class="el-icon"><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                  <path fill="currentColor"
                    d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z">
                  </path>
                </svg></i><span class="">修改术语</span>
            </button> -->
              <button
                @click="batchDelete"
                data-v-5317e0d1=""
                aria-disabled="false"
                type="button"
                class="el-button el-button--danger el-button--default"
              >
                <i class="el-icon"
                  ><svg
                    viewBox="0 0 1024 1024"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill="currentColor"
                      d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                    ></path></svg></i
                ><span class="">批量删除</span>
              </button>
            </div>
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: space-between;
              "
            >
              <el-select
                v-model="selectedOption"
                placeholder="请选择"
                style="
                  width: calc(100vw * 150 / 1920);
                  margin-right: calc(100vw * 10 / 1920);
                  height: calc(100vh * 32 / 1080);
                "
                @change="handleSelectChang2"
                clearable
                @clear="handleClear"
              >
                <el-option
                  v-for="item in options"
                  :key="item.words_typeId"
                  :label="item.words_type"
                  :value="item.words_typeId"
                ></el-option>
              </el-select>

              <div class="multi-input" style="width: calc(100vw * 500 / 1920)">
                <el-tag
                  v-for="(tag, index) in tags"
                  :key="index"
                  closable
                  @close="removeTag(index)"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  v-model="inputValue"
                  class="input-new-tag"
                  size="small"
                  placeholder="请输入搜索内容"
                  @keyup.enter.native="addTag"
                  @blur="addTag"
                  v-if="showInput"
                ></el-input>
                <div class="plusBtn" @click="handleSearch">
                  <!-- <el-button>搜索</el-button> -->
                  <i class="el-icon-plus"></i>
                </div>
              </div>
            </div>
          </div>

          <div
            style="
              margin-left: calc(100vw * 35 / 1920);
              margin-top: calc(100vh * 10 / 1080);
              height: calc(100vh * 850 / 1080);
              width: calc(100% - calc(100vw * 35 / 1920)); /* 减去左右边距 */
              overflow: hidden; /* 防止内容溢出 */
            "
          >
            <div style="height: 100%; display: flex; flex-direction: column">
              <el-table
                stripe
                border
                @selection-change="handleSelectionChange"
                :default-sort="{ prop: 'date', order: 'descending' }"
                style="width: 100%"
                height="calc(100vh * 850 / 1080)"
                :data="this.tableData1"
                :header-cell-style="{
                  background: '#ebf2fb',
                }"
              >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column label="序号" sortable type="index" width="80">
                </el-table-column>
                <el-table-column prop="words_name" sortable label="术语名称">
                </el-table-column>
                <el-table-column prop="words_type" sortable label="术语类型">
                </el-table-column>
                <el-table-column prop="cjsj" sortable label="创建时间">
                </el-table-column>
                <el-table-column prop="gxsj" sortable label="更新时间">
                </el-table-column>
                <el-table-column
                  prop=""
                  label="操作"
                  min-width="150"
                  align="center"
                >
                  <template v-slot="aa">
                    <el-button
                      style="margin-right: 0px"
                      type="text"
                      size="medium"
                      @click="(formedit = aa.row), (dialogVisibleedit = true)"
                      >修改</el-button
                    >
                    <el-button
                      style="margin-right: 10px; color: red"
                      type="text"
                      size="medium"
                      @click="showdia3(aa.row)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <el-pagination
            style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="this.total"
          >
          </el-pagination>
        </div>
      </el-main>
    </el-container>
    <!-- 新增术语弹框 -->
    <el-dialog title="新增术语" :visible.sync="adddia" @close="handleCloseadd">
      <el-form
        ref="userForm"
        :model="form"
        :rules="formRules"
        :label-width="formLabelWidth"
      >
        <el-form-item label="术语名称" prop="words_name">
          <el-input
            v-model="form.words_name"
            placeholder="请输入术语名称"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="术语类型" prop="words_typeId">
          <el-select
            v-model="form.words_typeId"
            placeholder="请选择术语类型"
            @change="handleSelectChange"
          >
            <el-option
              v-for="item in options"
              :key="item.words_typeId"
              :label="item.words_type"
              :value="item.words_typeId"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCloseadd">取 消</el-button>
        <el-button type="primary" @click="submitForm()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 修改术语弹框 -->
    <el-dialog title="修改术语" :visible.sync="dialogVisibleedit">
      <el-form
        :model="formedit"
        ref="userForm1"
        :rules="formRules1"
        :label-width="formLabelWidth"
      >
        <el-form-item label="术语名称" prop="words_name">
          <el-input
            v-model="formedit.words_name"
            placeholder="请输入术语名称"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="术语类型" prop="words_typeId">
          <el-select
            v-model="formedit.words_type"
            placeholder="请选择术语类型"
            @change="handleSelectChang1"
          >
            <el-option
              v-for="item in options"
              :key="item.words_typeId"
              :label="item.words_type"
              :value="item.words_typeId"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCloseedit"> 取 消</el-button>
        <el-button type="primary" @click="submitForm1()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import App from "../App";
import Wordcloud from "../components/Wordcloud.vue";
import { VueEditor } from "vue2-editor";
import CustomSteps from "../components/Step.vue";
//   import store from '../store/index'
import { mapState } from "vuex";
// ****************************
import Quill from "quill";
let BlockEmbed = Quill.import("blots/block/embed");
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute(
      "style",
      "padding: 20px; border: 1px dashed #488aff; color:red; position: relative;"
    );
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement("button");
    insertButton.innerHTML = "应用";
    insertButton.className = "btn-insert";
    insertButton.style.position = "absolute";
    // 按钮颜色
    insertButton.style.backgroundColor = "#488aff";
    // 字体为白色
    insertButton.style.color = "white";
    // insertButton.style.top = '10px';
    insertButton.style.right = "70px";
    insertButton.addEventListener("click", () => {
      // console.log(value.editorClass, '编辑器')
      // console.log(value.newText, '续写内容！')
      // // 这里的位置，是替换的传position，是插入的传position加length
      // console.log(value.Position, '位置')
      // console.log(value.selectedTextlength, '长度？？？？？？？？')
      // console.log(value.editorClass.getSelection(), '光标位置？？？？')
      if (value.flag == 1) {
        value.editorClass.insertText(
          value.Position + value.selectedTextlength,
          value.newText
        );
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement("button");
    cancelButton1.innerHTML = "取消";
    cancelButton1.className = "btn-cancel";
    cancelButton1.style.position = "absolute";
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = "#e64721";
    // 字体为白色
    cancelButton1.style.color = "white";
    cancelButton1.style.right = "10px";
    cancelButton1.addEventListener("click", () => {
      node.remove();
      // alert('取消按钮点击');
      // // 这里可以添加取消按钮的逻辑
    });
    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = "customBlock";
CustomBlock.tagName = "div";

Quill.register({
  "formats/customBlock": CustomBlock,
});
import store from "../store/index";
import {
  insert_handle,
  update_Single_User,
  select_handle_all,
  add_Single_User,
  js,
  fwsc,
  getSecDtDatas,
  getSuccessInfo,
  getSuccessInfoItem,
  getLabel1,
  getLabel2,
  testcheck,
  bcwz,
  rewrite,
  xz,
  cyt,
  get_user,
  getwrite,
  get_login_user,
  initPassWord,
  disabled,
  remove_disabled,
  delete_User,
  selectTermtype, //获取术语类型
  selectWordsByPage, //分页查询
  insertTermtype, //新增术语类型
  deleteTermtype, //删除术语
  updateTermWords, //修改术语
} from "../api/home.js"; // 接口请求
export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id
    username() {
      return this.$store.state.username;
    },
    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 600}px`,
      };
    },
    id() {
      return this.$store.state.id;
    },
    imageSrc() {
      return `data:image/jpeg;base64,${this.base64Image}`; // 注意根据实际的MIME类型替换'image/jpeg'
    },
  },
  data() {
    return {
      options: [], // 下拉选择框的选项
      selectedOption0: null, // 选中的下拉选项
      selectedOption: null, // 选中的下拉选项
      selectedUsers: [], // 用于存储选中的用户名字
      departments: [], // 用于存储从后端获取的处室数据
      adddia: false,
      adddiacs: false,

      tableData1: [],
      inputvalyhm: "",
      sure_passw: "",
      new_passw: "",
      old_passw: "",
      form1: {
        xm: "",
        xb: "",
        age: "",
        szdw: "",
        ssgw: "",
        sjgzly: "",
        ssqy: "",
      },

      form: {
        words_name: "",
        words_typeId: "",
        passWord: "",
        handle_name: "",
        gwmc: "",
        yhlx: "",
      },
      formcs: {
        csm: "",
      },
      formedit: {
        words_type: "",
        words_name: "",
        words_typeId: "",
        gwmc: "",
        handler_name: "",
        id: "",
      },
      // },
      formLabelWidth: "calc(100vw * 100 / 1920)",
      dialogVisible: false,
      dialogVisibleedit: false,
      up_img: false,
      nan_img: false,
      nv_img: false,
      backgroundImage: "", // 默认背景图
      tags: [], // 存储从后端获取的“句式特点”标签
      tags1: [], // 存储从后端获取的“句式特点”标签
      tags2: [], // 存储从后端获取的“句式特点”标签

      base64Image: "",
      xb: "",
      xm: "",
      szdw: "",
      ssgw: "",
      sjgzly: "",
      ssqy: "",
      age: "",
      wcdata: [],
      // 定义变量存词云的返回值，然后用vuex传给词云组件
      fzqwnr: "",
      newText: "",
      newText1: "",
      newText2: "",
      newText3: "",
      xxdialog: false,
      xxcontent: "",
      kxdialog: false,
      sxdialog: false,
      rsdialog: false,
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      selectedText: "",
      con_loading: false,
      loading1: false,
      correctionList: [],
      jydis: true,
      navShow: true,
      textarea: "",
      textarea2: [],
      textarea21: [],
      textarea22: [],

      textarea3: "",
      textarea4: "",
      textarea4: "",
      dialogShow: false,
      dialog2Show: false,
      gzbj: [],
      stepActived: 1,
      loading: false,
      curIndex: "2",
      articles: "",
      artShow: false,
      mask: false,
      maskAll: false,
      /**
       * @description:  功能性按钮
       * @return {*}    传入组件->FunctionTable
       */
      steps: [
        { title: "录入角色" },
        { title: "录入写作风格" },
        { title: "生成词云" },
      ],

      currentStep: 2, // 初始化当前步骤为第二个步骤
      secRecommend: [], // 第二步推荐
      dynamicTags: [],
      inputVisible: true,
      inputValue: "",
      showInput: true,
      resDataItem: [],
      progressPercent: 0,
      fetchResPrams: [],
      buttonShow: true,
      drawer: false,
      direction: "ltr",
      activeNames: [],
      currentPage: 1, // 当前页
      pageSize: 10, // 每页显示条数
      total: 0,

      // 表单验证规则
      formRules: {
        words_name: [
          { required: true, message: "术语名不能为空", trigger: "blur" },
        ],
        words_typeId: [
          { required: true, message: "术语类型不能为空", trigger: "blur" },
        ],
      },
      formRules1: {
        words_name: [
          { required: true, message: "术语名不能为空", trigger: "blur" },
        ],
        words_typeId: [
          { required: true, message: "术语类型不能为空", trigger: "blur" },
        ],
      },
      // };
    };
  },
  components: {
    App,
    // 注册组件
    CustomSteps,
    VueEditor,
    Wordcloud,
  },
  mounted() {
    this.getuser();
    this.getOptions();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 置空搜索框标签
      vm.tags = [];
      // 置空下拉选择框
      vm.selectedOption = null;
      // 获取下拉选择框的选项
      vm.getOptions();
      vm.getuser(1, 10, vm.username, "术语管理", []); // 进入页面时获取数据
    });
  },
  methods: {
    addTag() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.getuser();
      // 调接口！！！！！！！！！！！！
    },
    removeTag(index) {
      this.tags.splice(index, 1);
      this.getuser();
      // 调接口！！！！！！！！！！！！
    },
    handleSearch() {
      this.showInput = true;
      // this.getuser(1, 10);
    },

    // 获取下拉选择框的选项
    async getOptions() {
      const { data } = await selectTermtype();
      console.log(data, "从接口获取的数据");
      if (data.status_code == 200) {
        this.options = data.data;
        console.log(this.options, "获取下拉选择框的选项11111111111");
      }
    },
    // 下拉选择框变化时的处理
    handleSelectChange(value) {
      console.log("选中的值:", value);
      this.form.words_typeId = value;
    },
    handleSelectChang1(value) {
      console.log("选中的值:", value);
      this.formedit.words_typeId = value;
      // this.getuser(this.currentPage, this.pageSize);
    },
    handleSelectChang2(value) {
      console.log("选中的值:", value);
      // this.form.words_typeId = value;
      this.selectedOption = value; // 更新选中的值
      this.getuser(this.currentPage, this.pageSize); // 调用分页查询方法
    },

    handleClear() {
      console.log("清空了选中的值");
      this.selectedOption = null; // 清空选中的值
      this.getuser(this.currentPage, this.pageSize); // 重新加载数据
    },

    // 分页查询

    clickpb() {
      this.$router.push("/pb_manage");
    },
    // clickpb() {
    //   this.$router.push("/pb_manage");
    // },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },

    handleCloseadd() {
      this.form = {
        words_typeId: "",
        words_type: "",
        words_name: "",
      };
      this.adddia = false; // 确保关闭对话框
    },
    handleCloseedit() {
      this.formedit = {
        words_type: "",
        words_name: "",
        words_typeId: "",
      };
      this.dialogVisibleedit = false; // 确保关闭对话框
      this.getuser(this.currentPage, this.pageSize);
    },
    handleClosecs() {
      // 关闭对话框时重置表单

      this.formcs = {
        csm: "",
      };
      console.log(this.formcs.csm, "什么玩应");
      this.formcs.csm = "";
      this.adddiacs = false; // 确保关闭对话框
    },
    addcs() {
      let params = {
        csm: this.formcs.csm,
      };
      let res = insert_handle(params);

      // console.log(res,'新增处室接口的res')
      let x = res.then((data) => {
        // console.log(data.data.status_code);
        if (data.data.status_code == 200) {
          this.$message({
            message: "新增处室成功",
            type: "success",
          });
          this.getszcs();

          this.adddiacs = false;
          this.getuser(this.currentPage, this.pageSize);
          this.formcs.csm = "";
        } else {
          this.$message({
            message: "新增处室失败",
            type: "error",
          });
        }
      });
    },
    // },
    clearInput() {
      this.inputvalyhm = ""; // 清空输入框内容
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    handleSelectionChange(selected) {
      // selected 是当前选中的用户数组
      // this.selectedUsers = selected.map((user) => user.yhm); // 更新 selectedUsers 数组
      this.selectedUsers = selected.map((term) => term.words_id);
      // if (selected.length === 1) {
      //   this.formedit = selected[0]; // 确保 formedit 被正确填充
      //   this.dialogVisibleedit = true;
      // }
    },
    // handleSelectionChange(selected) {
    //   this.selectedUsers = selected.map(term => term.words_id);
    //   if (selected.length === 1 && this.someCondition) { // 增加条件判断
    //     this.formedit = selected[0];
    //     this.dialogVisibleedit = true;
    //   }
    // },

    //新增保存
    submitForm() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          this.adduser(); // 在验证通过后调用添加用户的函数
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //修改保存
    submitForm1() {
      this.$refs.userForm1.validate((valid) => {
        if (valid) {
          this.showdiaedit(); // 在验证通过后调用修改用户的函数
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //新增术语
    async adduser() {
      let params = {
        words_name: this.form.words_name,
        cjrid: this.id,
        words_typeId: this.form.words_typeId,
        // passWord: this.form.passWord,
        // handle_name: this.form.handle_name,
        // gwmc: this.form.gwmc,
        // yhlx: this.form.yhlx,
      };
      let res = await insertTermtype(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "新增成功",
          type: "success",
        });
        this.adddia = false;
        this.getuser(); // 刷新用户列表
      } else {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
      // this.getuser(this.currentPage, this.pageSize);
    },
    //  },
    async handleSwitchChange(row) {
      const status = row.disabled === "禁用" ? "正常" : "禁用"; // 取反状态
      const params = {
        yhm: row.yhm, // 假设每行有个 yhm 字段
      };

      try {
        let res;

        if (status === "禁用") {
          // 调用启用接口
          res = await remove_disabled(params);
        } else {
          // 调用禁用接口
          res = await disabled(params);
        }

        if (res.data.data.code === 10000) {
          this.$message({
            message: status === "禁用" ? "启用成功" : "禁用成功",
            type: "success",
          });
          row.disabled = status; // 更新行状态
        } else {
          this.$message({
            message: res.data.data.message || "操作失败",
            type: "error",
          });
        }
        // 刷新用户列表
        this.getuser(this.currentPage, this.pageSize);
      } catch (error) {
        console.error("状态更新失败", error);
        this.$message({
          message: "状态更新失败",
          type: "error",
        });
      }
    },

    // async getuser() {
    //   let params = {
    //     cjrid: this.id,
    //     yhm: this.words_name,
    //     pageNo: this.currentPage,
    //     pageSize: this.pageSize,
    //     words_name: this.tags,
    //     words_typeId: this.selectedOption,
    //   };
    //   let rescs = await selectWordsByPage(params);
    //   this.tableData1 = rescs.data.data;
    //   console.log(rescs.data.total_records, "分页");
    //   this.total = rescs.data.total_result;
    // },

    //分页查询
    async getuser(pageNo, pageSize) {
      let params = {
        cjrid: this.id,
        yhm: this.words_name,
        pageNo: pageNo,
        pageSize: pageSize,
        words_name: this.tags,
        words_typeId: this.selectedOption,
      };
      let rescs = await selectWordsByPage(params);
      if (rescs.data.status_code == 200) {
        this.$set(this, "tableData1", rescs.data.data); // 使用 this.$set 更新数据
        this.total = rescs.data.total_result;
      } else {
        console.error("分页查询失败", rescs.data.message);
      }
    },

    async sea(c4) {
      // let pam = {
      //   yhm: c4,
      //   pageSize: '',
      //   page: '',
      // };
      this.getuser("", "", c4);
      // this.inputvalyhm = "";
    },
    // handleSizeChange(val) {
    //   this.getuser(this.currentPage, val);
    //   this.pageSize = val;
    //   // console.log(`每页 ${val} 条`);
    // },
    // handleCurrentChange(val) {
    //   // console.log(`当前页: ${val}`);
    //   this.getuser(val, this.pageSize);
    //   this.currentPage = val;
    // },

    handleSizeChange(newSize) {
      this.pageSize = newSize; // 更新每页显示条数
      this.getuser(this.currentPage, this.pageSize); // 调用分页查询
    },

    handleCurrentChange(newPage) {
      this.currentPage = newPage; // 更新当前页码
      this.getuser(this.currentPage, this.pageSize); // 调用分页查询
    },

    clickgrzx() {
      this.$router.push("/grzx1");
    },
    clickzhgl() {
      this.$router.push("/grzx2");
    },
    clickgosygl() {
      this.$router.push("/sygl");
    },

    // 初始化
    async showdia(c1) {
      let params = {
        yhm: c1.yhm,
      };
      let res = await initPassWord(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "初始化成功",
          type: "success",
        });
      } else {
        this.$message({
          message: "初始化失败",
          type: "error",
        });
      }
    },

    // 修改
    async showdiaedit() {
      this.dialogVisibleedit = true;
      let params = {
        words_name: this.formedit.words_name,
        words_typeId: this.formedit.words_typeId,
        cjrid: this.id,
        words_id: this.formedit.words_id,
      };
      let res = await updateTermWords(params);
      console.log(res, "我是其他接口的res");
      if (res.data.status_code == 200) {
        this.$message({
          message: "修改成功",
          type: "success",
        });
        this.dialogVisibleedit = false;
        this.getuser(this.currentPage, this.pageSize);
      } else {
        this.$message({
          message: "修改失败",
          type: "error",
        });
      }
      this.getuser();
    },
    //删除单条数据
    async showdia3(c1) {
      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的术语, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          try {
            let params = {
              words_id: [c1.words_id],
            };

            let res = await deleteTermtype(params);
            console.log(res, "删除删除删除删除");

            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的用户
              // this.getuser(this.currentPage, this.pageSize); // 刷新用户列表
              this.getuser(); // 刷新用户列表
              this.currentPage = 1; // 刷新当前页
            } else {
              this.$message({
                message: res.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
      // this.getuser();
      // this.getOptions();
      // this.getuser(this.currentPage, this.pageSize);
    },

    // 批量删除
    async batchDelete() {
      // 检查是否有选中的用户
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择用户！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的术语, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            // userName: this.selectedUsers, // 将选中的用户名数组发送给后端
            words_id: this.selectedUsers,
          };

          try {
            let res = await deleteTermtype(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的用户
              this.getuser(this.currentPage, this.pageSize);
              this.currentPage = 1; // 刷新当前页
            } else {
              this.$message({
                message: res.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async remove_disabled() {
      // 检查是否有选中的用户
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择用户！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将启用选择的用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            yhm: this.selectedUsers, // 将选中的用户名数组发送给后端
          };

          try {
            let res = await remove_disabled(params);
            if (res.data.data.code == 10000) {
              this.$message({
                message: "启用成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的用户
              this.getuser(this.currentPage, this.pageSize); // 刷新用户列表
            } else {
              this.$message({
                message: res.data.data.message || "启用失败",
                // message: "启用失败",

                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "启用请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消启用",
            type: "info",
          });
        });
    },
    async disabled() {
      // 检查是否有选中的用户
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择用户！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将禁用选择的用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            yhm: this.selectedUsers, // 将选中的用户名数组发送给后端
          };

          try {
            let res = await disabled(params);
            if (res.data.data.code == 10000) {
              this.$message({
                message: "禁用成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的用户
              this.getuser(this.currentPage, this.pageSize); // 刷新用户列表
            } else {
              this.$message({
                message: res.data.data.message || "禁用失败",
                // message: "启用失败",

                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "禁用请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消禁用",
            type: "info",
          });
        });
    },
    changePwd() {
      // alert('sss')
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    async getcyt() {
      // this.currentStep = 3;
      let params = {
        name: this.username,
      };
      let res = await cyt(params);
      this.wcdata = res.data.data;
      console.log("词云数据:", this.wcdata);
      this.$store.dispatch("updatecy", this.wcdata);
    },
    clickzskgl() {
      this.$router.push("/zsk");
    },
    async onSubmit() {
      let params = {
        name: this.username,
        xm: this.form1.xm,
        xb: this.form1.xb,
        age: this.form1.age,
        szdw: this.form1.szdw,
        ssgw: this.form1.ssgw,
        sjgzly: this.form1.sjgzly,
        ssqy: this.form1.ssqy,
      };
      let res = await js(params);
      this.dialogVisible = false;
      // this.getuser();
      this.getcyt();
      this.$message({
        message: "修改成功",
        type: "success",
      });
      // this.getuser();
    },
    async scfw() {
      console.log("FormData123456:", this.formData.getAll("files[]"));
      if (!this.formData) {
        console.error("没有文件被上传");
        return;
      }
      try {
        let res = await fwsc(this.formData); // 直接传递 formData 进行上传
        console.log("w看一下返回值结果:", res.data.status_code);
        if (res.data.status_code == 200) {
          this.$message({
            message: "上传成功",
            type: "success",
          });
        } else {
          this.$message({
            message: "上传失败",
            type: "error",
          });
        }
        // this.loading = false;
        //         // this.xxdialog=true;
        //         this.xxcontent=this.newText;
        //         // this.kxdialog=true;
        //         // this.sxdialog=true;
        //         // this.rsdialog=true;
        //         // this.textarea2=res.data.data;
        //         // this.textarea3=res.data.data;
        //         // this.textarea4=res.data.data;
        //         // this.textarea5=res.data.data;
        //         // this.textarea6=res.data.data;
        //         // this.textarea7=res.data.data;
        //         // this.textarea8=res.data.data;
        // console.log("上传结果:", res);
      } catch (error) {
        console.error("上传失败:", error);
      }
    },
    beforeAvatarUpload(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "述职报告"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload1(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "心得体会"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload2(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];
      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "领导讲话"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload3(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "工作方案"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload4(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "调研报告"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload5(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "宣传材料"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    togrzx() {
      // alert(1)
      this.$router.push("/grzx1");
    },
    // async getuser(c1) {
    //   let params = {
    //     page: c1,
    //   };
    //   let res2 = await get_login_user(params);
    //   this.tableData1 = res2.data.data;
    //   // console.log(res2.data.total_records, "分页");
    //   this.total = res2.data.total_records;
    // },
    async get_write() {
      let params = {
        name: this.username,
      };
      let res2 = await getwrite(params);
      // console.log(res2.data.data.szqt, "受众群体");
      this.tags = res2.data.data.jstd
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
      this.tags1 = res2.data.data.wzfg
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
      this.tags2 = res2.data.data.szqt
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
    },

    showContextMenu(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener("click", this.hideContextMenu);
      }
    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener("click", this.hideContextMenu);
    },
    // 续写接口
    async sendToBackend() {
      this.loading1 = true;
      this.mask = true;
      let params1 = {
        text: this.selectedText,
        flag: "1",
      };
      let res = await rewrite(params1);
      this.newText = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "续写成功",
          type: "success",
        });
        // 注册自定义样式
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();
        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);
        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText,
              newText: this.newText,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 1,
            },
            Quill.sources.USER
          );
          // 将后端返回的续写文字插入到该位置后面
          // editor.insertText(position + this.selectedText.length, this.newText);
          // editor.formatText(position + this.selectedText.length, this.newText.length, 'color', 'red');
          // // 将光标移动到新插入的文本后面
          // editor.setSelection(position + this.selectedText.length + this.newText.length, 0);
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText);
          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText.length
          );
          console.log("Formatting text length:", this.newText.length);
          editor.formatText(
            editor.getLength() - this.newText.length,
            this.newText.length
          );

          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "续写失败",
          type: "error",
        });
      }
    },

    // 2.扩写接口
    async sendToBackend2() {
      this.loading1 = true;
      this.mask = true;
      let params2 = {
        text: this.selectedText,
        flag: "2",
      };
      let res = await rewrite(params2);
      this.newText1 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "扩写成功",
          type: "success",
        });
        // this.loading = false;
        //         // this.xxdialog=true;
        //         this.xxcontent=this.newText;
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          const position = allText.indexOf(this.selectedText);

          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText1,
              newText: this.newText1,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 2,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText1);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText1.length
          );
          console.log("Formatting text length:", this.newText1.length);
          editor.formatText(
            editor.getLength() - this.newText1.length,
            this.newText1.length
          );
          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "扩写失败",
          type: "error",
        });
      }
    },
    // 3.缩写接口
    async sendToBackend3() {
      this.loading1 = true;
      this.mask = true;
      let params3 = {
        text: this.selectedText,
        flag: "3",
      };
      let res = await rewrite(params3);
      this.newText2 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "缩写成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText2,
              newText: this.newText2,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 3,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText2);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText2.length
          );
          console.log("Formatting text length:", this.newText2.length);
          editor.formatText(
            editor.getLength() - this.newText2.length,
            this.newText2.length
          );
          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "缩写失败",
          type: "error",
        });
      }
    },
    // 4.润色接口
    async sendToBackend4() {
      this.loading1 = true;
      this.mask = true;
      let params4 = {
        text: this.selectedText,
        flag: "4",
      };
      let res = await rewrite(params4);
      this.newText3 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "润色成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText3,
              newText: this.newText3,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 4,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText3);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText3.length
          );
          console.log("Formatting text length:", this.newText3.length);
          editor.formatText(
            editor.getLength() - this.newText3.length,
            this.newText3.length
          );

          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "润色失败",
          type: "error",
        });
      }
    },
    copyText() {
      // 复制逻辑
      console.log("复制");
    },
    pasteText() {
      // 粘贴逻辑
      console.log("粘贴");
    },
    cutText() {
      // 剪切逻辑
      console.log("剪切");
    },
    // }
    async baocun() {
      console.log(this.$route.query.userName);
      let params = {
        wznr: this.textarea4.replace('"', '/"'),
        wzlx: "述职报告",
        userName: this.username,
      };
      let resbcwz = await bcwz(params);
      //  console.log(resbcwz)
      resbcwz.data.status_code == 200
        ? this.$message({
            message: "保存成功,请前往我的文档查看",
            type: "success",
          })
        : this.$message({
            message: "保存失败",
            type: "error",
          });
    },
    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "************************************");
      // this.textarea3 = html;
      // return html;
    },
    getPlainText(html) {
      const div = document.createElement("div");
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    },
    //     getHtmlContent(html) {
    //   const div = document.createElement('div');
    //   div.innerHTML = html;
    //   return div.innerHTML;
    // },

    onCopySuccess() {
      console.log("复制成功");
    },
    onCopyError() {
      console.log("复制失败");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    ignore() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
    },
    highlightChange(text1, text2) {
      console.log(this.textarea4);
      let changeData = text2;
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.textarea4 = this.removetext(this.textarea4);
      this.textarea4 = this.textarea4.replace(text1, changeData);
    },
    highlightError(error, word) {
      this.textarea4 = this.removetext(this.textarea4);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.textarea4 = this.textarea4.replace(error, a);
      console.log(this.textarea4);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(this.$refs.editor.$el, "editorIframe");
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    removetextp(text) {
      let str = text;
      let reg1 = new RegExp(`<p>`, "g");
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</p>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    onCopySuccess() {
      this.$message({
        message: "内容已复制到剪贴板！",
        type: "success",
      });
    },
    onCopyError() {
      this.$message({
        message: "复制失败，请手动复制!",
        type: "warning",
      });
    },

    async jiaoyan() {
      this.textarea4 = this.removetext(this.textarea4);
      this.con_loading = true;
      // let text = this.removetextp(this.textarea4)
      testcheck({
        test_all: this.textarea4,
      }).then(async (data) => {
        this.correctionList = data.data.data;
        this.con_loading = false;
        if (this.correctionList.length == 0) {
          this.jydis = true;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          console.log(data, "619");
          this.jydis = false;
        }
      });
    },
    handleClosedrawer() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    // async jiaoyan(){
    //   this.jydis = false
    // },
    save() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.jydis = true;
    },
    fanhui() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    handleStepClick(index) {
      this.lock++;
      this.activeIndex = index; // 更新activeIndex的值
    },
    upShow() {
      this.navShow = !this.navShow;
    },
    async textareaFocus() {
      let data = await getLabel1();
      this.gzbj = data.data;
      this.dialogShow = true;
    },
    closeDialog() {
      this.dialogShow = false;
    },
    textarea2Focus() {
      this.dialog2Show = true;
    },
    closeDialog2() {
      this.dialog2Show = false;
    },
    async getText(e) {
      console.log(e);
      let params = {
        label: e,
      };
      let data = await getLabel2(params);
      console.log(data, "583");
      this.secRecommend = data.data;
      this.textarea = e;
      this.textarea2 = [];
    },
    getText2(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea2.push(e);
        this.dynamicTags.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm(e);
      } else {
        console.log(111);
      }
    },
    getText21(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea21.push(e);
        this.dynamicTags1.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm1(e);
      } else {
        console.log(111);
      }
    },
    getText22(e) {
      if (!this.dynamicTags2.includes(e)) {
        this.textarea22.push(e);
        this.dynamicTags2.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm2(e);
      } else {
        console.log(111);
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickszbg() {
      this.$router.push("/xz");
    },

    //角色
    clickTopld() {
      this.$router.push("/grzx");
    },
    clickTopLd() {
      this.$router.push("/sfw");
    },
    //   提示词
    clickToptsc() {
      this.$router.push("/tsc");
    },
    firstNextStep() {
      if (this.textarea == "") {
        this.$notify({
          title: "提示",
          message: "请填写工作背景",
          type: "warning",
        });
      } else if (this.textarea2.length == 0) {
        this.$notify({
          title: "提示",
          message: "请填写工作要点关键词",
          type: "warning",
        });
      } else {
        this.mask = true;
        if (this.mask == true) {
          this.getInfo(this.textarea, this.dynamicTags);
        }
      }
    },
    async getInfo(c1, c2) {
      this.loading = true;
      let params = {
        test_1: c1,
        test_2: c2,
      };
      let res = await getSecDtDatas(params);
      // console.log(res.data.status_code,'我是状态码11111')
      // console.log(res.data.message,'我是提示33332222')
      if (res.data.status_code == 200) {
        // 弹出提示
        this.$message({
          message: res.data.message,
          type: "success",
        });
        this.textarea3 = "";

        // console.log(res.data, 55555555);
        this.textarea3 = res.data.data;
        // console.log(this.textarea3,66666);
        // 这里放置需要执行的逻辑或调用其他方法
        this.buttonShow = true;
        // this.stepActived = 2;
        this.currentStep = 2;
        this.loading = false;
        this.mask = false;
        this.curIndex = "2";
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10001) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10002) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    repeatStep() {
      this.loading = true;
      this.mask = true;
      if (this.mask == true) {
        this.getInfo(this.textarea, this.dynamicTags);
      }
    },
    success() {
      this.artShow = true;
      this.loading = true;
      // 这里放置需要执行的逻辑或调用其他方法
      if (this.textarea3 == "") {
        this.$notify({
          title: "提示",
          message: "大纲内容为空，无法生成",
          type: "warning",
        });
      } else {
        this.progressPercent = 0;
        this.getSuccessInfo(this.textarea3, this.textarea);
      }
    },
    // 依次请求接口
    async fetchResData(index, nextIndex) {
      if (index >= this.fetchResPrams.length) {
        this.fetchData(nextIndex);
        return;
      }
      console.log(this.fetchResPrams[index], "675");
      let pams = {
        string: this.fetchResPrams[index],
        id_name: this.textarea,
      };
      const response = await getSuccessInfoItem(pams);
      if (response.data.status_code == 200) {
        // this.$message({
        //   message: response.data.message,
        //   type: 'success'
        // });
        this.textarea4 += response.data.data;

        let totalLength = 0;
        let allSubArrays = this.resDataItem.filter(
          (item) => typeof item != "string"
        );
        allSubArrays.forEach((subArray) => {
          totalLength += subArray.length;
        });
        let kuai = 100 / totalLength;
        this.progressPercent += Math.floor(kuai);
        await this.fetchResData(index + 1, nextIndex);
      } else if (response.data.status_code == 500) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10001) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10002) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      }
    },
    // 一段式生成
    async fetchData(index) {
      if (index >= this.resDataItem.length) {
        this.progressPercent = 100;
        this.maskAll = false;
        this.buttonShow = false;
        return;
      }
      if (typeof this.resDataItem[index] == "string") {
        this.textarea4 += this.resDataItem[index];
        await this.fetchData(index + 1);
      } else {
        this.fetchResPrams = this.resDataItem[index];
        this.fetchResData(0, index + 1);
      }
      // this.stepActived = 3;
      this.currentStep = 3;
      this.loading = false;
    },
    async getSuccessInfo(c1, c2) {
      this.loading = true;
      this.maskAll = true;
      let params = {
        string: c1,
        work: c2,
      };
      let res = await getSuccessInfo(params);
      if (res.data.status_code == 200) {
        // this.$message({
        //   message: res.data.message,
        //   type: 'success'
        // })
        this.textarea4 = "";
        this.resDataItem = res.data.data;
        this.fetchData(0);
        this.currentStep = 3;
        // this.stepActived = 3;
        this.loading = false;
        // }
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    clickTab(i) {
      this.curIndex = i;
      // }
    },
  },
  watch: {
    xb(newVal) {
      if (newVal) {
        this.backgroundImage = "../img/bga.png"; // 男背景图
      } else {
        this.backgroundImage = "../img/hahu3.png"; // 女背景图
      }
    },
  },
  // };
};
</script>
<style lang="less" scoped>
.dChangePwd .footerInline .tsFont {
  width: 100%;
  line-height: 30px;
  margin-top: 0;
  color: #f60;
}

.ml {
  margin-left: 100px;
}

.ml1 {
  margin-left: 120px;
}

.ml2 {
  margin-left: 100px;
}

::v-deep .el-descriptions-item__label.has-colon::after {
  content: "";
  margin-top: 20px;
}

.el-tag {
  background: #f5f8fc;
  border-radius: 12px;
  padding: 4px;
  // padding-left: 2px;
  // padding-right: 5px;
  // height: 32px;
  // line-height: 32px;
}

.btnr {
  margin-top: 190px;
}

.btn-group {
  background: url(../img/bga.png) no-repeat center;
  background-size: 45%;
}

.btn-group1 {
  // margin-right: 500px;
  background: url(../img/hahu3.png) no-repeat center;
  background-size: 45%;
}

// .btn-group1{
//     background: url(../img/hahu3.png) no-repeat left;
//     background-size: 40%;
//     margin-left: -500px;
// }
// .btn-group {
//   // background: url(../img/bga.png) no-repeat center;
//   background: url(this.backgroundImage) no-repeat center;
//   background-size: 45%;
// }
.left1 {
  background: #ffffff;
  border-radius: 8px;
}

.next_but {
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  width: 130px;
  height: calc(100vh * 40/ 1080);
  border-radius: 20px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 600;

  &:hover {
    color: #ffffff;
  }

  &:active {
    color: #ffffff;
  }
}

.right {
  background: url(../img/bga.png) no-repeat center;
  // width: 88%;
  // height: 100%;
  background-size: 70%;
  //  background: #FFFFFF;
  // border-radius: 8px;
}

.el-select {
  width: 100%;
}

.el-input {
  // width: 600px;
  // margin-left: -120px;
}

.fir-kuai2 {
  margin-top: px;
  margin-right: 10px;

  width: 6px;
  height: 16px;
  float: left;
  background: #4081ff;
  border-radius: 1.5px;
}

.el-upload__tip {
  margin-top: -20px;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}

.parent-container .avatar-uploader .el-upload {
  border: 1px #d9d9d9 dashed !important;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: calc(100vw * 28 / 1920);
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-left-bar-li {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh *  50 / 1080);
  width: calc(100vw *  1000/ 1920);
  // color: #000;

  line-height: 16px;
  white-space: nowrap;
  cursor: pointer;
  // z-index: 9999;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 19 / 1920);
  // color: #000000;
  letter-spacing: 0;
  text-align: center;
  background: url(../assets/jstext.png) no-repeat center;
}
.fr {
  float: right;
}

.work-container {
  height: 100%;

  .result-container {
    padding: 30px;
  }
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

.ejdhl {
  margin-top: calc(100vh * 43 / 1080);
  background-color: #fff;
  height: calc(100vh - (100vh * 43 / 1080));
}

.el-header1 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  background: url(../assets/jsbg.png) no-repeat center;
  background-color: #fff;

  .ai-header {
    width: 100%;
  }
}

.el-main {
  background-color: #f8f9fd;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: calc(100% - calc(100vw * 300 / 1920));
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: calc(100vh * 20 / 1080) calc(100vw * 10 / 1920);
  overflow: hidden;
}

::v-deep(.el-textarea__inner) {
  padding: 16px;
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: 10px;
  height: 10px;
}

.ai-nav {
  height: calc(100% - calc(100vh * 80 / 1080));
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  transition: ease-out 0.4s;

  .title {
    width: 300px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    letter-spacing: 0px;
    height: 20px;
    line-height: 20px;
    padding-top: 20px;
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    width: 100%;
    padding-top: 45px;
    height: calc(100% - calc(100vh * 80 / 1080));
  }
}
::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

// 导航项
.nav-item {
  height: calc(100vh * 40 / 1080);
  background: transparent;
  line-height: calc(100vh * 20 / 1080);
  white-space: pre-wrap;
  // background-image: linear-gradient(270deg,
  //     rgba(30, 75, 202, 0.39) 0%,
  //     rgba(59, 130, 234, 0.28) 100%);
  // border: 1px dashed rgba(255, 255, 255, 0.2);
  // border-radius: 4px;
  background: #f4f6f8;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
  cursor: pointer;
  // margin: 0 auto;
  margin-top: calc(100vh * 20 / 1080);
  margin-left: calc(100vw * 100 / 1920);
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #9094a5;
  border-radius: 20px;
  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-family: PingFangSC-Semibold;
    letter-spacing: 0;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.choose {
  border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  color: #ffffff;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 130px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-drawer__header) {
  display: none;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-notification__group) {
  height: auto !important;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: calc(100vw * 14 / 1920) !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

::v-deep(.el-progress-bar__innerText) {
  padding: 7px !important;
  color: #fff !important;
}

.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: 383px;
  word-wrap: break-word;
  text-align: left;
  margin-left: 5px;
  margin-bottom: 5px;
  float: left;
}

.el-main .ai-body .tab-item-fir .menu_label:active {
  background: #fff;
  border: 1px dashed #4170f6;
}

::v-deep(.el-alert__title) {
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-drawer__open .el-drawer.ltr) {
  width: 100% !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0;
}

::v-deep(.el-alert) {
  padding: 8px 8px;
}

.fir-textarea1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: 1px dashed rgba(229, 232, 245, 1);
  border-radius: 4px;
  margin: 20px;
  margin-bottom: 0;
  height: 158px;

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: 13px 18px 33px 16px;
  }
}

::v-deep(.el-collapse) {
  border: 0px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: 1px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40/ 1080);
  border-radius: 4px;
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: 4px;
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: 4px;
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: -10px;
    top: 2px;
    width: 4px;
    height: 15px;
    background: #5585f0;
    border-radius: 2px;
  }
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: 6px;
  /* 滚动条的宽度 */
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
  /* 滚动条的背景色 */
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
  /* 滚动条的滑块颜色 */
}

.elcol-title-left {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 20px;
  margin-left: 10px;
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 48px;
}

/* 整个滚动条 */
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 10px;
  /* 对应横向滚动条的宽度 */
  height: 10px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: gray;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #fff;
  border-radius: 32px;
}

.el-icon {
  --color: inherit;
  height: 1em;
  width: 1em;
  line-height: 1em;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  fill: currentColor;
  color: var(--color);
  font-size: inherit;
  margin-right: 10px;
}

.el-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  height: calc(100vh * 32 / 1080);
  white-space: nowrap;
  cursor: pointer;
  padding: calc(100vh * 15 / 1080) calc(100vw * 20 / 1920);
  font-size: calc(100vw * 14 / 1920);
}

:deep(.el-dialog__header) {
  padding: 15px 20px 10px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  font-weight: 700;
  // border-bottom: 1px solid var(--vxe-modal-border-color);
  // background-color: var(--vxe-modal-header-background-color);
  border-radius: var(--vxe-border-radius) var(--vxe-border-radius) 0 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

:deep(.el-dialog__title) {
  font-size: calc(100vw * 15 / 1920);
  color: #606266;
}

:deep(.el-form .el-form-item__label) {
  font-weight: 700;
  // margin-right:-100px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: calc(100vw * 14 / 1920);
  color: #606266;
  line-height: calc(100vh * 40/ 1080);
  box-sizing: border-box;
}

.multi-input {
  height: calc(100vh * 36 / 1080);
  box-sizing: border-box;
  border: 1px solid rgb(220, 223, 230);
  border-radius: 4px;
  position: relative;
  padding-left: calc(100vw * 3 / 1920);
  padding-right: calc(100vw * 32 / 1920);
  display: flex;
  align-items: center;
  margin-left: 0px;
  display: flex;
  align-items: center;
  //justify-content: space-between;
}
::v-deep(.el-input__inner) {
  height: calc(100vh * 32 / 1080);
  line-height: calc(100vh * 32 / 1080);
  padding: 0 calc(100vw * 15 / 1920);
  font-size: calc(100vw * 14 / 1920);
}
::v-deep(.el-input--suffix .el-input__inner) {
  padding-right: calc(100vw * 30 / 1920);
}
::v-deep(.el-input__icon) {
  line-height: calc(100vh * 32 / 1080);
  width: calc(100vw * 25 / 1080);
}
.plusBtn {
  position: absolute;
  right: calc(100vw * 3 / 1920);
  top: calc(100vh * 3 / 1080);
  height: calc(100vh * 24 / 1080);
  width: calc(100vw * 15 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-sizing: border-box;
  border: 1px solid rgb(64, 158, 255);
  border-radius: 4px;
}

.input-new-tag {
  width: calc(100vw * 200 / 1920);
  margin-left: calc(100vw * 1 / 1920);
  font-size: calc(100vw * 12 / 1920);
  height: calc(100vh * 32 / 1080);
}
.input-new-tag .el-input__inner {
  height: calc(100vh * 30 / 1080);
  line-height: calc(100vh * 30 / 1080);
}
.el-tag.is-closable {
  padding-right: 5px;
}
.el-tag {
  height: calc(100vh * 24 / 1080);
  padding: 0 calc(100vw * 9 / 1920);
  font-size: calc(100vw * 12 / 1920);
  margin: 0px;
  border-radius: 4px;
  // display: flex;
  line-height: calc(100vh * 24 / 1080);
  // margin-right: 2px;
  // margin-top: 5px;
}
.el-tag .el-tag__close {
  margin-left: 6px;
}

.el-icon-serch {
  height: 24px;
  line-height: 24px;
  padding: 0 7px;
}

.LgscCon {
  width: 1500px;
  height: auto;
  margin: 0 auto;

  .button {
    width: 108px;
    height: 58px;
    background: #4c91ff;
    border-radius: 0px 100px 100px 0px;
    cursor: pointer;
  }

  img {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }

  p {
    line-height: 58px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}
::v-deep .el-table .el-table__cell {
  padding: calc(100vh * 12 / 1080) 0;
  font-size: calc(100vw * 14 / 1920);
}
</style>