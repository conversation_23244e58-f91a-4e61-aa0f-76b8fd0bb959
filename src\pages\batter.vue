<!-- ai写公文写作 -->
<template>
  <div class="work-container" v-loading="con_loading">
    <el-drawer
      title=""
      :visible.sync="drawer"
      :direction="direction"
      style="width: 80%"
      :before-close="handleClosedrawer"
    >
      <div style="display: flex; height: 94%">
        <div style="width: 50%; height: calc(100% - 30px); margin-top: 20px">
          <div
            style="
              margin-left: 20px;
              height: 100%;
              overflow: overlay;
              background-color: rgb(255, 255, 255);
              padding: 0px 20px;
              font-size: calc(100vw * 14 / 1920);
            "
            v-html="textarea4"
            contenteditable="true"
            class="result-content"
          ></div>
        </div>
        <div
          class="result-container"
          style="
            width: calc(50% - 40px);
            height: calc(100% - 30px);
            background-color: rgb(225, 231, 243);
            margin: 20px;
            padding-top: 16px;
          "
        >
          <div
            class="result-title"
            style="
              text-align: left;
              font-size: calc(100vw * 16 / 1920);
              margin-bottom: 10px;
              font-weight: bold;
            "
          >
            全部校对结果
          </div>
          <div style="height: 96%; overflow: overlay" class="result-content">
            <div v-for="(item, index) in correctionList">
              <el-collapse
                v-model="activeNames"
                @change="highlightError(item.source, index)"
                style="margin-bottom: 10px"
                accordion
              >
                <el-collapse-item :name="index">
                  <template slot="title">
                    <div style="width: 96%">
                      <div class="elcol-title">
                        <div
                          class="elcol-title-left"
                          :style="{
                            backgroundColor: getBackgroundColor(index),
                          }"
                        ></div>
                        <!-- <p class="elcol-title-text">{{ item.source }}</p> -->
                        <el-input
                          class="elcol-input"
                          v-model="item.source"
                          placeholder="请输入内容"
                        ></el-input>
                        <p class="elcol-title-text2">建议替换</p>
                        <!-- <p class="elcol-title-text">{{ item.target }}</p> -->
                        <el-input
                          class="elcol-input"
                          v-model="item.target"
                          placeholder="请输入内容"
                        ></el-input>
                      </div>
                    </div>
                  </template>
                  <div
                    style="
                      height: calc(100vh * 40 / 1080);
                      text-align: left;
                      padding-left: 20px;
                      line-height: calc(100vh * 40 / 1080);
                      border-bottom: 1px solid #e1e7f3;
                    "
                  >
                    拼写：政治语素错误
                  </div>
                  <!-- <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div> -->
                  <div style="height: 8px; margin-top: 6px">
                    <span
                      @click="ignore()"
                      style="
                        float: right;
                        margin-right: 10px;
                        color: red;
                        cursor: pointer;
                      "
                      >忽略</span
                    >
                    <span
                      @click="highlightChange(item.source, item.target)"
                      style="
                        float: right;
                        margin-right: 10px;
                        color: #66b1ff;
                        cursor: pointer;
                      "
                      >替换</span
                    >
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div
          type="primary"
          class="fr clbutton1"
          style="margin-right: 20px; cursor: pointer"
          @click="save"
        >
          关 闭
        </div>
        <!-- <el-button type="danger" @click="fanhui"></el-button> -->
      </div>
    </el-drawer>
    <SideNavigation :navShow="navShow" />

    <el-container>
      <el-header
        style="transition: ease-out 0.4s"
        :style="
          navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
        "
      >
        <div class="ai-header">
          <div class="ai-bar">
            <HeadNavigation />
          
          </div>
        </div>
      </el-header>
      <el-main
        style="transition: ease-out 0.4s"
        :style="
          navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
        "
      >
        <div
          class="ai-gai"
          v-loading="loading"
          element-loading-text="请稍候..."
          element-loading-background="rgba(255, 255, 255, 0.7)"
          :style="navShow ? 'width: 770px' : 'width: 528px'"
          style="transition: ease-out 0.4s,z-index: 999"
          v-if="mask"
        ></div>
        <div
          class="ai-gai"
          v-loading="loading2"
          element-loading-text="正在刷新，请稍候..."
          element-loading-background="rgba(255, 255, 255, 0.7)"
          :style="navShow ? 'width: 770px' : 'width: 528px'"
          style="transition: ease-out 0.4s"
          v-if="mask"
        ></div>
        <div v-if="maskAll" class="progressClass">
          <el-progress
            class="progress"
            :style="
              navShow
                ? 'width: calc(100% - 870px)'
                : 'width: calc(100% - 637px)'
            "
            :text-inside="true"
            :stroke-width="26"
            :percentage="progressPercent"
          ></el-progress>
        </div>
        <div
          class="ai-gai"
          v-loading="loading1"
          element-loading-text="生成中，请稍候..."
          element-loading-background="rgba(255, 255, 255, 0.7)"
          :style="
            navShow
              ? 'width: 960px; margin-left:850px;height:700px;margin-bottom:100px;'
              : 'width: 600px;height:400px;'
          "
          style="transition: ease-out 0.4s"
          v-if="mask"
        ></div>

        <div class="ai-body">
          <div class="ai-body-left">
            <div class="ai-body-left-top">
              <div v-if="jydis">
                <custom-steps
                  :steps="steps"
                  :current-step="currentStep"
                ></custom-steps>
                <div class="ai-tab">
                  <div
                    @click="clickTab('1')"
                    class="tab-item"
                    :class="{ activedTab: curIndex == 1 }"
                  >
                    基本信息
                  </div>
                  <div
                    @click="clickTab('2')"
                    class="tab-item"
                    :class="{ activedTab: curIndex == 2 }"
                  >
                    AI大纲
                  </div>
                  <div
                    @click="clickTab('3')"
                    class="tab-item"
                    :class="{ activedTab: curIndex == 3 }"
                  >
                    参考素材
                  </div>
                </div>
                <div class="tab-item-fir" v-if="curIndex === '1'">
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">填写文种（必填）</p>
                  </div>

                  <el-input
                    class="fir-textarea"
                    type="textarea"
                    placeholder=""
                    v-model="textarea"
                    @focus="textareaFocus"
                    show-word-limit
                  >
                  </el-input>
                  <div class="ai-dialog" v-if="dialogShow">
                    <div class="ai-d-title">
                      <p class="ai-d-title-p">
                        请在文种部分填写文章对应的文种信息，建议您参考直接或选择推荐的文种，生成效果更佳
                      </p>
                      <img
                        src="../assets/close.png"
                        @click="closeDialog"
                        alt=""
                      />
                    </div>
                    <div class="ai-d-body">
                      <div class="hints-control">
                        <div class="hint-icon hot"></div>
                        <div class="hint-description">选择文种</div>
                      </div>
                      <div class="ai-tj-body">
                        <p
                          @click="getText(item)"
                          class="ai-tj-item"
                          v-for="(item, index) in gzbj"
                          :key="index"
                        >
                          {{ item }}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">填写标题（必填）</p>
                  </div>
                  <el-input
                    class="fir-textarea"
                    type="textarea"
                    placeholder=""
                    v-model="title"
                    show-word-limit
                  >
                  </el-input>
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">内容主题（非必填）</p>
                  </div>
                  <el-input
                    class="fir-textarea"
                    type="textarea"
                    placeholder=""
                    v-model="content_theme"
                    show-word-limit
                  >
                  </el-input>
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">
                      填写摘要（摘要和一级标题至少填写一项）
                    </p>
                  </div>
                  <el-input
                    class="fir-textarea"
                    type="textarea"
                    placeholder=""
                    v-model="abstract"
                    show-word-limit
                  >
                  </el-input>
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">
                      填写一级标题（摘要和一级标题至少填写一项）
                    </p>
                  </div>

                  <el-input
                    class="fir-textarea"
                    type="textarea"
                    placeholder=""
                    v-model="textarea1"
                    @focus="textareaFocus1"
                    show-word-limit
                  >
                  </el-input>

                  <!-- <div class="ai-dialog" v-if="dialogShow1">
                    <div class="ai-d-title">
                      <p class="ai-d-title-p">
                        请在一级标题部分填写文章对应的信息，建议您参考直接或选择推荐的一级标题，生成效果更佳
                      </p>
                      <img
                        src="../assets/close.png"
                        @click="closeDialog"
                        alt=""
                      />
                    </div>
                    <div class="ai-d-body">
                      <div class="hints-control">
                        <div class="hint-icon hot"></div>
                        <div class="hint-description">选择一级标题</div>
                      </div>

                      <div class="ai-tj-body">
                        <p
                          @click="getText1()"
                          style="
                            white-space: pre-wrap;
                            text-align: left;
                            font-family: PingFangSC-Regular;
                            font-size: calc(100vw *  14 / 1920);
                            color: #313733;
                            font-weight: 400;
                            cursor: pointer;
                          "
                        >
                          {{ responseText }}
                        </p>
                      </div>
                    </div>
                  </div> -->
                </div>
                <div class="tab-item-fir" v-if="curIndex === '2'">
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">大纲</p>
                  </div>
                  <el-alert
                    class="fir-alert"
                    title="您可以根据自己的需要修改大纲或选中部分大纲右键重新生成"
                    type="success"
                  >
                  </el-alert>
                  <!-- <vue-editor
             style="background-color: #f8f9fd !important;font-size: 14px !important;font-family:PingFangSC-Regular !important;color:#606266 !important;"
            v-model="textarea3"
            ref="editor"
           @contextmenu.native="showContextMenuadd"
           class="add-editor"
           ></vue-editor> -->
                  <div class="editor-container">
                    <vue-editor
                      style="
                        background-color: #f8f9fd !important;
                        font-size: 14px !important;
                        font-family: PingFangSC-Regular !important;
                        color: #606266 !important;
                      "
                      v-model="textarea3"
                      ref="editor"
                      @contextmenu.native="showContextMenuadd"
                      class="add-editor"
                    ></vue-editor>
                    <div class="overlay" v-if="isOverlayVisible"></div>
                  </div>
                  <div
                    v-if="contextMenuVisibleadd"
                    class="context-menu"
                    :style="contextMenuStyleadd"
                  >
                    <ul>
                      <li @click="sendToBackendadd()">
                        <i class="el-icon-refresh"></i>
                        重新生成选中部分
                      </li>
                    </ul>
                  </div>
                  <el-button
                    v-if="show_change_button"
                    @click="changedg()"
                    type="primary"
                    style="
                      border-radius: 20px;
                      background-image: linear-gradient(
                        107deg,
                        #3a6bc6 0%,
                        #488aff 100%
                      );
                      box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                      margin-top: 10px;
                    "
                  >
                    修改大纲
                  </el-button>
                </div>
                <div class="tab-item-fir" v-if="curIndex === '3'">
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">关联参考素材</p>
                  </div>

                  <el-alert
                    class="fir-alert"
                    title="根据自己的需要匹配参考素材，这样有助于更加精准的生成文章~"
                    type="success"
                  >
                  </el-alert>

                  <el-button
                    @click="dia_sure = true"
                    type="primary"
                    style="
                      border-radius: 20px;
                      background-image: linear-gradient(
                        107deg,
                        #3a6bc6 0%,
                        #488aff 100%
                      );
                      box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                      margin-top: 10px;
                      margin-right: 255px;
                    "
                  >
                    +上传参考素材
                  </el-button>
                  <div
                    style="margin-top: 10px;display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  row-gap: calc(100vh * 1 / 1080);
  width: 100%;
  height: calc(100vh * 290 / 1080);
  max-height: calc(100vh * 294 / 1080);
  padding: calc(100vh * 7 / 1080);
    calc(100vh * 4 / 1080) #e4e4e524;
  margin-top: calc(100vh * 10 / 1080);
  transition: ease-out 0.4s;
  background: #ace9ff;
  border: calc(100vw * 1 / 1920) solid rgba(90, 206, 255, 1);
  border-radius: calc(100vh * 4 / 1080);"
                  >
                    <el-table
                      stripe
                      border
                      :default-sort="{ prop: 'date', order: 'descending' }"
                      style="
                        margin-top: 10px;
                        overflow-y: scroll;
                        height: 230px;
                      "
                      :data="this.tableData2"
                      :header-cell-style="{
                        background: '#ebf2fb',
                      }"
                      class="table1"
                    >
                      <el-table-column
                        prop="file_name"
                        sortable
                        label="文件名"
                        width="240"
                      >
                      </el-table-column>
                      <el-table-column
                        prop=""
                        label="操作"
                        width="160"
                        align="center"
                      >
                        <template v-slot="aa">
                          <el-button
                            style="font-size: 18px"
                            type="text"
                            class="el-icon-view"
                            @click="showdia4_1(aa.row)"
                          ></el-button>
                          <el-button
                            style="font-size: 18px"
                            type="text"
                            class="el-icon-delete"
                            @click="showdia3_1(aa.row)"
                          ></el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!-- <el-pagination
            @size-change="handleSizeChange1"
            @current-change="handleCurrentChange1"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="this.total1"
          >
          </el-pagination> -->
                  </div>
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">上传参考文本</p>
                  </div>
                  <el-alert
                    class="fir-alert"
                    title="根据自己的需要上传参考文本，这样有助于更加精准的生成文章~"
                    type="success"
                  >
                  </el-alert>
                  <el-upload
                    class="upload-demo"
                    @change="handleFileChange"
                    :limit="1"
                    :before-upload="beforeAvatarUpload1"
                    ref="upload"
                    action="#"
                    :show-file-list="false"
                    :file-list="fileList"
                    accept=".docx,.doc,.pdf"
                  >
                    <el-button
                      type="primary"
                      style="
                        border-radius: 20px;
                        background-image: linear-gradient(
                          107deg,
                          #3a6bc6 0%,
                          #488aff 100%
                        );
                        box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                        margin-top: 10px;
                        margin-right: 255px;
                      "
                    >
                      +上传参考文本
                    </el-button>
                  </el-upload>
                  <div
                    v-if="fileList.length > 0"
                    style="
                      margin-left: -20px;
                      color: #409eff;
                      font-size: calc(100vw * 15 / 1920);
                      margin-top: 10px;
                    "
                  >
                    {{ fileList[0].name }}
                  </div>
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">是否需要大纲</p>
                  </div>
                  <!-- <el-radio v-model="radio" label=1>是</el-radio>
  <el-radio v-model="radio" label=2>否</el-radio> -->
                  <el-radio-group v-model="radio">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </div>
              </div>

              <div class="tab-item-fir" v-else>
                <div
                  style="height: 100%; overflow: overlay; margin-top: 20px"
                  class="result-content"
                >
                  <div v-for="(item, index) in correctionList">
                    <!-- <template slot="title"> -->
                    <!-- <div class="elcol-title">
              <p class="elcol-title-text">{{ item.source }}</p>
              <p class="elcol-title-text2">建议替换</p>
              <p class="elcol-title-text">{{ item.target }}</p> -->
                    <!-- <el-input class="elcol-input" v-model="input" placeholder="请输入内容"></el-input> -->
                    <!-- </div> -->
                    <!-- </template> -->
                    <el-collapse
                      v-model="activeNames"
                      @change="highlightError(item.source, item.wordNo)"
                      style="margin-bottom: 10px"
                      accordion
                    >
                      <el-collapse-item :name="index">
                        <template slot="title">
                          <div style="width: 96%">
                            <div class="elcol-title" style="display: flex">
                              <div
                                class="elcol-title-left"
                                :style="{
                                  backgroundColor: getBackgroundColor(index),
                                }"
                              ></div>
                              <p class="elcol-title-text elcol-input">
                                {{ item.wordNo }}
                              </p>
                              <!-- <el-input :title="item.wordNo" class="elcol-input" v-model="item.wordNo"
                                placeholder="请输入内容" style="width: 40%;"></el-input> -->
                              <p class="elcol-title-text2">建议替换</p>
                              <!-- <p class="elcol-title-text">{{ item.target }}</p> -->
                              <el-input
                                :title="item.wordYes"
                                class="elcol-input"
                                v-model="item.wordYes"
                                placeholder="请输入内容"
                                style="width: 40%"
                              ></el-input>
                            </div>
                          </div>
                        </template>
                        <div
                          style="
                            height: calc(100vh * 40 / 1080);
                            text-align: left;
                            padding-left: 20px;
                            line-height: calc(100vh * 40 / 1080);
                            border-bottom: 1px solid #e1e7f3;
                          "
                        >
                          {{ item.eq }}
                        </div>
                        <!-- <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div> -->
                        <div style="height: 8px; margin-top: 6px">
                          <span
                            @click="ignore()"
                            style="
                              float: right;
                              margin-right: 10px;
                              color: red;
                              cursor: pointer;
                            "
                            >忽略</span
                          >
                          <span
                            @click="highlightChange(item.source, item.target)"
                            style="
                              float: right;
                              margin-right: 10px;
                              color: #66b1ff;
                              cursor: pointer;
                            "
                            >替换</span
                          >
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </div>
                <div
                  type="primary"
                  class="clbutton2 clbutton1"
                  style="margin-right: 20px; cursor: pointer"
                  @click="save"
                >
                  关 闭
                </div>
              </div>
            </div>
            <div class="ai-body-left-bottom" v-if="jydis == true">
              <div
                v-if="curIndex == '1'"
                @click="firstNextStep"
                class="ai-body-left-bottom-button"
              >
                下一步：生成大纲
              </div>

              <div v-if="curIndex == '2'" class="ai-body-left-bottom2">
                <div class="repeat" @click="changedg1()">重新生成大纲</div>
                <!-- <div @click="success()" class="ai-body-left-bottom-button"> -->
                <div
                  @click="(curIndex = '3'), (currentStep = 3)"
                  class="ai-body-left-bottom-button"
                >
                  <span>下一步：关联素材</span>
                  <!-- <span v-else>重新生成文章</span> -->
                </div>
              </div>
              <div v-if="curIndex == '3'" class="ai-body-left-bottom">
                <!-- <div class="repeat" @click="changedg1()">重新生成大纲</div> -->
                <!-- <div @click="success()" class="ai-body-left-bottom-button"> -->
                <div @click="success()" class="ai-body-left-bottom-button">
                  <span v-if="buttonShow">下一步：生成文章</span>
                  <span v-else>重新生成文章</span>
                </div>
                <!-- <div
                v-if="curIndex == '1'"
                @click="firstNextStep"
                class="ai-body-left-bottom-button"
              >
                下一步：生成大纲
              </div> -->
              </div>
            </div>
          </div>
          <div class="ai-body-right">
            <div class="ai-body-start" v-if="!artShow">
              <!-- <div class="ai-body-start" v-if="false"> -->
              <!-- <div class="pic_bkg">
                <div class="pic_font">欢迎使用AI写作</div>
                <div class="title_message">
                  采用AI大模型智能生成文章，仅需3步即可一键成文，快去试试吧~
                </div>
              </div>

              <div class="pic_step"></div> -->
              <div class="pic_bkg1"></div>
            </div>

            <div class="ai-body-art" v-else>
              <vue-editor
                class="fir-textarea fir-textarea-max"
                v-model="textarea4"
                ref="editor"
                @contextmenu.native="showContextMenu"
              ></vue-editor>
              <div
                v-if="contextMenuVisible"
                class="context-menu"
                :style="contextMenuStyle"
              >
                <ul>
                  <!-- el-icon-edit -->
                  <li @click="sendToBackend()">
                    <i class="el-icon-edit"></i>

                    续写
                  </li>
                  <!-- el-icon-circle-plus-outline -->
                  <li @click="sendToBackend2()">
                    <i class="el-icon-circle-plus-outline"></i>

                    扩写
                  </li>
                  <!-- el-icon-remove-outline -->
                  <li @click="sendToBackend3()">
                    <i class="el-icon-remove-outline"></i>
                    缩写
                  </li>
                  <li @click="sendToBackend4()">
                    <i class="el-icon-magic-stick"></i>
                    润色
                  </li>
                  <li @click="sendToBackend5()">
                    <i class="el-icon-refresh"></i>
                    重新生成选中部分
                  </li>
                </ul>
              </div>
              <el-dialog
                title="续写内容"
                :visible.sync="this.xxdialog"
                width="30%"
                :before-close="handleClose"
              >
                <div>{{ this.xxcontent }}</div>
                <span slot="footer" class="dialog-footer">
                  <el-button @click="xxdialog = false">取 消</el-button>
                  <el-button
                    type="primary"
                    @click="(xxdialog = false), insertText()"
                    >插入</el-button
                  >
                </span>
              </el-dialog>
              <div
                style="cursor: pointer"
                v-if="progressPercent == 100"
                class="clbutton"
                v-clipboard:copy="getPlainText(textarea4)"
                v-clipboard:success="onCopySuccess"
                v-clipboard:error="onCopyError"
              >
                复制全文
              </div>

              <div
                style="cursor: pointer"
                v-if="progressPercent == 100"
                class="clbutton1"
                @click="jiaoyan"
              >
                校验
              </div>
              <div
                style="cursor: pointer"
                v-if="progressPercent == 100"
                class="clbutton12"
                @click="baocun()"
              >
                保存
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>

    <el-dialog
      :close-on-click-modal="false"
      title="素材匹配"
      :visible.sync="dialogadd"
      @close="reset()"
      width="97%"
      v-loading="loading"
      element-loading-text="请稍候..."
      element-loading-background="rgba(255, 255, 255, 0.7)"
    >
      <div class="dialog-content">
        <div class="dialog-left">
          <div class="fir-title">
            <div class="fir-kuai"></div>
            <p class="fir-title-p">第一步：选择需要素材的大纲</p>
          </div>
          <el-alert
            class="fir-alert"
            title="您可以勾选需要素材的大纲，并在右侧上传素材进行匹配"
            type="success"
          >
          </el-alert>
          <!-- <el-checkbox-group v-model="checkList" class="checkbox-group">
            <el-checkbox
              v-for="outline in outlines"
              :key="outline.outline_id"
              :label="outline.outline_id"
              :value="outline.outline_id"
            >
              {{ outline.outline_content }}
            </el-checkbox>
          </el-checkbox-group> -->
          <el-checkbox-group v-model="checkList" class="checkbox-group">
            <el-checkbox
              v-for="outline in outlines"
              :key="outline.outline_id"
              :label="outline.outline_id"
              :value="outline.outline_id"
              :disabled="outline.level === 1 || outline.level === 2"
            >
              {{ outline.outline_content }}
            </el-checkbox>
          </el-checkbox-group>
        </div>

        <div class="dialog-right">
          <!-- 右侧内容 -->

          <div class="fir-title">
            <div class="fir-kuai"></div>
            <p class="fir-title-p">第二步：上传素材并匹配</p>
          </div>
          <el-alert
            class="fir-alert"
            title="上传素材文章，并选中对应段落和左侧勾选的大纲进行匹配"
            type="success"
          >
          </el-alert>
          <el-button
            type="text"
            @click="back()"
            style="margin-left: 480px"
            v-if="comeback"
            >【返回】</el-button
          >

          <el-upload
            class="upload-demo"
            :limit="1"
            :before-upload="beforeAvatarUpload"
            ref="upload"
            action="#"
            :show-file-list="false"
            accept=".docx,.doc,.pdf"
          >
            <!-- 上传成功过一次就消失，点过x之后就有/都删除没了的时候 -->
            <el-button size="small" type="primary" v-if="upsc"
              >上传参考素材</el-button
            >
          </el-upload>
          <!-- <div
            
            style="height:525px;overflow:auto;"
            ref="editor"
           @contextmenu.native="showContextMenuadd_dg"
           class="add-editor1"
           >{{textarea3add}}</div> -->
          <vue-editor
            v-model="textarea3add"
            v-if="look"
            ref="editor"
            class="add-editor1"
            @contextmenu.native="showContextMenuadd_dg"
          ></vue-editor>
          <div v-html="highlightedFullText" v-if="high"></div>
          <div
            v-if="contextMenuVisibleadd_dg"
            class="context-menu"
            :style="contextMenuStyleadd"
          >
            <ul>
              <li @click="sendToBackendadd_dg()">
                <i class="el-icon-document-checked"></i>
                确定关联
              </li>
            </ul>
          </div>
          <!-- 只有点修改时出现 -->
          <el-button
            size="small"
            type="primary"
            @click="change_match()"
            v-if="save_change"
            >保存修改</el-button
          >
          <!--  点修改和预览时消失-->
          <!-- <el-button
            size="small"
            type="primary"
            @click="sendToBackendadd_dg()"
            v-if="sure"
            >确定关联</el-button
          > -->
        </div>
        <div class="dialog-right">
          <!-- 右侧内容 -->
          <div class="fir-title">
            <div class="fir-kuai"></div>
            <p class="fir-title-p">历史匹配记录</p>
          </div>
          <div>
            <!-- <el-button type="text" @click="back()">【返回】</el-button> -->
            <el-table
              stripe
              border
              :default-sort="{ prop: 'date', order: 'descending' }"
              style="overflow-y: scroll; height: 610px"
              :data="this.tableData1"
              :header-cell-style="{
                background: '#ebf2fb',
              }"
              class="table1"
            >
              <!-- <el-table-column type="selection"></el-table-column> -->
              <el-table-column prop="outline_content" sortable label="大纲内容">
              </el-table-column>
              <el-table-column
                prop="reference_content"
                sortable
                label="参考内容"
              >
              </el-table-column>
              <el-table-column prop="" label="操作" align="center">
                <template v-slot="aa">
                  <el-button
                    style="font-size: 18px"
                    type="text"
                    class="el-icon-view"
                    @click="showdia4(aa.row)"
                  ></el-button>
                  <el-button
                    style="font-size: 18px"
                    type="text"
                    class="el-icon-edit"
                    @click="showdia5(aa.row)"
                  ></el-button>
                  <el-button
                    style="font-size: 18px"
                    type="text"
                    class="el-icon-delete"
                    @click="showdia3(aa.row)"
                  ></el-button>
                  <!-- <el-button
                    
                    type="text"
                    size="medium"
                    @click="showdia5(aa.row)"
                    >修改</el-button> -->
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="this.total"
          >
          </el-pagination> -->
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dia_sure"
      width="30%"
      :before-close="handleClose"
    >
      <span>关联参考素材后大纲将会被锁定，是否继续操作？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dia_sure = false">取 消</el-button>
        <el-button
          type="primary"
          @click="
            (dia_sure = false),
              (isOverlayVisible = true),
              updg(),
              (dialogadd = true),
              (show_change_button = true)
          "
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import SideNavigation from "../components/SideNavigation.vue"; // 引入侧边导航组件
import HeadNavigation from "../components/HeadNavigation.vue"; // 引入头部导航组件

import { VueEditor } from "vue2-editor";
import CustomSteps from "../components/Step.vue";
import store from "../store/index";
// import { mapState } from "vuex";
// ****************************
import Quill from "quill";
let BlockEmbed = Quill.import("blots/block/embed");
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute(
      "style",
      "padding: 20px; border: 1px solid #488aff; color:red; position: relative;"
    );
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement("button");
    insertButton.innerHTML = "应用";
    insertButton.className = "btn-insert";
    insertButton.style.position = "absolute";
    // 按钮颜色
    insertButton.style.backgroundColor = "#488aff";
    // 字体为白色
    insertButton.style.color = "white";
    // insertButton.style.top = '10px';
    insertButton.style.right = "70px";
    insertButton.addEventListener("click", () => {
      if (value.flag == 1) {
        value.editorClass.insertText(
          value.Position + value.selectedTextlength,
          value.newText
        );
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement("button");
    cancelButton1.innerHTML = "取消";
    cancelButton1.className = "btn-cancel";
    cancelButton1.style.position = "absolute";
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = "#e64721";
    // 字体为白色
    cancelButton1.style.color = "white";
    cancelButton1.style.right = "10px";
    cancelButton1.addEventListener("click", () => {
      node.remove();
    });

    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = "customBlock";
CustomBlock.tagName = "div";
Quill.register({
  "formats/customBlock": CustomBlock,
});
import { EventBus } from "../eventBus"; // 引入事件总线
import {
  select_write_referencefiles_Bypage,
  select_write_referencefile,
  delete_write_referencefile,
  delete_write_basic_related_information,
  delete_outline_reference_info,
  select_outline_reference_info,
  update_outline_reference_info,
  upload_reference_material,
  add_correspondence,
  identify_content_document,
  add_number_to_outline,
  prepare_write_basic,
  getLabel1_new,
  getSecDtDatas,
  getSuccessInfo,
  getSuccessInfoItem,
  getLabel1,
  getLabel2,
  testcheck,
  bcwz,
  rewrite,
  sx2,
  getwz,
  insert_genre,
  select_write_type,
  recommend_firstLevel,
  recommended_outline,
  regenerate_outline_part,
  generate_article,
  get_outline_reference_info,
  delete_meetThemeContent,
} from "../api/home.js"; // 接口请求
import { mapState } from "vuex";

export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id
    highlightedText() {
      const { start, end, textarea3add } = this;
      // 移除之前的高亮
      // const plainText = textarea3add.replace(/<span[^>]*>(.*?)<\/span>/g, '$1')
      if (start !== null && end !== null) {
        const startText = textarea3add.slice(0, start);
        const highlightedText = textarea3add.slice(start, end);
        const endText = textarea3add.slice(end);

        return `${startText}<span style="background-color: yellow;">${highlightedText}</span>${endText}`;
      }
      return textarea3add; // 如果没有高亮，则返回原文本
    },

    // },

    username() {
      return this.$store.state.username;
    },
    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 600}px`,
      };
    },
    contextMenuStyleadd() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 100}px`,
      };
    },
    ...mapState(["id"]),
  },
  data() {
    return {
      fileList: [],
      high: false,
      look: true,
      fullText: "", // 从后端接收的全文
      highlightText: "", // 从后端接收的需要高亮的部分
      highlightedFullText: "", // 处理后的全文，包含高亮部分
      comeback: false,
      show_change_button: false,
      upsc: true,
      save_change: false,
      sure: false,
      dele: false,
      dia_sure: false,
      isOverlayVisible: false, // 遮罩层
      editid: 0,
      edibutton: false,
      start: "",
      end: "",
      // selectedTextlength: 0,
      tableData1: [], // 历史匹配记录
      tableData2: [], // 历史素材记录

      currentPage: 1, // 当前页
      pageSize: 10, // 每页显示条数
      total: 0,
      total1: 0,

      radio: 1,
      basic_id: 0,
      checkList: [],
      outlines: [],
      dialogadd: false,
      responseText: "",
      fzqwnr: "",
      newText: "",
      newText1: "",
      newText2: "",
      newText3: "",
      xxdialog: false,
      xxcontent: "",
      kxdialog: false,
      sxdialog: false,
      rsdialog: false,
      contextMenuVisible: false,
      contextMenuVisibleadd: false,
      contextMenuVisibleadd_dg: false,
      contextMenuPosition: { x: 100, y: 0 },
      selectedText: "",
      con_loading: false,
      loading1: false,
      loading2: false,
      correctionList: [],
      jydis: true,
      navShow: true,
      textarea: "",
      textarea1: "",
      title: "",
      content_theme: "",
      abstract: "",
      textarea2: [],
      textarea3: "",
      textarea3add: "",
      article_id: 0,
      textarea4: "",
      textarea4: "",
      dialogShow: false,
      dialogShow1: false,

      gzbj: [],
      stepActived: 1,
      loading: false,
      curIndex: "1",
      articles: "",
      artShow: false,
      mask: false,
      maskAll: false,
      /**
       * @description:  功能性按钮
       * @return {*}    传入组件->FunctionTable
       */
      steps: [
        { title: "基本信息" },
        { title: "大纲" },
        { title: "素材" },
        { title: "文章" },
      ],
      currentStep: 1,
      secRecommend: [], // 第二步推荐
      dynamicTags: [],
      inputVisible: true,
      inputValue: "",
      resDataItem: [],
      progressPercent: 0,
      fetchResPrams: [],
      buttonShow: true,
      drawer: false,
      direction: "ltr",
      activeNames: [],
      // asyncStatus: true
    };
  },
  components: {
    CustomSteps,
    VueEditor,
    SideNavigation,
    HeadNavigation
  },
  mounted() {},

  methods: {
    clickpb() {
      this.$router.push("/pb_manage");
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    handleFileChange(file, fileList) {
      // 处理文件变化的逻辑，添加清空之前文件列表的逻辑
      this.fileList = [file]; // 仅显示当前文件
    },
    highlightFullText() {
      // 使用正则表达式替换全文中的高亮文本
      const regex = new RegExp(
        `(${this.escapeRegExp(this.highlightText)})`,
        "gi"
      );
      this.highlightedFullText = this.fullText.replace(
        regex,
        '<span class="highlight">$1</span>'
      );
    },
    escapeRegExp(string) {
      // 转义正则表达式中的特殊字符
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    },
    // }
    back() {
      this.comeback = false;
      this.save_change = false;
      this.sure = true;
      this.high = false;
      this.look = true;
    },
    changedg() {
      // this.isOverlayVisible=true;
      let params = {
        basic_id: this.basic_id,
      };
      this.$confirm("此操作将永久清除原大纲关联的参考素材，是否继续？")
        .then(() => {
          delete_write_basic_related_information(params).then((res) => {
            if (res.data.status_code == 200) {
              this.$message({
                message: "清除成功",
                type: "success",
              });
              this.isOverlayVisible = false;
              this.get_wz();
              this.fileList = []; // 清空文件列表
              this.show_change_button = false;
              this.basic_id = 0;
              // this.getuser(this.currentPage, this.pageSize);
            } else {
              this.$message({
                message: "清除失败",
                type: "error",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            message: "已取消清除",
            type: "info",
          });
        });
    },
    changedg1() {
      // this.isOverlayVisible=false;
      let params = {
        basic_id: this.basic_id,
      };
      this.$confirm("此操作将永久清除原大纲关联的参考素材，是否继续？")
        .then(() => {
          this.isOverlayVisible = false;
          delete_write_basic_related_information(params).then((res) => {
            if (res.data.status_code == 200) {
              this.$message({
                message: "清除成功",
                type: "success",
              });
              this.isOverlayVisible = false;
              this.repeatStep();
              this.get_wz();
              // this.getuser(this.currentPage, this.pageSize);
              this.show_change_button = false;
              this.fileList = [];
              this.basic_id = 0;
            } else {
              this.$message({
                message: "清除失败",
                type: "error",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            message: "已取消清除",
            type: "info",
          });
        });
      // this.repeatStep();
    },
    async change_match() {
      this.selectedText = window.getSelection().toString();
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const startOffset = range.startOffset; // 光标起始位置
        const endOffset = range.endOffset; // 光标结束位置
        let pams = {
          outline_reference_id: this.editid,
          //
          outline_ids: this.checkList,
          content: this.selectedText,
          article_id: this.article_id,
          basic_id: this.basic_id,
          start: startOffset,
          end: endOffset,
        };
        let res = await update_outline_reference_info(pams);
        if (res.data.status_code == 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.get_his_match();
        } else {
          this.$message({
            message: "修改失败",
            type: "error",
          });
        }
      } else {
        console.error("没有选中文本。");
      }
    },
    async showdia5(c1) {
      // 保存修改按钮出现
      this.comeback = true;

      this.save_change = true;
      this.sure = false;
      this.dele = true;
      this.high = false;
      this.look = true;
      let pams = {
        outline_reference_id: c1.outline_reference_id,
        basic_id: this.basic_id,
      };
      let res = await select_outline_reference_info(pams);
      //         file_content         全文内容
      // outline_content      所有大纲和对应的id
      // outline_id           本次关联关系选择的大纲对应的id
      // reference_content    本次关联选中的素材内容
      // start                选中的素材内容在原文的  起始位置
      // end                  选中的素材内容在原文的  终止位置
      if (res.data.status_code == 200) {
        this.checkList = res.data.data.outline_id;
        this.textarea3add = res.data.data.file_content;
        this.start = res.data.data.start;
        console.log(this.start, "选中的素材内容在原文的  起始位置");
        this.end = res.data.data.end;
        this.outlines = res.data.data.outline_content;
      } else {
        this.$message({
          message: "获取失败",
          type: "error",
        });
      }
      this.editid = c1.outline_reference_id;
      this.edibutton = true;
    },
    async showdia4(c1) {
      this.look = false;
      this.high = true;
      this.comeback = true;
      this.upsc = false;
      this.save_change = false;
      this.sure = false;
      this.dele = false;
      let pams = {
        outline_reference_id: c1.outline_reference_id,
        basic_id: this.basic_id,
      };
      let res = await select_outline_reference_info(pams);
      //         file_content         全文内容
      // outline_content      所有大纲和对应的id
      // outline_id           本次关联关系选择的大纲对应的id
      // reference_content    本次关联选中的素材内容
      // start                选中的素材内容在原文的  起始位置
      // end                  选中的素材内容在原文的  终止位置
      if (res.data.status_code == 200) {
        this.checkList = res.data.data.outline_id;
        this.textarea3add = res.data.data.file_content;
        this.start = res.data.data.start;
        console.log(this.start, "选中的素材内容在原文的  起始位置");
        this.end = res.data.data.end;
        this.outlines = res.data.data.outline_content;
        this.fullText = res.data.data.file_content;
        this.highlightText = res.data.data.reference_content;
        this.highlightFullText();
      } else {
        this.$message({
          message: "获取失败",
          type: "error",
        });
      }
    },
    async showdia4_1(c1) {
      this.upsc = false;
      this.sure = true;

      let pams = {
        article_id: c1.article_id,
        basic_id: this.basic_id,
      };
      let res = await select_write_referencefile(pams);
      if (res.data.status_code == 200) {
        // this.checkList = res.data.data.outline_id;
        this.textarea3add = res.data.data.current_segment;
        this.outlines = res.data.data.outline;
        // 调用一下12
        this.get_his_match_c(c1);
        this.dialogadd = true;
      } else {
        this.$message({
          message: "获取失败",
          type: "error",
        });
      }
    },
    async get_wz() {
      let pams = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        basic_id: this.basic_id,
      };
      let res = await select_write_referencefiles_Bypage(pams);
      this.tableData2 = res.data.data;
      this.total1 = res.data.total_records;
    },
    async showdia3(c1) {
      let pams = {
        outline_reference_ids: [c1.outline_reference_id],
      };
      let res = await delete_outline_reference_info(pams);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.get_his_match();
        this.back();
      } else {
        this.$message({
          message: "删除失败",
          type: "error",
        });
        this.back();
      }
    },
    async showdia3_1(c1) {
      let pams = {
        article_id: c1.article_id,
      };
      let res = await delete_write_referencefile(pams);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.get_wz();
      } else {
        this.$message({
          message: "删除失败",
          type: "error",
        });
      }
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.getuser(this.currentPage, val);
      // console.log(`每页 ${val} 条`);
      this.pageSize = val;
    },
    handleSizeChange1(val) {
      // console.log(`每页 ${val} 条`);
      this.get_wz(this.currentPage, val);
      // console.log(`每页 ${val} 条`);
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.getuser(val, this.pageSize);
      this.currentPage = val;
    },
    handleCurrentChange1(val) {
      // console.log(`当前页: ${val}`);
      this.get_wz(val, this.pageSize);
      this.currentPage = val;
    },
    async get_his_match() {
      let pams = {
        article_id: this.article_id,
        basic_id: this.basic_id,
        // pageNo: this.currentPage,
        // pageSize: this.pageSize,
        content: "",
      };
      let res = await get_outline_reference_info(pams);
      this.tableData1 = res.data.data;
      this.total = res.data.total_records;
    },
    // },
    async get_his_match_c(c1) {
      let pams = {
        article_id: c1.article_id,
        basic_id: this.basic_id,
        // pageNo: this.currentPage,
        // pageSize: this.pageSize,
        content: "",
      };
      let res = await get_outline_reference_info(pams);
      this.tableData1 = res.data.data;
      this.total = res.data.total_records;
    },
    reset() {
      this.comeback = false;
      this.checkList = [];
      this.outlines = [];
      this.dialogadd = false;
      this.textarea3add = "";
      // 重置按钮值 关联选中 上传
      this.sure = false;
      this.save_change = false;
      this.dele = true;
      this.upsc = true;
      this.tableData1 = []; // 历史匹配记录
      this.total = 0;
      this.high = false;
      this.look = true;
    },
    beforeAvatarUpload(files) {
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "reference_file",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("basic_id", this.basic_id); // 添加额外字段type

          this.formData = formData; // 将 formData 存储在组件的属性中

          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload1(files) {
      this.fileList = [files]; // 只保留当前上传的文件
      // this.loading = true;
      this.mask = true;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "material_file",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("basic_id", this.basic_id); // 添加额外字段
          formData.append("type", this.textarea); // 添加额外字段type
          formData.append("title", this.title); // 添加额外字段type
          formData.append("abstract", this.abstract); // 添加额外字段type
          formData.append("first_level_title", this.textarea1); // 添加额外字段type
          // content_theme
          formData.append("content_theme", this.content_theme); // 添加额外字段type

          this.formData = formData; // 将 formData 存储在组件的属性中

          this.scfw1();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    async scfw() {
      this.loading = true;
      this.mask = true;
      if (!this.formData) {
        console.error("没有文件被上传");
        return;
      }
      try {
        let res = await identify_content_document(this.formData); // 直接传递 formData 进行上传
        if (res.data.status_code == 200) {
          this.$message({
            message: "上传成功",
            type: "success",
          });

          // 上传参考素材按钮消失，选中取消按钮出现
          this.sure = true;
          this.dele = true;
          this.upsc = false;
          this.textarea3add = res.data.data;
          this.article_id = res.data.article_id;
          console.log(res.data.article_id, "回来的文章id");
          this.get_wz();
          this.loading = false;
          this.mask = false;
        } else {
          this.$message({
            message: "上传失败",
            type: "error",
          });
          this.loading = false;
        }
      } catch (error) {
        console.error("上传失败:", error);
      }
    },
    async scfw1() {
      this.loading = true;
      this.mask = true;
      if (!this.formData) {
        console.error("没有文件被上传");
        return;
      }
      try {
        this.formData.append("user_id", this.id);
        let res = await upload_reference_material(this.formData); // 直接传递 formData 进行上传
        if (res.data.status_code == 200) {
          this.$message({
            message: "上传成功",
            type: "success",
          });
          this.loading = false;
          this.mask = false;
        } else {
          this.$message({
            message: "上传失败",
            type: "error",
          });
          this.loading = false;
          this.mask = false;
        }
      } catch (error) {
        console.error("上传失败:", error);
      }
    },
    async updg() {
      let pams = {
        basic_id: this.basic_id,
        finally_outline: this.textarea3,
        type: this.textarea,
        title: this.title,
        content_theme: this.content_theme,
        abstract: this.abstract,
        first_level_title: this.textarea1,
      };
      let res = await add_number_to_outline(pams);
      console.log(res, "583");
      this.outlines = res.data.data;
      this.basic_id = res.data.basic_id;
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    // clickTopother() {
    //   this.$router.push("/other");
    // },
    toxg() {
      this.$router.push("/grzx4");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    async sx1() {
      // this.loading2 = true;
      let pams = {
        // username: this.username,
        label: this.textarea,
      };
      let data = await sx2(pams);
      console.log(data, "583");
      this.secRecommend = data.data;
      this.textarea = e;
      this.textarea2 = [];
    },
    async sx() {
      // this.loading2 = true;
      let pams = {
        username: this.username,
      };
      let res = await getLabel1_new(pams);
      this.gzbj = res.data;
      this.dialogShow = true;
    },
    togrzx() {
      this.$router.push("/grzx1");
    },

    // 高亮文本方法
    highlightText(text, color) {
      const editor = this.$refs.editor.quill;
      const allText = editor.getText();
      const position = allText.indexOf(text);
      if (position !== -1) {
        editor.formatText(position, text.length, { color: color });
      }
    },
    showContextMenu(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener("click", this.hideContextMenu);
      }
    },
    showContextMenuadd(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisibleadd = true;
        document.addEventListener("click", this.hideContextMenuadd);
      }
    },

    showContextMenuadd_dg(event) {
      event.preventDefault();

      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuPosition = { x: event.clientX + 300, y: event.clientY };
        this.contextMenuVisibleadd_dg = true;
        document.addEventListener("click", this.hideContextMenuadd_dg);
      }
    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener("click", this.hideContextMenu);
    },
    hideContextMenuadd() {
      this.contextMenuVisibleadd = false;
      document.removeEventListener("click", this.hideContextMenuadd);
    },
    hideContextMenuadd_dg() {
      this.contextMenuVisibleadd_dg = false;
      document.removeEventListener("click", this.hideContextMenuadd_dg);
    },
    // 续写接口
    async sendToBackend() {
      this.loading1 = true;
      this.mask = true;
      let params1 = {
        text: this.selectedText,
        flag: "1",
      };
      let res = await rewrite(params1);
      this.newText = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "续写成功",
          type: "success",
        });
        // 注册自定义样式
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();
        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);
        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText,
              newText: this.newText,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 1,
            },
            Quill.sources.USER
          );
          this.$nextTick(() => {
            // 确保 DOM 更新完成后再进行操作
            this.highlightText(this.newText2, "red");
          });
        }
      } else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: "续写失败",
          type: "error",
        });
      }
    },

    // 2.扩写接口
    async sendToBackend2() {
      this.loading1 = true;
      this.mask = true;
      let params2 = {
        text: this.selectedText,
        flag: "2",
      };
      let res = await rewrite(params2);
      this.newText1 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "扩写成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          const position = allText.indexOf(this.selectedText);

          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText1,
              newText: this.newText1,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 2,
            },
            Quill.sources.USER
          );
        }
        this.$nextTick(() => {
          // 确保 DOM 更新完成后再进行操作
          this.highlightText(this.newText2, "red");
        });
      } else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: "扩写失败",
          type: "error",
        });
      }
    },
    // 3.缩写接口
    async sendToBackend3() {
      this.loading1 = true;
      this.mask = true;
      let params3 = {
        text: this.selectedText,
        flag: "3",
      };
      let res = await rewrite(params3);
      this.newText2 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "缩写成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText2,
              newText: this.newText2,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 3,
            },
            Quill.sources.USER
          );
        }
        this.$nextTick(() => {
          // 确保 DOM 更新完成后再进行操作
          this.highlightText(this.newText2, "red");
        });
        // else {
        //   // // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
        //   // editor.insertText(editor.getLength(), this.newText2);

        //   // // 将新插入的文本格式化为红色
        //   // console.log('Formatting text at position:', editor.getLength() - this.newText2.length);
        //   // console.log('Formatting text length:', this.newText2.length);
        //   // editor.formatText(editor.getLength() - this.newText2.length, this.newText2.length,);
        //   // editor.setSelection(editor.getLength(), 0);
        // }
        this.$nextTick(() => {
          // 确保 DOM 更新完成后再进行操作
          this.highlightText(this.newText2, "red");
        });
      } else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: "缩写失败",
          type: "error",
        });
      }
    },
    // 4.润色接口
    async sendToBackend4() {
      this.loading1 = true;
      this.mask = true;
      let params4 = {
        text: this.selectedText,
        flag: "4",
      };
      let res = await rewrite(params4);
      this.newText3 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;

        this.$nextTick(() => {
          const editor = this.$refs.editor.quill;

          // 获取编辑器中的所有文本
          const allText = editor.getText();

          // 找到你传给后端的那段文字的位置
          const position = allText.indexOf(this.selectedText);

          console.log(position, "position");

          if (position !== -1) {
            const editor = this.$refs.editor.quill;
            editor.insertEmbed(
              position + this.selectedText.length,
              "customBlock",
              {
                html: this.newText3,
                newText: this.newText3,
                editorClass: this.$refs.editor.quill,
                Position: position,
                selectedTextlength: this.selectedText.length,
                flag: 4,
              },
              Quill.sources.USER
            );
            this.$nextTick(() => {
              // 确保 DOM 更新完成后再进行操作
              this.highlightText(this.newText2, "red");

              this.$message({
                message: "润色成功",
                type: "success",
              });
            });
          }
        });
      } else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: "润色失败",
          type: "error",
        });
      }
    },

    // 5.重新生文接口
    async sendToBackend5() {
      this.loading1 = true;
      this.mask = true;
      let params4 = {
        text: this.selectedText,
        flag: "5",
      };
      let res = await rewrite(params4);
      this.newText3 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "生文成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText3,
              newText: this.newText3,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 4,
            },
            Quill.sources.USER
          );
          this.$nextTick(() => {
            // 确保 DOM 更新完成后再进行操作
            this.highlightText(this.newText2, "red");
          });
        }
      } else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: "生文失败",
          type: "error",
        });
      }
    },
    // 5.重新生成XXX
    async sendToBackendadd() {
      this.loading = true;
      this.mask = true;
      let params4 = {
        outline_part: this.selectedText,
        all_part: this.textarea3,
        user_id: this.id,
      };
      let res = await regenerate_outline_part(params4);
      this.newText3 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading = false;
        this.mask = false;
        this.$message({
          message: "已重新生成",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText3,
              newText: this.newText3,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 4,
            },
            Quill.sources.USER
          );
          this.$nextTick(() => {
            // 确保 DOM 更新完成后再进行操作
            this.highlightText(this.newText2, "red");
          });
        }
      } else {
        this.loading = false;
        this.mask = true;
        this.$message({
          message: "操作失败",
          type: "error",
        });
      }
    },
    // 新增关联
    // async sendToBackendadd_dg() {
    //   // this.isOverlayVisible = true;
    //   this.selectedText = window.getSelection().toString();
    //   const selection = window.getSelection();
    //   if (selection.rangeCount > 0) {
    //     const range = selection.getRangeAt(0);
    //     const startOffset = range.startOffset; // 光标起始位置
    //     const endOffset = range.endOffset; // 光标结束位置

    //     let params4 = {
    //       outline_ids: this.checkList,
    //       content: this.selectedText,
    //       article_id: this.article_id,
    //       basic_id: this.basic_id,
    //       start: startOffset,
    //       end: endOffset,
    //     };
    //     let res = await add_correspondence(params4);
    //     if (res.data.status_code == 200) {
    //       this.loading = false;
    //       this.$message({
    //         message: res.data.message,
    //         type: "success",
    //       });
    //       this.checkList = [];
    //       //  this.selectedText = "";
    //       //  this.article_id = "";
    //       this.get_his_match();
    //     } else {
    //       this.loading = false;
    //       this.$message({
    //         message: res.data.message,
    //         type: "error",
    //       });
    //       // 调用接口，成功之后调查接口，查成功了遮罩消失
    //     }
    //   } else {
    //     console.error("没有选中文本。");
    //   }

    // },
    // 新增关联
    // 新增关联
    async sendToBackendadd_dg() {
      // this.selectedText=window.getselection().tostring();
      // const selection =window.getselection();
      // if(selection.rangecount>0){
      // const range = selection.getRangeAt(0);
      // const startoffset=range.startoffset;//光标起始位置
      // const endoffset= range.endoffset

      let params4 = {
        outline_ids: this.checkList,
        content: this.selectedText,
        article_id: this.article_id,
        basic_id: this.basic_id,
        start: 0,
        end: 0,
      };
      let res = await add_correspondence(params4);
      if (res.data.status_code == 200) {
        this.loading = false;
        this.$message({
          message: res.data.message,
          type: "success",
        });
        this.checkList = [];
        //  this.selectedText = "";
        //  this.article_id = "";
        this.get_his_match();
      } else {
        this.loading = false;
        this.$message({
          message: res.data.message,
          type: "error",
        });
        // 调用接口，成功之后调查接口，查成功了遮罩消失
      }
    },
    // else {
    //   console.error("没有选中文本。");
    // }

    // },

    copyText() {
      // 复制逻辑
      console.log("复制");
    },
    pasteText() {
      // 粘贴逻辑
      console.log("粘贴");
    },
    cutText() {
      // 剪切逻辑
      console.log("剪切");
    },
    // }
    clickTopsfw() {
      // if(this.username=='zhangsan')
      // {
      //     this.$message({
      //   message: "暂无权限",
      //   type: "warning",
      // });
      // }
      // else{
      this.$router.push("/sfw");
      // }
    },
    async baocun() {
      console.log(this.$route.query.userName);
      let params = {
        wznr: this.textarea4.replace('"', '/"'),
        wzlx: "公文写作",
        userName: this.username,
        cjrid: this.id,
      };
      let resbcwz = await bcwz(params);
      // EventBus.$emit('updateDocument'); // 发射更新事件
      resbcwz.data.status_code == 200
        ? this.$message({
            message: "保存成功,请前往我的文档查看",
            type: "success",
          })
        : this.$message({
            message: "保存失败",
            type: "error",
          });
      // this.getwz1();
    },
    async getwz1() {
      let res1 = await getwz(this.params1);

      // if (res1.data.status_code == 200) {
      //   this.tableData1 = res1.data.data;

      //   // 弹出提示
      //   this.$message({
      //     message: res1.data.message,
      //     type: 'success'
      //   });

      // }
      // else if (res1.data.status_code == 500) {

      //   this.$message({
      //     message: res1.data.message,
      //     type: 'error'
      //   })
      // }
    },
    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "************************************");
      // this.textarea3 = html;
      // return html;
    },
    getPlainText(html) {
      const div = document.createElement("div");
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    },
    //     getHtmlContent(html) {
    //   const div = document.createElement('div');
    //   div.innerHTML = html;
    //   return div.innerHTML;
    // },

    onCopySuccess() {
      console.log("复制成功");
    },
    onCopyError() {
      console.log("复制失败");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    //     logout() {
    //   this.$router.push("/login");
    // },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    ignore() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
    },
    highlightChange(text1, text2) {
      console.log(this.textarea4);
      let changeData = text2;
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.textarea4 = this.removetext(this.textarea4);
      this.textarea4 = this.textarea4.replace(text1, changeData);
    },
    highlightError(error, word) {
      this.textarea4 = this.removetext(this.textarea4);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.textarea4 = this.textarea4.replace(error, a);
      console.log(this.textarea4);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(this.$refs.editor.$el, "editorIframe");
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    removetextp(text) {
      let str = text;
      let reg1 = new RegExp(`<p>`, "g");
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</p>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    onCopySuccess() {
      this.$message({
        message: "内容已复制到剪贴板！",
        type: "success",
      });
    },
    onCopyError() {
      this.$message({
        message: "复制失败，请手动复制!",
        type: "warning",
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push(inputValue);
        //
        this.textarea2 = this.dynamicTags;
      }
      // this.inputVisible = false;
      this.inputValue = "";
    },
    async jiaoyan() {
      this.textarea4 = this.removetext(this.textarea4);
      this.con_loading = true;
      // let text = this.removetextp(this.textarea4)
      testcheck({
        test_all: this.textarea4,
      }).then(async (data) => {
        this.correctionList = data.data.data;
        this.con_loading = false;
        if (this.correctionList.length == 0) {
          this.jydis = true;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          console.log(data, "619");
          this.jydis = false;
        }
      });
    },
    handleClosedrawer() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    // async jiaoyan(){
    //   this.jydis = false
    // },
    save() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.jydis = true;
    },
    fanhui() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    handleStepClick(index) {
      this.lock++;
      this.activeIndex = index; // 更新activeIndex的值
    },
    upShow() {
      this.navShow = !this.navShow;
    },
    async textareaFocus() {
      // let params = {
      //   username: this.username,
      // };

      let data = await select_write_type();
      this.gzbj = data.data.data;
      this.dialogShow = true;
      // }
      // else{
      //   // this.dialogShow = false;
      // this.dialogShow = true;

      // }
    },
    async textareaFocus1() {
      if (this.textarea == "") {
        this.$notify({
          title: "提示",
          message: "请先填写文种",
          type: "warning",
        });
      } else if (this.title == "") {
        this.$notify({
          title: "提示",
          message: "请先填写标题",
          type: "warning",
        });
      } else {
        // this.loading = true;
        // this.mask = true;
        let pams = {
          // username: this.username,
          type: this.textarea,
          title: this.title,
          content_theme: this.content_theme,
          abstract: this.abstract,
          user_id: this.id,
        };
        // ***
        // let data = await recommend_firstLevel(pams);
        // this.responseText = data.data.data;
        // this.loading = false;
        // this.mask = false;
        // this.dialogShow1 = true;
      }
    },
    closeDialog() {
      this.dialogShow = false;
    },
    closeDialog1() {
      this.dialogShow1 = false;
    },
    textarea2Focus() {
      this.dialog2Show = true;
    },
    closeDialog2() {
      this.dialog2Show = false;
    },
    async getText(e) {
      // console.log(e);
      // let params = {
      //   label: e,
      // };
      // this.secRecommend = data.data;
      this.textarea = e;
      this.textarea2 = [];
    },
    async getText1(e) {
      this.textarea1 = this.responseText;
    },
    getText2(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea2.push(e);
        this.dynamicTags.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm(e);
      } else {
        console.log(111);
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickszbg() {
      this.$router.push("/index");
    },
    clickTopXdth() {
      this.$router.push("/xdth");
    },
    clickTopLg() {
      this.$router.push("/lgsc");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    // 工作总结
    clickTopGz() {
      this.$router.push("/gzzj");
    },
    // 领导讲话
    clickTopLd() {
      this.$router.push("/ldjh");
    },
    // 工作方案
    clickTopFa() {
      this.$router.push("/gzfa");
    },
    // 调研报告
    clickTopDy() {
      this.$router.push("/dybg");
    },
    // 宣传材料
    clickTopXc() {
      this.$router.push("/xccl");
    },
    firstNextStep() {
      if (this.textarea == "") {
        this.$notify({
          title: "提示",
          message: "请填写文种",
          type: "warning",
        });
      } else if (this.title == "") {
        this.$notify({
          title: "提示",
          message: "请填写标题",
          type: "warning",
        });
      } else if (this.textarea1 == "" && this.abstract == "") {
        this.$notify({
          title: "提示",
          message: "摘要和一级标题至少要填写一个",
          type: "warning",
        });
      } else {
        //     title: "",
        // content_theme: "",
        // abstract: "",
        this.isOverlayVisible = false;

        this.mask = true;
        if (this.mask == true) {
          this.getInfo(
            this.textarea,
            this.title,
            this.content_theme,
            this.abstract,
            this.textarea1
          );
        }
      }
    },
    async getInfo(c1, c2, c3, c4, c5) {
      this.loading = true;
      let params = {
        type: c1,
        title: c2,
        content_theme: c3,
        abstract: c4,
        first_level_title: c5,
        basic_id: this.basic_id,
        user_id: this.id,
      };
      let res = await recommended_outline(params);
      if (res.data.status_code == 200) {
        // 弹出提示
        this.$message({
          message: res.data.message,
          type: "success",
        });
        this.textarea3 = "";
        this.textarea3 = res.data.data;
        this.buttonShow = true;
        // this.stepActived = 2;
        this.currentStep = 2;
        this.loading = false;
        this.mask = false;
        this.curIndex = "2";
        this.basic_id = 0;
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10001) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10002) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    repeatStep() {
      this.loading = true;
      this.mask = true;
      if (this.mask == true) {
        // this.getInfo(this.textarea, this.dynamicTags);
        this.getInfo(
          this.textarea,
          this.title,
          this.content_theme,
          this.abstract,
          this.textarea1
        );
        this.basic_id = 0;
      }
    },
    success() {
      this.artShow = true;
      this.loading = true;
      // 这里放置需要执行的逻辑或调用其他方法
      if (this.textarea3 == "") {
        this.$notify({
          title: "提示",
          message: "大纲内容为空，无法生成",
          type: "warning",
        });
      } else {
        this.progressPercent = 0;
        this.getSuccessInfo();
      }
    },
    // 依次请求接口
    async fetchResData(index, nextIndex) {
      if (index >= this.fetchResPrams.length) {
        this.fetchData(nextIndex);
        return;
      }
      let params = {
        basic_id: this.basic_id,
        outline_id: this.fetchResPrams[index],
      };
      const response = await generate_article(params);
      if (response.data.status_code == 200) {
        // this.$message({
        //   message: response.data.message,
        //   type: 'success'
        // });
        this.textarea4 += response.data.data + "\n";

        let totalLength = 0;
        let allSubArrays = this.resDataItem.filter(
          (item) => typeof item != "string"
        );
        allSubArrays.forEach((subArray) => {
          totalLength += subArray.length;
        });
        let kuai = 100 / totalLength;
        this.progressPercent += Math.floor(kuai);
        await this.fetchResData(index + 1, nextIndex);
      } else if (response.data.status_code == 500) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10001) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10002) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      }
    },
    // 一段式生成
    // async fetchData(index) {
    //   if (index >= this.resDataItem.length) {
    //     this.progressPercent = 100;
    //     this.maskAll = false;
    //     this.buttonShow = false;
    //     return;
    //   }
    //   if (typeof this.resDataItem[index] == "string") {
    //     this.textarea4 += this.resDataItem[index];
    //     await this.fetchData(index + 1);
    //   } else {
    //     this.fetchResPrams = this.resDataItem[index];
    //     this.fetchResData(0, index + 1);
    //   }
    //   // this.stepActived = 3;
    //   this.currentStep = 4;
    //   this.loading = false;
    // },
    async fetchData(index) {
      if (index >= this.resDataItem.length) {
        this.progressPercent = 100;
        this.maskAll = false;
        this.buttonShow = false;
        return;
      }
      if (typeof this.resDataItem[index] == "string") {
        // 添加换行符
        this.textarea4 += this.resDataItem[index] + "\n";
        await this.fetchData(index + 1);
      } else {
        this.fetchResPrams = this.resDataItem[index];
        this.fetchResData(0, index + 1);
      }
    },

    async getSuccessInfo() {
      this.loading = true;
      this.maskAll = true;
      let pams = {
        finally_outline: this.textarea3,
        type: this.textarea,
        title: this.title,
        content_theme: this.content_theme,
        abstract: this.abstract,
        first_level_title: this.textarea1,
        flag: this.radio,
        basic_id: this.basic_id,
      };
      let res = await prepare_write_basic(pams);
      if (res.data.status_code == 200) {
        this.basic_id = res.data.basic_id;
        this.textarea4 = "";
        this.resDataItem = res.data.data;
        this.fetchData(0);
        this.currentStep = 3;
        // this.stepActived = 3;
        this.loading = false;
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    clickTab(i) {
      // if (this.currentStep != 2) {
      //   this.$notify({
      //     title: "提示",
      //     message: "请先输入工作背景和工作要点生成大纲",
      //     type: "warning",
      //   });
      // } else {
      this.curIndex = i;
      // }
    },
  },
};
</script>
<style lang="less" scoped>
// font-size: calc(100vw * 14 / 1920);
.custom-steps {
  font-size: calc(100vw * 14 / 1920);
}
.context-menu {
  margin-left: calc(-100vw * 200 / 1920);
  position: absolute;
  background: #fff;
  box-shadow: 0 calc(100vh * 2 / 1080) calc(100vh * 10 / 1080)
    rgba(0, 0, 0, 0.1);
  z-index: 1080;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.context-menu li {
  padding: calc(100vh * 8 / 1080) calc(100vw * 16 / 1920);
  cursor: pointer;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}

.fr {
  float: right;
}

.work-container {
  height: 100%;

  .result-container {
    padding: calc(100vh * 30 / 1080);

    .elcol-title {
      display: flex;
      overflow: hidden;
      width: 100%;
      height: calc(100vh * 48 / 1080);

      .elcol-title-text {
        padding-left: calc(100vw * 10 / 1920);
        text-align: left;
        width: 40%;
        height: 100%;
      }

      .elcol-title-text2 {
        font-size: calc(100vw * 14 / 1920);
        width: calc(100vw * 75 / 1920);
      }

      .elcol-input {
        float: left;
        width: calc(60% - calc(100vw * 75 / 1920));
        border: none !important;
      }
    }
  }
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: calc(100vh * 20 / 1080);
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

.ejdhl {
  height: 100%;
  background: url(../assets/img-left.png) no-repeat center;
  background-size: cover;
  background-position: center;
  transition: ease-out 0.4s;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  // background-color: #fff;
  background-image: linear-gradient(
    76deg,
    #07389c 0%,
    #3d86d1 0%,
    #3448b3 100%
  );

  img {
    width: calc(100vw * 120 / 1920);
    height: calc(100vh * 48 / 1080);
  }

  p {
    border: calc(100vw * 1.54 / 1920) solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: calc(100vw * 5 / 1920);
  }
}

.el-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0 calc(100vh * 2 / 1080) calc(100vh * 4 / 1080) 0
    rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  width: calc(100% - calc(100vw * 300 / 1920));

  .ai-header {
    width: 100%;

    .ai-bar {
      width: calc(100% - calc(100vw * 300 / 1920));
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vw * 40 / 1920);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: calc(100vw * 1 / 1920) solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vw * 130 / 1920);
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vw * 16 / 1920);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vw * 14 / 1920);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
                rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }
          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
              rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: calc(100vw * 93 / 1920);
        height: calc(100vh * 80 / 1080);

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 16 / 1920);
            height: calc(100vh * 16 / 1080);
          }
        }

        .btn {
          margin-left: calc(100vw * 30 / 1920);
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 28 / 1920);
            height: calc(100vh * 28 / 1080);
            float: left;
          }

          p {
            float: left;
            margin-left: calc(100vw * 22 / 1920);
            margin-top: calc(-100vh * 2 / 1080);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  color: #333;
  text-align: center;
  width: calc(100% - calc(100vw * 300 / 1920));
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: calc(100vh * 20 / 1080) calc(100vw * 10 / 1920);
}
.ai-gai {
  position: fixed;
  left: 0;
  bottom: 0;
  height: 91%;
  z-index: 999;
}

.ai-body {
  transition: ease-out 0.4s;
  width: 100%;
  height: 100%;
  float: left;
  overflow: hidden;
}
.ai-body-left {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: calc(100vw * 444 / 1920);
  height: 100%;
  background: #ffffff;
  border-radius: calc(100vh * 8 / 1080);
  overflow-x: hidden;
  overflow-y: auto;
  margin-left: calc(100vw * 10 / 1920);
  float: left;
}

.ai-body-left-top {
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  overflow-y: scroll;
}

.ai-body-left-bottom {
  width: 100%;
  height: calc(100vh * 90 / 1080);

  .ai-body-left-bottom-button {
    height: calc(100vh * 40 / 1080);
    font-size: calc(100vw * 14 / 1920);
    flex-grow: 1;
    color: #fff;
    margin: 0 calc(100vw * 25 / 1920);
    cursor: pointer;
    line-height: calc(100vh * 40 / 1080);
    margin-top: calc(100vh * 30 / 1080);
    background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
      rgba(100, 120, 212, 1);
    border-radius: calc(100vh * 20 / 1080);
    font-family: PingFangSC-Regular;
  }
}

.ai-body-left-bottom2 {
  width: 100%;
  height: calc(100vh * 80 / 1080);
  padding-top: calc(100vh * 30 / 1080);

  .repeat {
    width: calc(100vw * 188 / 1920);
    float: left;
    height: calc(100vh * 40 / 1080);
    font-size: calc(100vw * 14 / 1920);
    flex-grow: 1;
    margin-left: calc(100vw * 25 / 1920);
    cursor: pointer;
    line-height: calc(100vh * 40 / 1080);
    background: #f4f6f8;
    border: calc(100vw * 1 / 1920) solid rgba(230, 230, 230, 1);
    border-radius: calc(100vh * 20 / 1080);
    font-family: PingFangSC-Semibold;
    color: #666666;
    text-align: center;
  }

  .ai-body-left-bottom-button {
    width: calc(100vw * 188 / 1920);
    float: left;
    height: calc(100vh * 40 / 1080);
    flex-grow: 1;
    margin-left: calc(100vw * 14 / 1920);
    cursor: pointer;
    line-height: calc(100vh * 40 / 1080);
    background-image: linear-gradient(102deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
      rgba(100, 120, 212, 1);
    border-radius: calc(100vh * 20 / 1080);
    margin-top: 0px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }

  .ai-step {
    margin-top: calc(100vh * 20 / 1080);
    width: calc(100% - calc(100vw * 40 / 1920));
    margin: 0 calc(100vw * 20 / 1920);
    margin-top: calc(100vh * 5 / 1080);
    height: calc(100vh * 40 / 1080);
  }
}
.ai-body-right {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: calc(100% - calc(100vw * 480 / 1920));
  height: 100%;
  background: #ffffff;
  border-radius: calc(100vh * 8 / 1080);
  overflow-x: hidden;
  overflow-y: auto;
  margin-left: calc(100vw * 20 / 1920);
  float: left;

  .ai-body-start {
    width: 100%;
    height: 100%;

    .pic_bkg1 {
      width: calc(100vw * 670 / 1920);
      height: calc(100vh * 590 / 1080);
      background: url(../assets/img-bg1.png) no-repeat center;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: calc(100vh * 70 / 1080);
      position: relative;
    }

    .pic_bkg {
      width: calc(100vw * 528 / 1920);
      height: calc(100vh * 518 / 1080);
      background: url(../assets/img-ai.png) no-repeat center;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: calc(100vh * 118 / 1080);
      position: relative;
    }

    .pic_font {
      position: absolute;
      margin-left: auto;
      margin-right: auto;
      left: 0;
      right: 0;
      bottom: calc(100vh * 116 / 1080);
      width: calc(100vw * 255 / 1920);
      height: calc(100vh * 40 / 1080);
      border: calc(100vw * 1.54 / 1920) solid rgba(0, 0, 0, 0);
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw * 28 / 1920);
      color: #000000;
      text-align: center;
      font-weight: 600;
    }

    .title_message {
      position: absolute;
      margin-left: auto;
      margin-right: auto;
      left: 0;
      right: 0;
      bottom: calc(100vh * 82 / 1080);
      text-align: center;
      line-height: calc(100vh * 16 / 1080);
      margin-top: calc(100vh * 10 / 1080);
      height: calc(100vh * 22 / 1080);
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 14 / 1920);
      color: #999999;
      letter-spacing: 0;
      font-weight: 400;
    }

    .pic_step {
      width: calc(100vw * 551 / 1920);
      height: calc(100vh * 142 / 1080);
      background: url("../assets/pic_step.png");
      background-size: contain;
      margin: auto;
    }
  }

  .ai-body-art {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;

    .over {
      overflow: auto;
    }

    .fir-textarea {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 96.5%;
      background: #f8f9fd;
      border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
      border-radius: calc(100vh * 4 / 1080);
      margin: calc(100vh * 20 / 1080);
      margin-bottom: 0;
      height: calc(100vh * 158 / 1080);

      ::v-deep(.el-textarea__inner) {
        font-size: calc(100vw * 14 / 1920) !important;
        background-color: #f8f9fd !important;
        height: 100% !important;
        font-family: PingFangSC-Regular;
        padding: calc(100vh * 13 / 1080) calc(100vw * 18 / 1920)
          calc(100vh * 33 / 1080) calc(100vw * 16 / 1920);
      }
    }

    .fir-textarea-max {
      height: 95% !important;
    }

    ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
      display: none !important;
    }

    ::v-deep(.ql-blank) {
      display: none !important;
    }

    ::v-deep(.ql-container.ql-snow) {
      border: 0;
    }
  }
}

.ai-tab {
  width: calc(100vw * 315 / 1920);
  height: calc(100vh * 40 / 1080);
  margin: 0 auto;
  margin-top: calc(100vh * 30 / 1080);
  background: #f4f6f8;
  border: calc(100vw * 1 / 1920) solid #eeeff0;
  border-radius: calc(100vh * 20 / 1080);

  .tab-item {
    font-size: calc(100vw * 14 / 1920);

    width: 33%;
    height: calc(100vh * 40 / 1080);
    line-height: calc(100vh * 16 / 1080);
    float: left;
    line-height: calc(100vh * 40 / 1080);
    cursor: pointer;

    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #9094a5;
    letter-spacing: 0;
    text-align: center;
  }

  .activedTab {
    border-radius: calc(100vh * 20 / 1080);
    background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
      rgba(100, 120, 212, 1);
    color: #ffffff;
  }
}

.tab-item-fir {
  width: 100%;
  height: calc(100vh * 536 / 1080);
  padding: 0 calc(100vw * 25 / 1920);
}

.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw * 14 / 1920);
  letter-spacing: 0;
  line-height: calc(100vh * 16 / 1080);
  overflow: hidden;
  margin-top: calc(100vh * 20 / 1080);
  margin-bottom: calc(100vh * 20 / 1080);
  height: calc(100vh * 20 / 1080);

  .fir-kuai {
    width: calc(100vw * 6 / 1920);
    height: calc(100vh * 16 / 1080);
    margin-right: calc(100vw * 8 / 1920);
    float: left;
    background: #4081ff;
    border-radius: calc(100vh * 1.5 / 1080);
  }

  .fir-title-p {
    line-height: calc(100vh * 16 / 1080);
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}

.fir-alert {
  margin-top: calc(100vh * 5 / 1080);
  height: calc(100vh * 35 / 1080);
  margin-bottom: 10px;
}

.ai-dialog {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  row-gap: calc(100vh * 1 / 1080);
  width: 100%;
  height: calc(100vh * 290 / 1080);
  max-height: calc(100vh * 294 / 1080);
  padding: calc(100vh * 7 / 1080);
  box-shadow: 0 calc(100vh * 20 / 1080) calc(100vh * 40 / 1080)
    calc(100vh * 4 / 1080) #e4e4e524;
  margin-top: calc(100vh * 10 / 1080);
  transition: ease-out 0.4s;
  background: #ace9ff;
  border: calc(100vw * 1 / 1920) solid rgba(90, 206, 255, 1);
  border-radius: calc(100vh * 4 / 1080);

  .ai-d-title {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    height: fit-content;
    margin: 0;
    padding: calc(100vh * 1 / 1080) calc(100vw * 3 / 1920)
      calc(100vh * 2 / 1080) calc(100vw * 2 / 1920);
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #313733;
    letter-spacing: 0;
    font-weight: 400;

    .ai-d-title-p {
      flex-grow: 1;
      line-height: calc(100vh * 16 / 1080);
      text-align: left;
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 14 / 1920);
      color: #313733;
      letter-spacing: 0;
      font-weight: 400;
      margin-bottom: calc(100vh * 10 / 1080);
    }

    img {
      width: calc(100vw * 18 / 1920);
      height: calc(100vh * 18 / 1080);
      cursor: pointer;
    }
  }

  .ai-d-body {
    width: 100%;
    height: calc(100% - calc(100vh * 44 / 1080));
    overflow: hidden;
    background: #ffffff;
    border-radius: calc(100vh * 4 / 1080);

    .hints-control {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      padding: calc(100vh * 14 / 1080) calc(100vw * 14 / 1920) 0;
      height: calc(100vh * 30 / 1080);

      .hint-icon {
        flex-grow: 0;
        flex-shrink: 0;
        width: calc(100vw * 20 / 1920);
        height: calc(100vh * 20 / 1080);
        margin-right: calc(100vw * 6 / 1920);
        background-size: contain;
        background-image: url("../assets/icon_fire.png");
      }

      .hint-description {
        font-weight: 600;
        line-height: calc(100vh * 14 / 1080);
        font-family: SourceHanSansSC-Bold;
        font-size: calc(100vw * 14 / 1920);
        color: #313733;
        letter-spacing: 0;
        font-weight: 700;
      }
    }
  }
}
.ai-tj-body {
  width: 100%;
  height: calc(100vh * 200 / 1080);
  overflow-y: auto;

  .ai-tj-item {
    padding: calc(100vh * 14 / 1080) calc(100vw * 14 / 1920) 0;
    line-height: calc(100vh * 12 / 1080);
    width: 50%;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: left;
    cursor: pointer;
    height: calc(100vh * 30 / 1080);
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #313733;
    letter-spacing: 0;
    font-weight: 400;
  }
}

// ***滚动条样式
.ai-tj-body::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
}

.ai-tj-body::-webkit-scrollbar-track {
  background: #fff;
}

.ai-tj-body::-webkit-scrollbar-thumb {
  background: #488aff;
}

.fir-textarea {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: calc(100vh * 14 / 1080);
  height: calc(100vh * 158 / 1080);
  background: #f8f9fd;
  border-radius: calc(100vh * 4 / 1080);

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
  }
}

.fir-textarea-height {
  height: calc(100vh * 460 / 1080) !important;
}

::v-deep(.el-textarea__inner) {
  padding: calc(100vh * 16 / 1080);
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: calc(100vw * 10 / 1920);
  height: calc(100vh * 10 / 1080);
}

.ai-nav {
  height: calc(100% - calc(100vh * 80 / 1080));
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  transition: ease-out 0.4s;

  .title {
    width: calc(100vw * 300 / 1920);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    height: calc(100vh * 20 / 1080);
    line-height: calc(100vh * 20 / 1080);
    padding-top: calc(100vh * 20 / 1080);
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    row-gap: calc(100vh * 12 / 1080);
    width: 100%;
    padding-top: calc(100vh * 45 / 1080);
    height: calc(100% - calc(100vh * 80 / 1080));
  }
}

.user-menu-content {
  width: calc(100vw * 246 / 1920);
  height: calc(100vh * 24 / 1080);
  padding: 0 calc(100vw * 20 / 1920);
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: calc(100vh * 17 / 1080);

    .avatar-wrapper {
      position: relative;
      width: calc(100vw * 52 / 1920);
      height: calc(100vh * 52 / 1080);
      border: calc(100vw * 1 / 1920) solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: calc(100vw * 52 / 1920);
        height: calc(100vh * 52 / 1080);
      }
    }

    .name-wrapper {
      width: calc(100vw * 300 / 1920);
      display: flex;
      flex-direction: column;
      margin-left: calc(100vw * 12 / 1920);

      .name {
        width: calc(100vw * 300 / 1920);
        color: #222;
        font-weight: 600;
        font-size: calc(100vw * 15 / 1920);
        letter-spacing: 0px;
        line-height: calc(100vh * 16 / 1080);
      }

      .id {
        margin-top: calc(100vh * 7 / 1080);
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw * 12 / 1920);
        letter-spacing: 0px;
        line-height: calc(100vh * 12 / 1080);
      }
    }
  }

  .divider {
    width: 100%;
    margin: calc(100vh * 20 / 1080) 0 calc(100vh * 18 / 1080);
    border-bottom: calc(100vw * 1 / 1920) solid #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      // margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: calc(100vh * 20 / 1080);
      cursor: pointer;

      :first-child {
        margin-top: 0px;
      }

      .icon {
        width: calc(100vw * 24 / 1920);
        height: calc(100vh * 24 / 1080);
        background-size: contain;
      }

      .text {
        margin-left: calc(100vw * 6 / 1920);
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: calc(100vh * 22 / 1080);
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

// 导航项
.nav-item {
  height: calc(100vh * 40 / 1080);
  background: transparent;
  line-height: calc(100vh * 20 / 1080);
  white-space: pre-wrap;
  background-image: linear-gradient(
    270deg,
    rgba(30, 75, 202, 0.39) 0%,
    rgba(59, 130, 234, 0.28) 100%
  );
  border: calc(100vw * 1 / 1920) solid rgba(255, 255, 255, 0.2);
  border-radius: calc(100vh * 4 / 1080);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
  cursor: pointer;
  margin: 0 auto;

  &:hover {
    background: #59bce1;
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
  }
}

.choose {
  border: calc(100vw * 1 / 1920) solid rgba(255, 255, 255, 0.2);
  border-radius: calc(100vh * 4 / 1080);
  background: #59bce1;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
}

.clbutton {
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: calc(100vh * 40 / 1080);
  left: calc(100vw * 40 / 1920);
}

.clbutton1 {
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: calc(100vh * 40 / 1080);
  left: calc(100vw * 160 / 1920);
}

.clbutton2 {
  left: calc(100vw * 180 / 1920);
}

.clbutton12 {
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: calc(100vh * 40 / 1080);
  left: calc(100vw * 280 / 1920);
}
.showUp {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 20 / 1080);
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: calc(100vh * 10 / 1080);
  left: calc(100vw * 60 / 1920);
  height: calc(100vh * 40 / 1080);
  bottom: calc(100vh * 20 / 1080);
  cursor: pointer;

  &:hover {
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.showUp1 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 20 / 1080);
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: calc(100vh * 10 / 1080);
  left: calc(100vw * 6 / 1920);
  height: calc(100vh * 40 / 1080);
  bottom: calc(100vh * 20 / 1080);
  cursor: pointer;

  &:hover {
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

::v-deep(.el-loading-spinner) {
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: calc(100vw * 150 / 1920) calc(100vh * 130 / 1080);
  height: calc(100vh * 100 / 1080);
  width: 100%;
  background-position: center;
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  margin: calc(100vh * 85 / 1080) 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(calc(-100vh * 20 / 1080))
    translateX(calc(-100vw * 16 / 1920));
  transform-origin: 0 0;
}

::v-deep(.el-drawer__header) {
  display: none;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(0);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0;
  width: 0;
  background: #c0c4cc;
}

::v-deep(.el-notification__group) {
  height: auto !important;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 1 / 1920);
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: calc(100vw * 14 / 1920) !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.progressClass {
  width: 100%;
  height: calc(100vh * 26 / 1080);
  position: fixed;
  bottom: calc(100vh * 48 / 1080);
  right: 0;
  z-index: 999;

  .progress {
    position: absolute;
    right: calc(100vw * 52 / 1920);
    transition: ease-out 0.4s;
  }
}

::v-deep(.el-progress-bar__innerText) {
  padding: calc(100vh * 7 / 1080) !important;
  color: #fff !important;
}

.menu_label {
  width: 100%;
  border-radius: calc(100vh * 4 / 1080);
  margin-top: calc(100vh * 10 / 1080);
  min-height: calc(100vh * 158 / 1080);
  height: auto;
  padding: calc(100vh * 15 / 1080) calc(100vw * 16 / 1920);
  background: #f8f9fd;
  border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
  border-radius: calc(100vh * 4 / 1080);
  padding: calc(100vh * 15 / 1080) 0;
}

.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: calc(100vw * 383 / 1920);
  word-wrap: break-word;
  text-align: left;
  margin-left: calc(100vw * 5 / 1920);
  margin-bottom: calc(100vh * 5 / 1080);
  float: left;
}

.el-main .ai-body .tab-item-fir .menu_label:active {
  background: #fff;
  border: calc(100vw * 1 / 1920) solid #4170f6;
}

.pass_input {
  width: 100%;
  height: calc(100vh * 40 / 1080);

  & /deep/ .el-input__inner {
    border: none;
    background-color: rgba(122, 151, 255, 0) !important;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #666666;
    letter-spacing: 0;
    font-weight: 400;
  }
}

::v-deep(.el-alert__title) {
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-drawer__open .el-drawer.ltr) {
  width: 100% !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0;
}

::v-deep(.el-alert) {
  padding: calc(100vh * 8 / 1080) calc(100vw * 8 / 1920);
}

.fir-textarea1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
  border-radius: calc(100vh * 4 / 1080);
  margin: calc(100vh * 20 / 1080);
  margin-bottom: 0;
  height: calc(100vh * 158 / 1080);

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: calc(100vh * 13 / 1080) calc(100vw * 18 / 1920)
      calc(100vh * 33 / 1080) calc(100vw * 16 / 1920);
  }
}

::v-deep(.el-collapse) {
  border: 0 solid #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: calc(100vw * 1 / 1920) solid #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40 / 1080);
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: calc(100vh * 4 / 1080);
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: calc(-100vw * 10 / 1920);
    top: calc(100vh * 2 / 1080);
    width: calc(100vw * 4 / 1920);
    height: calc(100vh * 15 / 1080);
    background: #5585f0;
    border-radius: calc(100vh * 2 / 1080);
  }
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
}

.elcol-title-left {
  width: calc(100vw * 8 / 1920);
  height: calc(100vh * 8 / 1080);
  border-radius: 50%;
  margin-top: calc(100vh * 20 / 1080);
  margin-left: calc(100vw * 10 / 1920);
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: calc(100vh * 48 / 1080);
}

.elcol-title-text {
  padding-left: calc(100vw * 10 / 1920);
  text-align: left;
  width: 40%;
  height: 100%;
}

.elcol-title-text2 {
  font-size: calc(100vw * 14 / 1920);
  color: #d1d7de;
  width: calc(100vw * 75 / 1920);
}

.elcol-input {
  float: left;
  width: calc(60% - calc(100vw * 75 / 1920));
  border: none !important;
}
::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
  display: none;
}
::v-deep(.ql-toolbar.ql-snow + .ql-container.ql-snow) {
  border-top: 1px solid #ccc; /* 修改为你想要的颜色和宽度 */
  min-height: 470px;
}
.add-editor {
  background-color: #f8f9fd !important;
  font-size: 14px !important;
  font-family: PingFangSC-Regular !important;
  color: #606266 !important;
}
.add-editor1 {
  background-color: #f8f9fd !important;
  font-size: 14px !important;
  font-family: PingFangSC-Regular !important;
  color: #606266 !important;
  border: none !important;
}

.dialog-content {
  height: 700px;
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: space-between; /* 让左右两个区域均匀分布 */
  align-items: stretch; /* 确保左侧内容区域能够撑开 */
  margin-top: 20px; /* 调整上边距 */
}

.dialog-left,
.dialog-right {
  background-color: #f8f9fd;
  flex: 1; /* 使左侧和右侧区域能够均分空间 */
  height: auto; /* 允许高度自适应，撑开内容 */
  // border: 1px solid #ccc;
  margin-right: 10px; /* 右侧有间隔 */
  overflow: auto; /* 允许滚动条 */
}

.dialog-right {
  margin-left: 10px; /* 确保右侧区域有间隔 */
}

.checkbox-group {
  display: flex;
  flex-direction: column; /* 竖向排列 */
  align-items: flex-start; /* 让复选框左对齐 */
}

.checkbox-group .el-checkbox {
  margin-bottom: 10px; /* 间隔 */
}
.add-editor1 ::v-deep(.ql-toolbar.ql-snow + .ql-container.ql-snow) {
  border: none; /* 只影响这个选择器 */
}
.editor-container {
  position: relative; /* 使遮罩层相对于容器定位 */
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1); /* 灰色遮罩 */
  z-index: 999; /* 确保遮罩层在上面 */
}
.el-button1 {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  height: 32px;
  white-space: nowrap;
  cursor: pointer;
  // color: var(--el-button-text-color);
  // text-align: center;
  // box-sizing: border-box;
  // outline: 0;
  // transition: .1s;
  // font-weight: var(--el-button-font-weight);
  // -webkit-user-select: none;
  // -moz-user-select: none;
  // -ms-user-select: none;
  // user-select: none;
  // vertical-align: middle;
  // -webkit-appearance: none;
  // background-color: var(--el-button-bg-color);
  // border: var(--el-border);
  // border-color: var(--el-button-border-color);
  // padding: 8px 15px;
  // font-size: var(--el-font-size-base);
  // border-radius: var(--el-border-radius-base);
}
::v-deep(.table1 .el-table__cell) {
  padding: 5px 0;
  min-width: 0;
  box-sizing: border-box;
  text-overflow: ellipsis;
  vertical-align: middle;
  position: relative;
  text-align: left;
}
.highlight {
  background-color: yellow; /* 设置高亮背景颜色 */
  font-weight: bold; /* 可选：设置高亮文本为粗体 */
}
</style>
