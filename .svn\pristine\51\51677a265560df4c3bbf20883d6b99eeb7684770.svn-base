<!-- 本页为范文管理-->
<template>
  <div class="work-container" v-loading="con_loading">
    <el-container>
      <div
        :style="
          navShow
            ? 'width: -calc(100vw * 10 / 1920)'
            : 'width: calc(100vw * 10 / 1920)'
        "
        class="ejdhl"
      >
        <div
          class="ai-nav"
          :style="
            navShow
              ? 'width: calc(100vw * 300 / 1920)'
              : 'width: calc(100vw * 64 / 1920)'
          "
        >
          <div class="nav-list">
            <div
              @click="clickgrzx()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon204a.png" alt="" />
              <p v-if="navShow">我的信息</p>
            </div>
            <div
              @click="clickzhgl()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon201a.png" alt="" />
              <p v-if="navShow">账户管理</p>
            </div>
            
            <div
              @click="clickgosygl()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/术语.png" alt="" />
              <p v-if="navShow">术语管理</p>
            </div>
                    <div
              @click="clickmb()"
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon-jj.png" alt="" />
              <p v-if="navShow">模板管理</p>
            </div> 
            <div
              @click="clickzskgl()"
              class="nav-item choose"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
            >
              <img src="../assets/icon202b.png" alt="" />
              <p v-if="navShow">范文管理</p>
            </div>
            <div
              class="nav-item"
              :style="
                navShow
                  ? 'width: calc(100vw * 120 / 1920)'
                  : 'width: calc(100vw * 50 / 1920)'
              "
              @click="clickTopLd()"
            >
              <img src="../assets/icon17.png" alt="" />
              <p v-if="navShow">返回系统</p>
            </div>
          </div>
        </div>
     
      </div>
      <el-header class="el-header1">
        <div class="ai-header">
          <div class="flex">
            <div class="ai-left-bar-li actived"></div>
          </div>
        </div>
      </el-header>
      <el-main>
        <div
          class="ai-gai"
          v-loading="loading"
          element-loading-text="请稍候..."
          element-loading-background="rgba(255, 255, 255, 0.7)"
          :style="navShow ? 'width: 1600px' : 'width: 528px'"
          style="transition: ease-out 0.4s; z-index: 999"
          v-if="mask"
        ></div>
        <div class="LgscCon">
          <div>
            <el-tabs class="centered-tabs" v-model="activeName">
              <el-tab-pane
                class="tab-item"
                name="a"
                @click="
                  getwz1(1, 10, username, '述职报告', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz1(1, 10, username, '述职报告', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    述职报告 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('1')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('1')">上传文件</span>
                  </button>
                  <button
                    @click="batchDelete1('述职报告')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData1"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia1(aa.row)"
                        >删除</el-button
                      >
                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia11(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange1"
                  @current-change="handleCurrentChange1"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total"
                >
                </el-pagination>
              </el-tab-pane>
              <el-dialog
                title="文章详情"
                :visible.sync="dialogVisible"
                width="30%"
              >
                <div class="dialog-content" v-html="currentWznr"></div>

                <span slot="footer" class="dialog-footer">
                  <el-button @click="dialogVisible = false">关闭</el-button>
                </span>
              </el-dialog>
              <el-tab-pane
                class="tab-item"
                name="b"
                @click="
                  getwz2(1, 10, username, '心得体会', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz2(1, 10, username, '心得体会', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    心得体会&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('2')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('2')">上传文件</span>
                  </button>

                  <button
                    @click="batchDelete2('心得体会')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input_1"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag1"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData2"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" sortable label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 500 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia2(aa.row)"
                        >删除</el-button
                      >

                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia22(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange2"
                  @current-change="handleCurrentChange2"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total1"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane
                class="tab-item"
                name="c"
                @click="
                  getwz3(1, 10, username, '领导讲话', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz3(1, 10, username, '领导讲话', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    领导讲话&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('3')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('3')">上传文件</span>
                  </button>
                  <button
                    @click="batchDelete3('领导讲话')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input_1"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag2"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData3"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" sortable label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia3(aa.row)"
                        >删除</el-button
                      >

                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia33(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                  <!-- </el-table> -->
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange3"
                  @current-change="handleCurrentChange3"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total2"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane
                class="tab-item"
                name="d"
                @click="
                  getwz4(1, 10, username, '工作方案', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz4(1, 10, username, '工作方案', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    工作方案&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('4')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('4')">上传文件</span>
                  </button>
                  <button
                    @click="batchDelete4('工作方案')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input_1"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag3"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData4"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" sortable label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia4(aa.row)"
                        >删除</el-button
                      >

                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia44(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange4"
                  @current-change="handleCurrentChange4"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total3"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane
                class="tab-item"
                name="e"
                @click="
                  getwz5(1, 10, username, '调研报告', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz5(1, 10, username, '调研报告', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    调研报告&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('5')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('5')">上传文件</span>
                  </button>

                  <button
                    @click="batchDelete5('调研报告')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input_1"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag4"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData5"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" sortable label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia5(aa.row)"
                        >删除</el-button
                      >

                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia55(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange5"
                  @current-change="handleCurrentChange5"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total4"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane
                class="tab-item"
                name="f"
                @click="
                  getwz6(1, 10, username, '宣传材料', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz6(1, 10, username, '宣传材料', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    宣传材料&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('6')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('6')">上传文件</span>
                  </button>
                  <button
                    @click="batchDelete6('宣传材料')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input_1"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag5"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData6"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" sortable label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia6(aa.row)"
                        >删除</el-button
                      >
                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia66(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange6"
                  @current-change="handleCurrentChange6"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total5"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane
                class="tab-item"
                name="g"
                @click="
                  getwz7(1, 10, username, '其他', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz7(1, 10, username, '其他', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    其他&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('7')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('7')">上传文件</span>
                  </button>
                  <button
                    @click="batchDelete7('其他')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input_1"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag6"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData7"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" sortable label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia7(aa.row)"
                        >删除</el-button
                      >
                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia77(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange7"
                  @current-change="handleCurrentChange7"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total6"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane
                class="tab-item"
                name="p"
                @click="
                  getwz8(1, 10, username, '会议纪要', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz8(1, 10, username, '会议纪要', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    会议纪要&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('8')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('8')">上传文件</span>
                  </button>
                  <button
                    @click="batchDelete8('会议纪要')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input_1"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag7"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData8"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" sortable label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia8(aa.row)"
                        >删除</el-button
                      >
                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia88(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange8"
                  @current-change="handleCurrentChange8"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total7"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane
                class="tab-item"
                name="h"
                @click="
                  getwz9(1, 10, username, '文', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz9(1, 10, username, '文', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    AI文&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('9')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('9')">上传文件</span>
                  </button>
                  <button
                    @click="batchDelete9('文')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input_1"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag8"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData9"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" sortable label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia9(aa.row)"
                        >删除</el-button
                      >
                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia99(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange9"
                  @current-change="handleCurrentChange9"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total8"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane
                class="tab-item"
                name="r"
                @click="
                  getwz10(1, 10, username, 'AI听', []);
                  tags = [];
                  inputValue = '';
                  showInput = true;
                "
              >
                <template #label>
                  <div
                    class="custom-label"
                    @click="
                      getwz10(1, 10, username, 'AI听', []);
                      tags = [];
                      inputValue = '';
                      showInput = true;
                    "
                  >
                    AI听
                  </div>
                </template>
                <div style="display: flex; align-items: center; gap: 20px">
                  <button
                    @click="up1('10')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--primary el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                        ></path>
                      </svg>
                    </i>
                    <span class="" @click="up1('10')">上传文件</span>
                  </button>
                  <button
                    @click="batchDelete10('AI听')"
                    aria-disabled="false"
                    type="button"
                    class="el-button el-button--danger el-button--default"
                  >
                    <i class="el-icon">
                      <svg
                        viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill="currentColor"
                          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                        ></path>
                      </svg>
                    </i>
                    <span class="">批量删除</span>
                  </button>
                  <div
                    class="multi-input_1"
                    style="width: calc(100vw * 500 / 1920)"
                  >
                    <el-tag
                      v-for="(tag, index) in tags"
                      :key="index"
                      closable
                      @close="removeTag(index)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-model="inputValue"
                      class="input-new-tag"
                      size="small"
                      placeholder="输入搜索内容后敲击回车进行搜索"
                      @keyup.enter.native="addTag9"
                      v-if="showInput"
                    ></el-input>
                    <div class="plusBtn" @click="handleSearch">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </div>
                <el-table
                  stripe
                  border
                  @selection-change="handleSelectionChange"
                  :default-sort="{ prop: 'date', order: 'descending' }"
                  style="width: 100%; margin-top: calc(100vh * 10 / 1080)"
                  height="calc(100vh * 800/ 1080)"
                  :data="tableData10"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column type="selection"> </el-table-column>
                  <el-table-column
                    label="序号"
                    width="50"
                    sortable
                    type="index"
                  >
                  </el-table-column>
                  <el-table-column prop="file_name" label="文件名">
                  </el-table-column>
                  <el-table-column prop="create_time" label="创建时间">
                  </el-table-column>

                  <el-table-column prop="remarks" sortable label="文章内容">
                    <template v-slot="scope">
                      <div
                        style="
                          max-height: calc(100vh * 50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                        :title="scope.row.file_content"
                      >
                        {{ scope.row.file_content }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    min-width="150"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="show1(aa.row)"
                        >查看文章详情</el-button
                      >
                      <el-button
                        class="el-icon-delete"
                        type="text"
                        size="medium"
                        @click="showdia10(aa.row)"
                        >删除</el-button
                      >
                      <!-- <el-button
                        class="el-icon-download"
                        type="text"
                        size="medium"
                        @click="showdia00(aa.row)"
                        >下载</el-button
                      > -->
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: calc(100vh * 10 / 1080); flex-shrink: 0"
                  @size-change="handleSizeChange10"
                  @current-change="handleCurrentChange10"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="this.total9"
                >
                </el-pagination>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="nrCon">
            <div class="nrConLeft">
              <div
                class="nrConLeftBk"
                v-for="(item, index) in this.nrArr"
                :key="index"
                v-if="index % 2 == 0"
              >
                <div class="bkTitle">
                  {{ item.t1 }}
                </div>
                <div class="bkCon">
                  {{ item.t2 }}
                </div>
                <div class="bkBtn flexLd">
                  <div class="bkBtnLeft flexcz">
                    <div class="xztg flex">
                      {{ item.t3 }}
                    </div>
                    <div class="qt flex" v-if="item.t4 != ''">
                      {{ item.t4 }}
                    </div>
                  </div>
                  <div class="bkBtnright flexLd">
                    <div
                      class="fz flex"
                      v-clipboard:copy="item.t2"
                      v-clipboard:success="onCopySuccess"
                      v-clipboard:error="onCopyError"
                    >
                      <img src="../assets/icon-30.png" alt="" />
                      <p>复制</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="nrConRight floatLeft">
              <div
                class="nrConLeftBk"
                v-for="(item, index) in this.nrArr"
                :key="index"
                v-if="index % 2 != 0"
              >
                <div class="bkTitle">
                  {{ item.t1 }}
                </div>
                <div class="bkCon">
                  {{ item.t2 }}
                </div>
                <div class="bkBtn flexLd">
                  <div class="bkBtnLeft flexcz">
                    <div class="xztg flex">
                      <!-- 写作提纲 -->
                      {{ item.t3 }}
                    </div>
                    <div class="qt flex" v-if="item.t4 != ''">
                      <!-- 其他 -->
                      {{ item.t4 }}
                    </div>
                  </div>
                  <div
                    class="bkBtnright flexLd"
                    v-clipboard:copy="item.t2"
                    v-clipboard:success="onCopySuccess"
                    v-clipboard:error="onCopyError"
                  >
                    <div class="fz flex">
                      <img src="../assets/icon-30.png" alt="" />
                      <p>复制</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog title="上传文件" :visible.sync="adddia">
      <div>
        <!-- <h4>上传文件</h4> -->
        <el-form>
          <div style="text-align: left; margin-bottom: 10px">
            <el-radio-group v-model="form.fileType" @change="radioChange">
              <el-radio-button label="txt" @click="flag_type = '1'"
                >文本文件</el-radio-button
              >
              <el-radio-button label="table" @click="flag_type_set()"
                >表格</el-radio-button
              >
              <el-radio-button label="QA" @click="flag_type = '3'"
                >QA 问答对</el-radio-button
              >
            </el-radio-group>
          </div>

          <!-- QA 问答对 -->
          <el-form-item v-if="form.fileType === 'QA'" prop="fileList">
            <div class="update-info">
              <div style="text-align: left">
                <p style="text-align: left">
                  1、点击下载对应模版并完善信息：
                  <el-button
                    type="text"
                    link
                    @click="downloadTableTemplate('3', '1')"
                  >
                    下载 Excel 模版
                  </el-button>
                  <el-button
                    type="text"
                    link
                    @click="downloadTableTemplate('3', '2')"
                  >
                    下载 CSV 模版
                  </el-button>
                </p>
                <p style="text-align: left">
                  2、上传的表格文件中每个 sheet
                  会作为一个文档，sheet名称为文档名称
                </p>
                <p style="text-align: left">
                  3、每次最多上传 50 个文件，每个文件不超过 100MB
                </p>
              </div>
            </div>
            <el-upload
              class="upload-demo"
              multiple
              :before-upload="beforeAvatarUpload"
              ref="upload"
              action="#"
              :show-file-list="false"
              accept=".xlsx,.csv"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                <em>点击上传</em>
              </div>
              <div class="el-upload__tip" slot="tip">
                支持格式：EXCEL 和 CSV
              </div>
            </el-upload>
          </el-form-item>

          <!-- 表格 -->
          <el-form-item v-else-if="form.fileType === 'table'" prop="fileList">
            <div class="update-info">
              <div style="text-align: left">
                <p style="text-align: left">
                  1、点击下载对应模版并完善信息：
                  <el-button
                    type="text"
                    link
                    @click="downloadTableTemplate('2', '1')"
                  >
                    下载 Excel 模版
                  </el-button>
                  <el-button
                    type="text"
                    link
                    @click="downloadTableTemplate('2', '2')"
                  >
                    下载 CSV 模版
                  </el-button>
                </p>
                <p style="text-align: left">
                  2、第一行必须是列标题，且列标题必须是有意义的术语，表中每条记录将作为一个分段
                </p>
                <p style="text-align: left">
                  3、上传的表格文件中每个 sheet
                  会作为一个文档，sheet名称为文档名称
                </p>
                <p style="text-align: left">
                  4、每次最多上传 50 个文件，每个文件不超过 100MB
                </p>
              </div>
            </div>
            <el-upload
              class="upload-demo"
              multiple
              :before-upload="beforeAvatarUpload"
              ref="upload"
              action="#"
              :show-file-list="false"
              accept=".xlsx,.csv"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                <em>点击上传</em>
              </div>
              <div class="el-upload__tip" slot="tip">
                支持格式：EXCEL 和 CSV
              </div>
            </el-upload>
          </el-form-item>

          <!-- 文本文件 -->
          <el-form-item v-else prop="fileList">
            <div class="update-info">
              <div style="text-align: left">
                <p style="text-align: left">
                  1、文件上传前，建议规范文件的分段标识
                </p>
                <p style="text-align: left">
                  2、每次最多上传 50 个文件，每个文件不超过 100MB
                </p>
              </div>
            </div>

            <el-upload
              class="upload-demo"
              multiple
              :before-upload="beforeAvatarUpload"
              ref="upload"
              action="http://**************:5003/insert_knowledge_base"
              :show-file-list="false"
              accept=".txt,.md,.pdf,.docx,.html"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                <em>点击上传</em>
              </div>
              <div class="el-upload__tip" slot="tip">
                支持格式：TXT、Markdown、PDF、DOCX、HTML
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adddia = false">取 消</el-button>
        <el-button type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import App from "../App";
import Wordcloud from "../components/Wordcloud.vue";
import { VueEditor } from "vue2-editor";
import CustomSteps from "../components/Step.vue";
//   import store from '../store/index'
import { mapState } from "vuex";

import store from "../store/index";
import {
  download_knowledge_base_list,
  selectPage_knowledge_base_list,
  select_handle_all,
  add_Single_User,
  js,
  fwsc,
  getSecDtDatas,
  getSuccessInfo,
  getSuccessInfoItem,
  getLabel1,
  getLabel2,
  testcheck,
  bcwz,
  rewrite,
  xz,
  cyt,
  get_user,
  getwrite,
  get_login_user,
  initPassWord,
  disabled,
  remove_disabled,
  delete_User,
  delete_knowledge_base_list,
  excel_download,
} from "../api/home.js"; // 接口请求
export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id
    username() {
      return this.$store.state.username;
    },
    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 600}px`,
      };
    },
    id() {
      return this.$store.state.id;
    },
    imageSrc() {
      return `data:image/jpeg;base64,${this.base64Image}`; // 注意根据实际的MIME类型替换'image/jpeg'
    },
  },
  data() {
    return {
      form: {
        fileType: "txt", // 默认选中文本文件
        fileList: [],
      },
      rules: {
        fileList: [
          { required: true, message: "请上传文件", trigger: "change" },
        ],
      },
      loading: false,
      mask: false,
      tags: [],
      inputValue: "",
      showInput: true,
      currentPage: 1, // 当前页
      pageSize: 10, // 每页显示条数
      selectedUsers: [], // 用于存储选中的文件名字
      departments: [], // 用于存储从后端获取的处室数据
      adddia: false,
      tableData1: [],
      inputvalyhm: "",
      sure_passw: "",
      new_passw: "",
      old_passw: "",
      form1: {
        xm: "",
        xb: "",
        age: "",
        szdw: "",
        ssgw: "",
        sjgzly: "",
        ssqy: "",
      },

      // },
      formLabelWidth: "100px",

      up_img: false,
      nan_img: false,
      nv_img: false,
      backgroundImage: "", // 默认背景图
      tags: [], // 存储从后端获取的“句式特点”标签
      base64Image: "",
      xb: "",
      xm: "",
      szdw: "",
      ssgw: "",
      sjgzly: "",
      ssqy: "",
      age: "",
      wcdata: [],
      // 定义变量存词云的返回值，然后用vuex传给词云组件
      fzqwnr: "",
      newText: "",
      newText1: "",
      newText2: "",
      newText3: "",
      xxdialog: false,
      xxcontent: "",
      kxdialog: false,
      sxdialog: false,
      rsdialog: false,
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      selectedText: "",
      con_loading: false,
      loading1: false,
      correctionList: [],
      jydis: true,
      navShow: true,
      textarea: "",
      textarea2: [],
      textarea21: [],
      textarea22: [],

      textarea3: "",
      textarea4: "",
      textarea4: "",
      dialogShow: false,
      dialog2Show: false,
      gzbj: [],
      stepActived: 1,
      loading: false,
      curIndex: "2",
      articles: "",
      artShow: false,
      // mask: false,
      maskAll: false,
      /**
       * @description:  功能性按钮
       * @return {*}    传入组件->FunctionTable
       */
      steps: [
        { title: "录入角色" },
        { title: "录入写作风格" },
        { title: "生成词云" },
      ],

      currentStep: 2, // 初始化当前步骤为第二个步骤
      secRecommend: [], // 第二步推荐
      dynamicTags: [],
      inputVisible: true,
      inputValue: "",
      resDataItem: [],
      progressPercent: 0,
      fetchResPrams: [],
      buttonShow: true,
      drawer: false,
      direction: "ltr",
      activeNames: [],
      currentPage: 1, // 当前页
      pageSize: 10, // 每页显示条数
      total: 0,
      total1: 0,
      total2: 0,
      total3: 0,
      total4: 0,
      total5: 0,
      total6: 0,
      total7: 0,
      total8: 0,
      total9: 0,
      // 表单验证规则
      formRules: {
        userName: [
          { required: true, message: "文件名不能为空", trigger: "blur" },
        ],
        passWord: [
          { required: true, message: "密码不能为空", trigger: "blur" },
        ],
        handle_name: [
          { required: true, message: "所在处室不能为空", trigger: "change" },
        ],
        // gwmc: [
        //   { required: true, message: '岗位名称不能为空', trigger: 'blur' }
        // ]
      },
      // };
      currentWznr: "",
      dialogVisible: false,
      tableData2: [],
      tableData3: [],
      tableData4: [],
      tableData5: [],
      tableData6: [],
      tableData7: [],
      tableData8: [],
      tableData9: [],
      tableData10: [],
      activeName: "a",
      array2: "",
      inputval: "",
      navShow: true,
      flpd: "",
      ztpd: "",
      input: "",
      flArr: [],
      ztArr: [],
      nrArr: [],
      flmc: "",
      ztmc: "",
      upload_type: "",
      flag_type: "1",
    };
  },
  components: {
    App,
    // 注册组件
    CustomSteps,
    VueEditor,
    Wordcloud,
  },
  mounted() {
    this.getwz1(1, 10, this.username, "述职报告", []);
    this.getwz2(1, 10, this.username, "心得体会", []);
    this.getwz3(1, 10, this.username, "领导讲话", []);
    this.getwz4(1, 10, this.username, "工作方案", []);
    this.getwz5(1, 10, this.username, "调研报告", []);
    this.getwz6(1, 10, this.username, "宣传材料", []);
    this.getwz7(1, 10, this.username, "其他", []);
    this.getwz8(1, 10, this.username, "会议纪要", []);
    this.getwz9(1, 10, this.username, "文", []);
    this.getwz10(1, 10, this.username, "AI听", []);
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.getwz1(1, 10, vm.username, "述职报告", []);
      vm.getwz2(1, 10, vm.username, "心得体会", []);
      vm.getwz3(1, 10, vm.username, "领导讲话", []);
      vm.getwz4(1, 10, vm.username, "工作方案", []);
      vm.getwz5(1, 10, vm.username, "调研报告", []);
      vm.getwz6(1, 10, vm.username, "宣传材料", []);
      vm.getwz7(1, 10, vm.username, "其他", []);
      vm.getwz8(1, 10, vm.username, "会议纪要", []);
      vm.getwz9(1, 10, vm.username, "文", []);
      vm.getwz10(1, 10, vm.username, "AI听", []);
    });
  },
  methods: {
    flag_type_set() {
      alert("进来");
      this.flag_type = "2";
      console.log(this.flag_type);
    },
    //     downloadTemplate(type) {
    //   console.log('下载模板', type);
    //   // 这里添加下载逻辑
    // },
    handlePreview(isFolder) {
      if (isFolder) {
        this.$refs.uploadFolder.$refs.input.click();
      } else {
        this.$refs.uploadFile.$refs.input.click();
      }
    },
    handleChange(file, fileList) {
      // 这里处理文件或文件夹选择的逻辑
      console.log("选择的文件:", file);
      this.fileList = fileList;
    },
    // },

    // };
    downloadTableTemplate(c1, c2) {
      //  flag=c1,
      //  file_type=c2;
      let params = {
        // check_id: c1.check_id,
        // flag:this.radio,
        flag: c1,
        file_type: c2,
      };
      // if (c1.percentage == 100 && c1.status == "成功") {
      excel_download(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            // 创建一个 URL 对象
            const url = window.URL.createObjectURL(blob); // 创建一个 <a> 元素
            const a = document.createElement("a"); // 设置下载属性
            a.href = url;
            a.download = `模板`;
            document.body.appendChild(a); // 触发点击事件
            a.click(); // 移除 <a> 元素
            document.body.removeChild(a); // 释放 URL 对象
            window.URL.revokeObjectURL(url);
            this.downdia = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
      // }
      // else {
      //   this.$message({
      //     message: "暂无可下载文件",
      //     type: "error",
      //   });
      // }
    },

    radioChange() {
      this.form.fileList = []; // 切换时清空文件列表
    },
    handlePreview(bool) {
      console.log("选择文件/文件夹", bool);
      // 这里添加文件选择逻辑
    },
    up1(c1) {
      this.adddia = true;
      this.upload_type = c1;
      // this.$router.push("/maxkbup");
    },
    clickgosygl() {
      this.$router.push("/sygl");
    },
    addTag() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea1();
    },
    addTag1() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea2();
    },
    addTag2() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea3();
    },
    addTag3() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea4();
    },
    addTag4() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea5();
    },
    addTag5() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea6();
    },
    addTag6() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea7();
    },
    addTag7() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea8();
    },
    addTag8() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea9();
    },
    addTag9() {
      this.showInput = false;
      if (this.inputValue && !this.tags.includes(this.inputValue)) {
        this.tags.push(this.inputValue);
        this.inputValue = "";
      }
      this.sea10();
    },
    removeTag(index) {
      this.tags.splice(index, 1);
      this.sea1();
      this.sea2();
      this.sea3();
      this.sea4();
      this.sea5();
      this.sea6();
      this.sea7();
      this.sea8();
      this.sea9();
      this.sea10();
    },
    handleSearch() {
      this.showInput = true;
    },
    clickpb() {
      this.$router.push("/pb_manage");
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    show1(c1) {
      this.currentWznr = c1.file_content.replace(/\n/g, "<br>");
      this.dialogVisible = true;
    },
    showdia11(row) {
      let params = {
        username: this.username,
        type: "述职报告",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    showdia22(row) {
      let params = {
        username: this.username,
        type: "心得体会",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    showdia33(row) {
      let params = {
        username: this.username,
        type: "领导讲话",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    showdia44(row) {
      let params = {
        username: this.username,
        type: "工作方案",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    showdia55(row) {
      let params = {
        username: this.username,
        type: "调研报告",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    showdia66(row) {
      let params = {
        username: this.username,
        type: "宣传材料",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    showdia77(row) {
      let params = {
        username: this.username,
        type: "其他",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    showdia88(row) {
      let params = {
        username: this.username,
        type: "会议纪要",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    // showdia99 ai文的下载
    showdia99(row) {
      let params = {
        username: this.username,
        type: "文",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    //  ai听下载
    showdia00(row) {
      let params = {
        username: this.username,
        type: "AI听",
        filename: row.file_id,
      };
      download_knowledge_base_list(params)
        .then((blob) => {
          if (blob && blob.size > 0) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${row.file_name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            this.dialogVisible = false;
          } else {
            this.$message({
              message: "下载失败: 无效的文件",
              type: "error",
            });
          }
        })
        .catch((error) => {
          console.error("下载时出现错误:", error);
          this.$message({
            message: "下载失败，请稍后重试",
            type: "error",
          });
        });
    },
    async showdia1(c1) {
      let params = {
        username: this.username,
        type: "述职报告",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz1(
          this.currentPage,
          this.pageSize,
          this.username,
          "述职报告",
          []
        );
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia2(c1) {
      let params = {
        username: this.username,
        type: "心得体会",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz2(
          this.currentPage,
          this.pageSize,
          this.username,
          "心得体会",
          []
        );
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia3(c1) {
      let params = {
        username: this.username,
        type: "领导讲话",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz3(
          this.currentPage,
          this.pageSize,
          this.username,
          "领导讲话",
          []
        );
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia4(c1) {
      let params = {
        username: this.username,
        type: "工作方案",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz4(
          this.currentPage,
          this.pageSize,
          this.username,
          "工作方案",
          []
        );
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia5(c1) {
      let params = {
        username: this.username,
        type: "调研报告",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz5(
          this.currentPage,
          this.pageSize,
          this.username,
          "调研报告",
          []
        );
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia6(c1) {
      let params = {
        username: this.username,
        type: "宣传材料",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz6(
          this.currentPage,
          this.pageSize,
          this.username,
          "宣传材料",
          []
        );
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia7(c1) {
      let params = {
        username: this.username,
        type: "其他",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz7(this.currentPage, this.pageSize, this.username, "其他", []);
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia8(c1) {
      let params = {
        username: this.username,
        type: "会议纪要",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz8(
          this.currentPage,
          this.pageSize,
          this.username,
          "会议纪要",
          []
        );
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia9(c1) {
      let params = {
        username: this.username,
        type: "文",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz9(this.currentPage, this.pageSize, this.username, "文", []);
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia10(c1) {
      let params = {
        username: this.username,
        type: "AI听",
        filename: [c1.file_id],
      };
      let res = await delete_knowledge_base_list(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getwz10(
          this.currentPage,
          this.pageSize,
          this.username,
          "AI听",
          []
        );
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    handleSizeChange1(val) {
      this.getwz1(this.currentPage, val, this.username, "述职报告", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange1(val) {
      this.getwz1(val, this.pageSize, this.username, "述职报告", this.tags);
      this.currentPage = val;
    },
    handleSizeChange2(val) {
      this.getwz2(this.currentPage, val, this.username, "心得体会", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange2(val) {
      this.getwz2(val, this.pageSize, this.username, "心得体会", this.tags);
      this.currentPage = val;
    },
    handleSizeChange3(val) {
      this.getwz3(this.currentPage, val, this.username, "领导讲话", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange3(val) {
      this.getwz3(val, this.pageSize, this.username, "领导讲话", this.tags);
      this.currentPage = val;
    },
    handleSizeChange4(val) {
      this.getwz4(this.currentPage, val, this.username, "工作方案", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange4(val) {
      this.getwz4(val, this.pageSize, this.username, "工作方案", this.tags);
      this.currentPage = val;
    },
    handleSizeChange5(val) {
      this.getwz5(this.currentPage, val, this.username, "调研报告", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange5(val) {
      this.getwz5(val, this.pageSize, this.username, "调研报告", this.tags);
      this.currentPage = val;
    },
    handleSizeChange6(val) {
      this.getwz6(this.currentPage, val, this.username, "宣传材料", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange6(val) {
      this.getwz6(val, this.pageSize, this.username, "宣传材料", this.tags);
      this.currentPage = val;
    },
    handleSizeChange7(val) {
      this.getwz7(this.currentPage, val, this.username, "其他", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange7(val) {
      this.getwz7(val, this.pageSize, this.username, "其他", this.tags);
      this.currentPage = val;
    },
    handleSizeChange8(val) {
      this.getwz8(this.currentPage, val, this.username, "会议纪要", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange8(val) {
      this.getwz8(val, this.pageSize, this.username, "会议纪要", this.tags);
      this.currentPage = val;
    },
    handleSizeChange9(val) {
      this.getwz9(this.currentPage, val, this.username, "文", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange9(val) {
      this.getwz9(val, this.pageSize, this.username, "文", this.tags);
      this.currentPage = val;
    },
    handleSizeChange10(val) {
      this.getwz10(this.currentPage, val, this.username, "AI听", this.tags);
      this.pageSize = val;
    },
    handleCurrentChange10(val) {
      this.getwz10(val, this.pageSize, this.username, "AI听", this.tags);
      this.currentPage = val;
    },

    async getwz1(c1, c2, c3, c4, c5) {
      let params1 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res1 = await selectPage_knowledge_base_list(params1);
      if (res1.data.status_code == 200) {
        this.tableData1 = res1.data.data;
        this.total = res1.data.total;
      } else {
        this.$message({
          message: res1.data.message,
          type: "error",
        });
      }
    },
    async getwz2(c1, c2, c3, c4, c5) {
      let params2 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res2 = await selectPage_knowledge_base_list(params2);
      if (res2.data.status_code == 200) {
        this.tableData2 = res2.data.data;
        this.total1 = res2.data.total;
      } else {
        this.$message({
          message: res2.data.message,
          type: "error",
        });
      }
    },
    async getwz3(c1, c2, c3, c4, c5) {
      let params2 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res2 = await selectPage_knowledge_base_list(params2);
      if (res2.data.status_code == 200) {
        this.tableData3 = res2.data.data;
        this.total2 = res2.data.total;
      } else {
        this.$message({
          message: res2.data.message,
          type: "error",
        });
      }
    },
    async getwz4(c1, c2, c3, c4, c5) {
      let params2 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res2 = await selectPage_knowledge_base_list(params2);
      if (res2.data.status_code == 200) {
        this.tableData4 = res2.data.data;
        this.total3 = res2.data.total;
      } else {
        this.$message({
          message: res2.data.message,
          type: "error",
        });
      }
    },
    async getwz5(c1, c2, c3, c4, c5) {
      let params2 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res2 = await selectPage_knowledge_base_list(params2);
      if (res2.data.status_code == 200) {
        this.tableData5 = res2.data.data;
        this.total4 = res2.data.total;
      } else {
        this.$message({
          message: res2.data.message,
          type: "error",
        });
      }
    },
    async getwz6(c1, c2, c3, c4, c5) {
      let params2 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res2 = await selectPage_knowledge_base_list(params2);
      if (res2.data.status_code == 200) {
        this.tableData6 = res2.data.data;
        this.total5 = res2.data.total;
      } else {
        this.$message({
          message: res2.data.message,
          type: "error",
        });
      }
    },
    async getwz7(c1, c2, c3, c4, c5) {
      let params2 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res2 = await selectPage_knowledge_base_list(params2);
      if (res2.data.status_code == 200) {
        this.tableData7 = res2.data.data;
        this.total6 = res2.data.total;
      } else {
        this.$message({
          message: res2.data.message,
          type: "error",
        });
      }
    },
    async getwz8(c1, c2, c3, c4, c5) {
      let params2 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res2 = await selectPage_knowledge_base_list(params2);
      if (res2.data.status_code == 200) {
        this.tableData8 = res2.data.data;
        this.total7 = res2.data.total;
      } else {
        this.$message({
          message: res2.data.message,
          type: "error",
        });
      }
    },
    async getwz9(c1, c2, c3, c4, c5) {
      let params2 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res2 = await selectPage_knowledge_base_list(params2);
      if (res2.data.status_code == 200) {
        this.tableData9 = res2.data.data;
        this.total8 = res2.data.total;
      } else {
        this.$message({
          message: res2.data.message,
          type: "error",
        });
      }
    },
    async getwz10(c1, c2, c3, c4, c5) {
      let params2 = {
        pageNo: c1,
        pageSize: c2,
        username: c3,
        type: c4,
        filename: c5,
      };
      let res2 = await selectPage_knowledge_base_list(params2);
      if (res2.data.status_code == 200) {
        this.tableData10 = res2.data.data;
        this.total9 = res2.data.total;
      } else {
        this.$message({
          message: res2.data.message,
          type: "error",
        });
      }
    },
    beforeAvatarUpload(files) {
      if (this.form.fileType == "QA") {
        this.flag_type = "3";
      }
      if (this.form.fileType == "table") {
        this.flag_type = "2";
      }
      if (this.form.fileType == "txt") {
        this.flag_type = "1";
      }
      this.loading = true;
      this.mask = true;
      this.text = false;
      const formData = new FormData();

      const fileArray = Array.isArray(files) ? files : [files];

      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result;
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              );
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file);
          });
        })
      )
        .then(() => {
          if (this.upload_type == "1") {
            formData.append("file_type", "述职报告");
            formData.append("name", this.username);

            formData.append("flag", this.flag_type);

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();
            setTimeout(() => {
              this.getwz1(
                this.currentPage,
                this.pageSize,
                this.username,
                "述职报告",
                []
              );
            }, 1000);
          } else if (this.upload_type == "2") {
            formData.append("file_type", "心得体会");
            formData.append("name", this.username);
            formData.append("flag", this.flag_type);

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();

            setTimeout(() => {
              this.getwz2(
                this.currentPage,
                this.pageSize,
                this.username,
                "心得体会",
                []
              );
            }, 1000);
          } else if (this.upload_type == "3") {
            formData.append("file_type", "领导讲话");
            formData.append("name", this.username);
            formData.append("flag", this.flag_type);

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();
            setTimeout(() => {
              this.getwz3(
                this.currentPage,
                this.pageSize,
                this.username,
                "领导讲话",
                []
              );
            }, 1000);
          } else if (this.upload_type == "4") {
            formData.append("file_type", "工作方案");
            formData.append("name", this.username);
            formData.append("flag", this.flag_type);

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();
            setTimeout(() => {
              this.getwz4(
                this.currentPage,
                this.pageSize,
                this.username,
                "工作方案",
                []
              );
            }, 1000);
          } else if (this.upload_type == "5") {
            formData.append("file_type", "调研报告");
            formData.append("name", this.username);
            formData.append("flag", this.flag_type);

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();
            setTimeout(() => {
              this.getwz5(
                this.currentPage,
                this.pageSize,
                this.username,
                "调研报告",
                []
              );
            }, 1000);
          } else if (this.upload_type == "6") {
            formData.append("file_type", "宣传材料");
            formData.append("name", this.username);
            formData.append("flag", this.flag_type);

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();
            setTimeout(() => {
              this.getwz6(
                this.currentPage,
                this.pageSize,
                this.username,
                "宣传材料",
                []
              );
            }, 1000);
          } else if (this.upload_type == "7") {
            formData.append("file_type", "其他");
            formData.append("name", this.username);
            formData.append("flag", this.flag_type);

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();
            setTimeout(() => {
              this.getwz7(
                this.currentPage,
                this.pageSize,
                this.username,
                "其他",
                []
              );
            }, 1000);
          } else if (this.upload_type == "8") {
            formData.append("file_type", "会议纪要");
            formData.append("name", this.username);
            formData.append("flag", this.flag_type);

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();
            setTimeout(() => {
              this.getwz8(
                this.currentPage,
                this.pageSize,
                this.username,
                "会议纪要",
                []
              );
            }, 1000);
          } else if (this.upload_type == "9") {
            formData.append("name", this.username);
            formData.append("flag", this.flag_type);
            formData.append("file_type", "文");

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();
            setTimeout(() => {
              this.getwz9(
                this.currentPage,
                this.pageSize,
                this.username,
                "文",
                []
              );
            }, 1000);
          } else if (this.upload_type == "10") {
            formData.append("file_type", "AI听");
            formData.append("name", this.username);
            formData.append("flag", this.flag_type);

            this.formData = formData;
            console.log("FormData 内容:", this.formData.getAll("files[]"));
            this.scfw();
            setTimeout(() => {
              this.getwz10(
                this.currentPage,
                this.pageSize,
                this.username,
                "AI听",
                []
              );
            }, 1000);
          }

          this.loading = false;
          this.mask = false;
          this.adddia = false;
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
          this.loading = false;
          this.loading = false;
          this.mask = false;
          this.adddia = false;
        });
    },
    async scfw() {
      console.log("FormData123456:", this.formData.getAll("files[]"));
      if (!this.formData) {
        console.error("没有文件被上传");
        return;
      }
      try {
        let res = await fwsc(this.formData);
        console.log("w看一下返回值结果:", res.data.status_code);
        if (res.data.status_code == 200) {
          this.$message({
            message: "上传成功",
            type: "success",
          });
        } else {
          this.$message({
            message: "上传失败",
            type: "error",
          });
        }
      } catch (error) {
        console.error("上传失败:", error);
      }
    },
    clickzhgl() {
      this.$router.push("/grzx2");
    },
    clickzskgl() {
      this.$router.push("/zsk");
    },
    clearInput() {
      this.inputvalyhm = "";
    },
    handleSelectionChange(selected) {
      this.selectedUsers = selected.map((user) => user.file_id);
    },
    async batchDelete1(c1) {
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            username: this.username,
            type: c1,
            filename: this.selectedUsers,
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = [];
              this.getwz1(1, this.pageSize, this.username, "述职报告", []);
              this.currentPage = 1;
              // this.
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async batchDelete2(c1) {
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            username: this.username,
            type: c1,
            filename: this.selectedUsers,
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = [];
              this.getwz2(1, this.pageSize, this.username, "心得体会", []);
              this.currentPage = 1;
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async batchDelete3(c1) {
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            username: this.username,
            type: c1,
            filename: this.selectedUsers,
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = [];
              this.getwz3(1, this.pageSize, this.username, "领导讲话", []);
              this.currentPage = 1;
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async batchDelete4(c1) {
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            username: this.username,
            type: c1,
            filename: this.selectedUsers,
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = [];
              this.getwz4(1, this.pageSize, this.username, "工作方案", []);
              this.currentPage = 1;
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async batchDelete5(c1) {
      // 检查是否有选中的文件
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            username: this.username,
            type: c1,
            filename: this.selectedUsers, // 将选中的文件名数组发送给后端
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的文件数组
              // 刷新文件列表
              this.getwz5(1, this.pageSize, this.username, "调研报告", []);
              this.currentPage = 1;
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async batchDelete7(c1) {
      // 检查是否有选中的文件
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            username: this.username,
            type: c1,
            filename: this.selectedUsers, // 将选中的文件名数组发送给后端
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的文件数组
              // 刷新文件列表
              this.getwz7(1, this.pageSize, this.username, "其他", []);
              this.currentPage = 1;
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async batchDelete8(c1) {
      // 检查是否有选中的文件
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            username: this.username,
            type: "会议纪要",
            filename: this.selectedUsers, // 将选中的文件名数组发送给后端
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的文件数组
              // 刷新文件列表
              this.getwz8(1, this.pageSize, this.username, "会议纪要", []);
              this.currentPage = 1;
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async batchDelete9(c1) {
      // 检查是否有选中的文件
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            username: this.username,
            type: c1,
            filename: this.selectedUsers, // 将选中的文件名数组发送给后端
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的文件数组
              // 刷新文件列表
              this.getwz9(1, this.pageSize, this.username, "文", []);
              this.currentPage = 1;
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async batchDelete10(c1) {
      // 检查是否有选中的文件
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            username: this.username,
            type: c1,
            filename: this.selectedUsers, // 将选中的文件名数组发送给后端
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的文件数组
              // 刷新文件列表
              this.getwz10(1, this.pageSize, this.username, "AI听", []);
              this.currentPage = 1;
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async batchDelete6(c1) {
      // 检查是否有选中的文件
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            username: this.username,
            type: c1,
            filename: this.selectedUsers, // 将选中的文件名数组发送给后端
          };
          try {
            let res = await delete_knowledge_base_list(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的文件数组
              // 刷新文件列表
              this.getwz6(1, this.pageSize, this.username, "宣传材料", []);
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async sea1() {
      this.getwz1(1, 10, this.username, "述职报告", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    async sea2(c5) {
      this.getwz2(1, 10, this.username, "心得体会", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    async sea3(c5) {
      this.getwz3(1, 10, this.username, "领导讲话", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    async sea4(c5) {
      this.getwz4(1, 10, this.username, "工作方案", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    async sea5(c5) {
      this.getwz5(1, 10, this.username, "调研报告", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    async sea6(c5) {
      this.getwz6(1, 10, this.username, "宣传材料", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    async sea7(c5) {
      this.getwz7(1, 10, this.username, "其他", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    async sea8(c5) {
      this.getwz8(1, 10, this.username, "会议纪要", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    async sea9(c5) {
      this.getwz9(1, 10, this.username, "文", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    async sea10(c5) {
      this.getwz10(1, 10, this.username, "AI听", this.tags);
      this.inputvalyhm = ""; // 清空输入框内容
    },
    clickgrzx() {
      this.$router.push("/grzx1");
    },

    togrzx() {
      this.$router.push("/grzx1");
    },

    async scfw() {
      console.log("FormData123456:", this.formData.getAll("files[]"));
      if (!this.formData) {
        console.error("没有文件被上传");
        return;
      }
      try {
        let res = await fwsc(this.formData); // 直接传递 formData 进行上传
        console.log("w看一下返回值结果:", res.data.status_code);
        if (res.data.status_code == 200) {
          this.$message({
            message: "上传成功",
            type: "success",
          });
        } else {
          this.$message({
            message: "上传失败",
            type: "error",
          });
        }
      } catch (error) {
        console.error("上传失败:", error);
      }
    },

    togrzx() {
      this.$router.push("/grzx1");
    },
    togrzx() {
      // alert(1)
      this.$router.push("/grzx1");
    },
    closeDialog() {
      this.dialogShow = false;
    },
    textarea2Focus() {
      this.dialog2Show = true;
    },
    closeDialog2() {
      this.dialog2Show = false;
    },
    clickszbg() {
      this.$router.push("/xz");
    },
    //角色
    clickTopld() {
      this.$router.push("/grzx");
    },
    clickTopLd() {
      this.$router.push("/sfw");
    },
    //   提示词
    clickToptsc() {
      this.$router.push("/tsc");
    },
    clickTab(i) {
      this.curIndex = i;
      // }
    },
  },
};
</script>
<style lang="less" scoped>
.dChangePwd .footerInline .tsFont {
  width: 100%;
  line-height: calc(100vh * 30 / 1080);
  margin-top: 0;
  color: #f60;
}

.ml {
  margin-left: 100px;
}
.ml1 {
  margin-left: 120px;
}
.ml2 {
  margin-left: 100px;
}
::v-deep .el-descriptions-item__label.has-colon::after {
  content: "";
  margin-top: 20px;
}

.el-tag {
  background: #f5f8fc;
  border-radius: 12px;
  padding: calc(100vw * 4 / 1920);
}

.btnr {
  margin-top: calc(100vh * 190 / 1080);
}
.btn-group {
  background: url(../img/bga.png) no-repeat center;
  background-size: 45%;
}
.btn-group1 {
  // margin-right: 500px;
  background: url(../img/hahu3.png) no-repeat center;
  background-size: 45%;
}

.left1 {
  background: #ffffff;
  border-radius: 8px;
}
.next_but {
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  width: calc(100vw * 130 / 1920);
  height: calc(100vh * 40 / 1080);
  border-radius: 20px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 600;
  &:hover {
    color: #ffffff;
  }
  &:active {
    color: #ffffff;
  }
}
.right {
  background: url(../img/bga.png) no-repeat center;
  // width: 88%;
  // height: 100%;
  background-size: 70%;
  //  background: #FFFFFF;
  // border-radius: 8px;
}
.el-select {
  width: calc(100vw * 600 / 1920);
}
.el-input {
  width: calc(100vw * 600 / 1920);
  margin-left: -calc(100vw * 120 / 1920);
}
.fir-kuai2 {
  margin-right: calc(100vw * 10 / 1920);
  width: calc(100vw * 6 / 1920);
  height: calc(100vh * 16 / 1080);
  float: left;
  background: #4081ff;
  border-radius: 1.5px;
}

.el-upload__tip {
  margin-top: -calc(100vh * 20 / 1080);
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}

.parent-container .avatar-uploader .el-upload {
  border: 1px #d9d9d9 dashed !important;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: calc(100vw * 28 / 1920);
  color: #8c939d;
  width: calc(100vw * 178 / 1920);
  height: calc(100vh * 178 / 1080);
  line-height: calc(100vh * 178 / 1080);
  text-align: center;
}
.avatar {
  width: calc(100vw * 178 / 1920);
  height: calc(100vh * 178 / 1080);
  display: block;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.ai-left-bar-li {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh * 50 / 1080);
  width: calc(100vw * 1000 / 1920);
  // color: #000;

  line-height: 16px;
  white-space: nowrap;
  cursor: pointer;
  // z-index: 9999;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 19 / 1920);
  // color: #000000;
  letter-spacing: 0;
  text-align: center;
  background: url(../assets/jstext.png) no-repeat center;
}

.actived {
}
.context-menu {
  margin-left: -200px;
  position: absolute;
  // background: white;
  background: #fff;

  // border: 1px dashed #ccc;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
  // margin-left:-200px;
}

.context-menu li {
  padding: 8px 16px;
  cursor: pointer;
  // margin-left:-200px;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}

.fr {
  float: right;
}

.work-container {
  height: 100%;

  .result-container {
    padding: 30px;

    .elcol-title {
      display: flex;
      overflow: hidden;
      width: 100%;
      height: 48px;

      .elcol-title-text {
        // float: left;
        padding-left: 10px;
        text-align: left;
        width: 40%;
        height: 100%;
      }

      .elcol-title-text2 {
        font-size: calc(100vw * 14 / 1920);
        // color: #d1d7de;
        // float: left;
        width: 75px;
      }

      .elcol-input {
        float: left;
        width: calc(60% - 75px);
        border: none !important;
      }
    }
  }
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

.ejdhl {
  margin-top: calc(100vh * 43 / 1080);
  background-color: #fff;
  height: calc(100vh - (100vh * 43 / 1080));
  //  background: url(../assets/img-left.png) no-repeat center;
  // background-size: cover;
  // background-position: center;
  // transition: ease-out 0.4s;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(
    76deg,
    #07389c 0%,
    #3d86d1 0%,
    #3448b3 100%
  );

  img {
    width: 120px;
    height: 48px;
  }

  p {
    border: 1.54px dashed rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header1 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  background: url(../assets/jsbg.png) no-repeat center;
  background-color: #fff;
  .ai-header {
    width: 100%;
    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: 40px;
        height: calc(100vh * 40 / 1080);
        border-radius: 20px;
        // background: #f4f6f8;
        border: 1px dashed #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 42px;
            width: 130px;
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: 16px;
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vw * 16 / 1920);
              height: 16px;
              margin-right: 14px;
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: 20px;
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 16 / 1920);
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  // background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: calc(100% - calc(100vw * 300 / 1920));
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: calc(100vh * 20 / 1080) calc(100vw * 10 / 1920);
  overflow: hidden;

  .ai-gai {
    position: fixed;
    /* top: 0; */
    left: 200;
    bottom: 0;
    height: 91%;
    background: rgba(239, 239, 240, 0.6);
    z-index: 999;
  }

  .ai-body {
    transition: ease-out 0.4s;
    width: 100%;
    height: 100%;
    // background: red;
    float: left;
    overflow: hidden;

    .ai-body-left {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      width: 444px;
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-left: 10px;
      float: left;

      .ai-body-left-top {
        width: 100%;
        height: calc(100% - calc(100vh * 80 / 1080));
        overflow-y: scroll;
      }

      .ai-body-left-bottom {
        width: 100%;
        height: 90px;

        // padding-top: 30px;
        .ai-body-left-bottom-button {
          height: calc(100vh * 40 / 1080);
          // font-weight: 600;
          font-size: calc(100vw * 14 / 1920);
          letter-spacing: 0px;
          flex-grow: 1;
          color: #fff;
          margin: 0 25px;
          cursor: pointer;
          line-height: calc(100vh * 40 / 1080);
          margin-top: 30px;
          background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 20px;
          font-family: PingFangSC-Regular;
        }
      }

      .ai-body-left-bottom2 {
        width: 100%;
        height: calc(100vh * 80 / 1080);
        padding-top: 30px;

        .repeat {
          width: 188px;
          float: left;
          height: calc(100vh * 40 / 1080);
          font-size: calc(100vw * 14 / 1920);
          flex-grow: 1;
          margin-left: 25px;
          cursor: pointer;
          line-height: calc(100vh * 40 / 1080);
          background: #f4f6f8;
          border: 1px dashed rgba(230, 230, 230, 1);
          border-radius: 20px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #666666;
          letter-spacing: 0;
          text-align: center;
          // font-weight: 600;
        }

        .ai-body-left-bottom-button {
          width: 188px;
          float: left;
          height: calc(100vh * 40 / 1080);
          flex-grow: 1;
          margin-left: 14px;
          cursor: pointer;
          line-height: calc(100vh * 40 / 1080);
          background-image: linear-gradient(102deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 20px;
          margin-top: 0px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          // font-weight: 600;
        }
      }

      .ai-step {
        margin-top: 20px;
        width: calc(100% - 40px);
        margin: 0 20px;
        margin-top: 5px;
        height: calc(100vh * 40 / 1080);
      }
    }

    .ai-body-right {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      width: calc(100% - 480px);
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-left: 20px;
      float: left;

      .ai-body-start {
        width: 100%;
        height: 100%;

        .pic_bkg1 {
          // width: calc(100vw * 670 / 1920);
          // height: calc(100vw * 590 / 1920);
          width: 670px;
          height: 590px;
          background: url(../assets/img-bg1.png) no-repeat center;
          // background: url(../assets/img-bg1.png) no-repeat center;
          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 70px;
          position: relative;
        }

        .pic_bkg {
          width: 528px;
          height: 518px;
          // z-index: 15;
          background: url(../assets/img-ai.png) no-repeat center;
          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 118px;
          position: relative;
        }

        .pic_font {
          position: absolute;
          margin-left: auto;
          margin-right: auto;
          left: 0;
          right: 0;
          bottom: 116px;
          width: 255px;
          height: calc(100vh * 40 / 1080);
          border: 1.54px dashed rgba(0, 0, 0, 0);
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 28 / 1920);
          color: #000000;
          text-align: center;
          font-weight: 600;
        }

        .title_message {
          position: absolute;
          margin-left: auto;
          margin-right: auto;
          left: 0;
          right: 0;
          bottom: 82px;
          text-align: center;
          line-height: 16px;
          margin-top: 10px;
          height: 22px;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw * 14 / 1920);
          color: #999999;
          letter-spacing: 0;
          font-weight: 400;
        }

        .pic_step {
          width: 551px;
          height: 142px;
          z-index: 15;
          background: url("../assets/pic_step.png");
          background-size: contain;
          margin: auto;
        }
      }

      .ai-body-art {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;

        .over {
          overflow: auto;
        }

        .fir-textarea {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 96.5%;
          background: #f8f9fd;
          border: 1px dashed rgba(229, 232, 245, 1);
          border-radius: 4px;
          margin: 20px;
          margin-bottom: 0;
          height: 158px;

          ::v-deep(.el-textarea__inner) {
            font-size: calc(100vw * 14 / 1920) !important;
            background-color: #f8f9fd !important;
            height: 100% !important;
            font-family: PingFangSC-Regular;
            padding: 13px 18px 33px 16px;
          }
        }

        .fir-textarea-max {
          height: 95% !important;
        }

        ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
          display: none !important;
        }

        ::v-deep(.ql-blank) {
          display: none !important;
        }

        ::v-deep(.ql-container.ql-snow) {
          border: 0;
        }
      }
    }

    .ai-tab {
      width: 230px;
      height: calc(100vh * 40 / 1080);
      margin: 0 auto;
      margin-top: 30px;
      background: #f4f6f8;
      border: 1px dashed #eeeff0;
      border-radius: 20px;

      .tab-item {
        width: 50%;
        height: calc(100vh * 40 / 1080);
        // line-height: 16px;
        float: left;
        line-height: calc(100vh * 40 / 1080);
        cursor: pointer;

        font-family: PingFangSC-Semibold;
        font-size: calc(100vw * 14 / 1920);
        color: #9094a5;
        letter-spacing: 0;
        text-align: center;
      }

      .activedTab {
        border-radius: 20px;

        background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
        box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
        color: #ffffff;
      }
    }

    .tab-item-fir {
      width: 100%;
      height: 536px;
      padding: 0 25px;

      .fir-title {
        color: #222;
        font-weight: 500;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 16px;
        overflow: hidden;
        margin-top: 20px;
        margin-bottom: 20px;
        height: 20px;

        .fir-kuai {
          width: calc(100vw * 6 / 1920);
          height: 16px;
          margin-right: 8px;
          float: left;
          // margin-top: 2px;
          background: #4081ff;
          border-radius: 1.5px;
        }

        .fir-title-p {
          line-height: 16px;
          float: left;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw * 14 / 1920);
          color: #333333;
          letter-spacing: 0;
          font-weight: 400;
        }
      }

      .fir-alert {
        margin-top: 10px;
        height: 35px;
      }

      .ai-dialog {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        row-gap: 1px;
        width: 100%;
        height: -moz-fit-content;
        height: 290px;
        max-height: 294px;
        padding: 7px;
        box-shadow: 0 20px 40px 4px #e4e4e524;
        margin-top: 10px;
        transition: ease-out 0.4s;
        background: #ace9ff;
        border: 1px dashed rgba(90, 206, 255, 1);
        border-radius: 4px;

        .ai-d-title {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          width: 100%;
          height: -moz-fit-content;
          height: fit-content;
          margin: 0;
          padding: 1px 3px 2px 2px;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw * 14 / 1920);
          color: #313733;
          letter-spacing: 0;
          font-weight: 400;

          .ai-d-title-p {
            flex-grow: 1;
            line-height: 16px;
            text-align: left;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #313733;
            letter-spacing: 0;
            font-weight: 400;
            margin-bottom: 10px;
          }

          img {
            width: 18px;
            height: 18px;
            cursor: pointer;
          }
        }

        .ai-d-body {
          width: 100%;
          height: calc(100% - 44px);
          overflow: hidden;
          background: #ffffff;
          border-radius: 4px;

          .hints-control {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
            padding: 14px 14px 0;
            height: 30px;

            .hint-icon {
              flex-grow: 0;
              flex-shrink: 0;
              width: 20px;
              height: 20px;
              margin-right: 6px;
              background-size: contain;
              background-image: url("../assets/icon_fire.png");
            }

            .hint-description {
              font-weight: 600;
              line-height: 14px;
              font-family: SourceHanSansSC-Bold;
              font-size: calc(100vw * 14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 700;
            }
          }

          .ai-tj-body {
            width: 100%;
            // height: 100%;
            // overflow: hidden;
            height: 200px;
            overflow-y: auto;

            /* 垂直滚动条 */
            .ai-tj-item {
              padding: 14px 14px 0;
              line-height: 12px;
              width: 50%;
              text-align: left;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              float: left;
              cursor: pointer;
              height: 30px;
              font-family: PingFangSC-Regular;
              font-size: calc(100vw * 14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 400;
              // &:hover {

              // }
            }
          }
        }
      }

      // ***滚动条样式
      .ai-tj-body::-webkit-scrollbar {
        width: calc(100vw * 6 / 1920);
        /* 滚动条的宽度 */
      }

      .ai-tj-body::-webkit-scrollbar-track {
        background: #fff;
        /* 滚动条的背景色 */
      }

      .ai-tj-body::-webkit-scrollbar-thumb {
        background: #488aff;
        /* 滚动条的滑块颜色 */
      }

      .fir-textarea {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        margin-top: 14px;
        height: 158px;
        background: #f8f9fd;
        // border: 1px dashed rgba(229, 232, 245, 1);
        border-radius: 4px;

        ::v-deep(.el-textarea__inner) {
          font-size: calc(100vw * 14 / 1920) !important;
          background-color: #f8f9fd !important;
          height: 100% !important;
          font-family: PingFangSC-Regular;
        }
      }

      .fir-textarea-height {
        height: 460px !important;
      }
    }
  }
}

::v-deep(.el-textarea__inner) {
  padding: 16px;
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: 10px;
  height: 10px;
}

.ai-nav {
  // width: 180px;
  // height: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  transition: ease-out 0.4s;

  .title {
    width: 300px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    letter-spacing: 0px;
    height: 20px;
    line-height: 20px;
    padding-top: 20px;
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    width: 100%;
    padding-top: 45px;
    height: calc(100% - calc(100vh * 80 / 1080));
  }
}

.user-menu-content {
  width: calc(100vw * 246 / 1920);
  height: calc(100vh * 24 / 1080);
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px dashed rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw * 16 / 1920);
        font-size: calc(100vw * 15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw * 12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px dashed #f0f3fa;
  }

  .options {
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;
      cursor: pointer;

      :first-child {
        margin-top: 0px;
      }

      .icon {
        width: 24px;
        height: calc(100vh * 24 / 1080);
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

// 导航项
.nav-item {
  height: calc(100vh * 40 / 1080);
  background: transparent;
  line-height: calc(100vh * 20 / 1080);
  white-space: pre-wrap;
  background: #f4f6f8;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
  cursor: pointer;
  // margin: 0 auto;
  margin-top: calc(100vh * 20 / 1080);
  margin-left: calc(100vw * 100 / 1920);
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #9094a5;
  border-radius: 20px;
  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-family: PingFangSC-Semibold;
    letter-spacing: 0;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}
.choose {
  border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  color: #ffffff;
}

.clbutton {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 40px;
}

.clbutton1 {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}

.clbutton2 {
  left: 180px;
}

.clbutton12 {
  // left: 200px;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 280px;
}

.showUp {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 60px;
  height: calc(100vh * 40 / 1080);
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.showUp1 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 6px;
  height: calc(100vh * 40 / 1080);
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 130px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-drawer__header) {
  display: none;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-notification__group) {
  height: auto !important;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: calc(100vw * 1 / 1920);
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: calc(100vw * 14 / 1920) !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.progressClass {
  width: 100%;
  height: 26px;
  position: fixed;
  bottom: 48px;
  right: 0;
  z-index: 999;

  .progress {
    position: absolute;
    right: 52px;
    transition: ease-out 0.4s;
  }
}

::v-deep(.el-progress-bar__innerText) {
  padding: 7px !important;
  color: #fff !important;
}

.menu_label {
  width: 100%;
  border-radius: 4px;
  margin-top: 10px;
  min-height: 158px;
  height: auto;
  padding: 15px 16px;
  // max-height: 158px;
  background: #f8f9fd;
  border: 1px dashed rgba(229, 232, 245, 1);
  border-radius: 4px;
  padding: 15px 0px;
}

.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: 383px;
  word-wrap: break-word;
  text-align: left;
  margin-left: 5px;
  margin-bottom: 5px;
  float: left;
}

.el-main .ai-body .tab-item-fir .menu_label:active {
  background: #fff;
  border: 1px dashed #4170f6;
}

.pass_input {
  // float: left;
  width: 100%;
  height: calc(100vh * 40 / 1080);
}

::v-deep(.el-alert__title) {
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-drawer__open .el-drawer.ltr) {
  width: 100% !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0;
}

::v-deep(.el-alert) {
  padding: 8px 8px;
}
.fir-textarea1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: 1px dashed rgba(229, 232, 245, 1);
  border-radius: 4px;
  margin: 20px;
  margin-bottom: 0;
  height: 158px;

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: 13px 18px 33px 16px;
  }
}

::v-deep(.el-collapse) {
  border: 0px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: 1px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40 / 1080);
  border-radius: 4px;
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: 4px;
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: 4px;
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: -10px;
    top: 2px;
    width: calc(100vw * 4 / 1920);
    height: 15px;
    background: #5585f0;
    border-radius: 2px;
  }
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
  /* 滚动条的宽度 */
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
  /* 滚动条的背景色 */
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
  /* 滚动条的滑块颜色 */
}

.elcol-title-left {
  width: calc(100vw * 8 / 1920);
  height: 8px;
  border-radius: 50%;
  margin-top: 20px;
  margin-left: 10px;
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 48px;
}

.elcol-title-text {
  // float: left;
  padding-left: 10px;
  text-align: left;
  width: 40%;
  height: 100%;
}

.elcol-title-text2 {
  font-size: calc(100vw * 14 / 1920);
  color: #d1d7de;
  // float: left;
  width: 75px;
}

.elcol-input {
  float: left;
  width: calc(60% - 75px);
  border: none !important;
}

/* 整个滚动条 */
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: calc(100vw * 10 / 1920);
  /* 对应横向滚动条的宽度 */
  height: 10px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: gray;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #fff;
  border-radius: 32px;
}
.el-icon {
  --color: inherit;
  height: 1em;
  width: 1em;
  line-height: 1em;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  fill: currentColor;
  color: var(--color);
  font-size: inherit;
  margin-right: 10px;
}
.el-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  height: calc(100vh * 32 / 1080);
  white-space: nowrap;
  cursor: pointer;
  padding: calc(100vh * 15 / 1080) calc(100vw * 20 / 1920);
  font-size: calc(100vw * 14 / 1920);
}
.el-input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  height: 35px;
  line-height: 35px;
  outline: 0;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
:deep(.el-dialog__header) {
  padding: 15px 20px 10px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  font-weight: 700;
  // border-bottom: 1px solid var(--vxe-modal-border-color);
  // background-color: var(--vxe-modal-header-background-color);
  border-radius: var(--vxe-border-radius) var(--vxe-border-radius) 0 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
:deep(.el-dialog__title) {
  font-size: calc(100vw * 15 / 1920);
  color: #606266;
}
:deep(.el-form .el-form-item__label) {
  font-weight: 700;
  // margin-right:-100px;
}
:deep(.el-form-item) {
  margin-bottom: 20px;
}
:deep(.el-form-item__label) {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: calc(100vw * 14 / 1920);
  color: #606266;
  line-height: calc(100vh * 40 / 1080);
  box-sizing: border-box;
  margin-left: calc(100vw * 60 / 1920);
}
.custom-label {
  margin-top: calc(100vh * 10 / 1080);
  font-weight: 600;
  font-size: calc(100vw * 18 / 1920);
  letter-spacing: 0px;
  line-height: calc(100vh * 16 / 1080);
}

.LgscCon {
  width: calc(100vw * 1500 / 1920);
  height: auto;
  margin: 0 auto;
  .button {
    width: calc(100vw * 108 / 1920);
    height: calc(100vh * 58 / 1080);
    background: #4c91ff;
    border-radius: 0px 100px 100px 0px;
    cursor: pointer;
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    margin-right: calc(100vw * 8 / 1920);
  }

  p {
    line-height: calc(100vh * 58 / 1080);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}
.el-tabs__content {
  // overflow: hidden;
  // position: relative;
  width: 1160px;
  margin-left: -100px;
}
::v-deep {
  .el-tabs {
    height: 100%;
    overflow: hidden;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    .el-tabs__active-bar {
      width: calc(100vw * 107 / 1920);
      background-color: transparent !important;
    }
  }
}
.multi-input {
  height: calc(100vh * 32 / 1080);
  box-sizing: border-box;
  border: 1px solid rgb(220, 223, 230);
  border-radius: 4px;
  position: relative;
  padding-left: calc(100vw * 3 / 1920);
  padding-right: calc(100vw * 32 / 1920);
  display: flex;
  align-items: center;
  margin-left: calc(100vw * 715 / 1920);
}
.multi-input_1 {
  height: calc(100vh * 32 / 1080);
  box-sizing: border-box;
  border: 1px solid rgb(220, 223, 230);
  border-radius: 4px;
  position: relative;
  padding-left: calc(100vw * 3 / 1920);
  padding-right: calc(100vw * 32 / 1920);
  display: flex;
  align-items: center;
  margin-left: calc(100vw * 715 / 1920);
}
.plusBtn {
  position: absolute;
  right: calc(100vw * 3 / 1920);
  top: calc(100vh * 3 / 1080);
  height: calc(100vh * 24 / 1080);
  width: calc(100vw * 15 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-sizing: border-box;
  border: 1px solid rgb(64, 158, 255);
  border-radius: 4px;
}
.input-new-tag {
  width: 200px;
  margin-left: 1px;
  font-size: calc(100vw * 12 / 1920);
  height: calc(100vh * 24 / 1080);
}
/deep/ .el-input--small .el-input__inner {
  height: calc(100vh * 24 / 1080);
  line-height: calc(100vh * 24 / 1080);
  padding: 0 7px;
}
.el-tag.is-closable {
  padding-right: 5px;
}
.el-tag {
  height: calc(100vh * 24 / 1080);
  padding: 0 9px;
  font-size: calc(100vw * 12 / 1920);
  margin: 0px;
  border-radius: 4px;
  // display: flex;
  line-height: calc(100vh * 24 / 1080);
}
.el-tag .el-tag__close {
  margin-left: 6px;
}
::v-deep .el-tabs__item {
  padding: 0 0px;
  height: calc(100vh * 40 / 1080);
  line-height: calc(100vh * 40 / 1080);
  font-size: calc(100vw * 14 / 1920);
}

::v-deep(.el-table .cell) {
  line-height: calc(100vh * 23 / 1080) !important;
  padding-left: calc(100vw * 10 / 1920) !important;
  padding-right: calc(100vw * 10 / 1920) !important;
}
::v-deep .el-table .el-table__cell {
  padding: calc(100vh * 12 / 1080) 0;
  font-size: calc(100vw * 14 / 1920);
}
::v-deep(.el-tabs__header) {
  margin: 0 0 calc(100vh * 15 / 1080);
}
::v-deep(.el-tabs__nav-wrap::after) {
  height: calc(100vh * 2 / 1080);
}

.update-info {
  background: #d6e2ff;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.upload-area {
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}
.upload-area {
  padding: 20px;
  text-align: center;
  border: 1px dashed #ccc;
}

.upload__decoration {
  margin-top: 10px;
  color: #666;
}
</style>