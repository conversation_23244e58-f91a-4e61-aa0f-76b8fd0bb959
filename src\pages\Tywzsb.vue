<!-- ai读 -->
<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle"></div>
        <div class="ai-header">
          <div class="ai-bar">
            <HeadNavigation/>
          </div>
        </div>
      </el-header>
      <el-main
        v-loading="loading"
        element-loading-text="请稍候..."
        element-loading-background="rgba(255, 255, 255, 0.7)"
      >
        <el-tabs v-model="activeTab">
          <el-tab-pane label="识别" name="shibie">
            <div
              class="con flexLd"
              v-loading="con_loading"
              style="position: relative"
            >
              <!-- 弹窗 -->
              <div
                style="
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 60%;
                  height: 100%;
                  background-color: #fff;
                  z-index: 1;
                  display: flex;
                "
                v-if="jiaoyanjc"
              >
                <div
                  class=""
                  style="
                    width: 60%;
                    height: 100%;
                    margin-right: 5%;
                    overflow: scroll;
                  "
                >
                  <img
                    v-if="type && type == 'img'"
                    :src="'data:image/jpeg;base64,' + imgURlcw"
                    alt=""
                    style="width: 100%; margin-top: 20px"
                  />
                  <div v-if="type && type == 'pdf'" width="100%" height="100%">
                    <pdf
                      v-for="i in numPages"
                      :key="i"
                      :src="filePdfUrl"
                      :page="i"
                      width="100%"
                    ></pdf>
                  </div>
                  <div
                    v-if="type && type == 'word'"
                    v-html="wordHtmlContent"
                  ></div>
                  <!-- 回显txt -->

                  <!-- <div v-if="type && type == 'txt'" v-html="txtContent"></div> -->
                </div>
                <div
                  class=""
                  style="
                    width: 30%;
                    height: 100%;
                    margin-right: 5%;
                    position: relative;
                  "
                >
                  <div
                    style="height: 85%; overflow: overlay; margin-top: 20px"
                    class="result-content"
                  >
                    <div v-for="(item, index) in correctionList">
                      <el-collapse
                        v-model="activeNames"
                        @change="highlightError(item.source, item.wordNo)"
                        style="margin-bottom: 10px"
                        accordion
                      >
                        <el-collapse-item :name="index">
                          <template slot="title">
                            <div style="width: 96%">
                              <div class="elcol-title" style="display: flex">
                                <div
                                  class="elcol-title-left"
                                  :style="{
                                    backgroundColor: getBackgroundColor(index),
                                  }"
                                ></div>
                                <p class="elcol-title-text">
                                  {{ item.wordNo }}
                                </p>
                                <!-- <el-input :title="item.wordNo" class="elcol-input" v-model="item.wordNo" placeholder="请输入内容"
                              style="width: 40%;"></el-input> -->
                                <p class="elcol-title-text2">建议替换</p>
                                <!-- <p class="elcol-title-text">{{ item.target }}</p> -->
                                <el-input
                                  :title="item.wordYes"
                                  class="elcol-input"
                                  v-model="item.wordYes"
                                  placeholder="请输入内容"
                                  style="width: 40%"
                                ></el-input>
                              </div>
                            </div>
                          </template>
                          <div
                            style="
                              height: calc(100vh * 40 / 1080);
                              text-align: left;
                              padding-left: 20px;
                              line-height: calc(100vh * 40 / 1080);
                              border-bottom: 1px solid #e1e7f3;
                            "
                          >
                            {{ item.eq }}
                          </div>
                          <!-- <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div> -->
                          <div style="height: 8px; margin-top: 6px">
                            <span
                              @click="ignore()"
                              style="
                                float: right;
                                margin-right: 10px;
                                color: red;
                                cursor: pointer;
                              "
                              >忽略</span
                            >
                            <span
                              @click="highlightChange(item.source, item.target)"
                              style="
                                float: right;
                                margin-right: 10px;
                                color: #66b1ff;
                                cursor: pointer;
                              "
                              >替换</span
                            >
                          </div>
                        </el-collapse-item>
                      </el-collapse>
                    </div>
                  </div>
                  <div
                    type="primary"
                    class="clbutton2 clbutton1"
                    style="cursor: pointer"
                    @click="save"
                  >
                    关 闭
                  </div>
                </div>
              </div>
              <div class="leftTp">
                <div class="tpBG flexLd">
                  <div class="zxTp" v-if="this.imgListRes.length > 0">
                    <div
                      class="xTpk flex"
                      v-for="(item, index) in imgListRes"
                      :key="index"
                      @click="changeBtn(item)"
                      :class="[
                        imgpd == item.re_imgs_base ? 'activeImg' : 'xTpk',
                      ]"
                    >
                      <div class="tpbj">
                        <img
                          v-if="item.type && item.type == 'img'"
                          :src="'data:image/jpeg;base64,' + item.re_imgs_base"
                          alt=""
                          style="width: 100%"
                        />
                        <div
                          v-if="item.type && item.type == 'pdf'"
                          style="width: 100%; height: 100%; overflow: scroll"
                        >
                          <pdf
                            :src="'data:image/jpeg;base64,' + item.re_imgs_base"
                            alt=""
                            style="width: 100%"
                          ></pdf>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="dxTp flex" v-if="this.imgListRes.length > 0">
                    <div class="tpbj">
                      <img
                        v-if="type && type == 'img'"
                        :src="'data:image/jpeg;base64,' + imgURl"
                        alt=""
                        style="width: 100%"
                      />
                      <div
                        v-if="
                          (type && type == 'pdf') || (type && type == 'ofd')
                        "
                        style="width: 100%; height: 100%; overflow: scroll"
                        ref="pdfContainer"
                      >
                        <pdf
                          v-for="i in numPages"
                          :key="i"
                          :src="filePdfUrl"
                          :page="i"
                          width="100%"
                        >
                        </pdf>
                      </div>
                    </div>
                  </div>
                  <div class="zwTp" v-if="this.imgListRes.length == 0"></div>
                </div>
                <div class="tpUpload flexLd">
                  <div class="upBtn flexLd">
                    <el-upload
                      class="upload-demo"
                      action="#"
                      :http-request="uploadFile"
                      ref="upload"
                      accept=".jpg,.png,.pdf,.docx,.doc,.jpeg,"
                    >
                      <el-button size="small">上传文件</el-button>
                    </el-upload>
                  </div>
                  <div class="zcSize">
                    支持上传jpg、jpeg、png、pdf、docx、doc格式文件，建议文件大小不超过10M
                  </div>
                </div>
                <!-- <div class="tpUpload flexLd">
              <div class="upBtn flexLd">
                <el-upload
                  class="upload-demo"
                  action="#"
                  :http-request="uploadFile"
                  ref="upload"
                  accept=".xlsx,.xlsx,"
                >
                  <el-button size="small">上传表格</el-button>
                </el-upload>
              </div>
              <div class="zcSize">
                支持上传Excel格式文件，可对文件内容进行提取
              </div>
            </div> -->
                <!-- <div class="tpUpload flexLd">
              <div class="upBtn flexLd">
                <el-button size="small" @click="show_check = true"
                  >核稿</el-button
                >
              </div>
              <div class="zcSize">
                支持上传docx格式文件，建议文件大小不超过10M
              </div>
            </div> -->
              </div>
              <div class="rightJson">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;
                  "
                >
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">识别内容</p>
                  </div>
                  <div>
                    <!-- <div
                  style="cursor: pointer"
                  v-if="OldText != ''"
                  class="clbutton3"
                  @click="shibie"
                >
                  识别
                </div> -->
                    <div
                      style="cursor: pointer"
                      class="clbutton3"
                      @click="
                        show_db = true;
                        oldText = removePTags(text);
                      "
                    >
                      内容对比
                    </div>
                    <div
                      style="cursor: pointer"
                      v-if="OldText != ''"
                      class="clbutton3"
                      @click="shibie"
                    >
                      识别
                    </div>
                    <div
                      style="cursor: pointer"
                      v-if="text != ''"
                      class="clbutton3"
                      @click="zhaiyao"
                    >
                      摘要
                    </div>
                    <div
                      style="cursor: pointer"
                      v-if="text != ''"
                      class="clbutton3"
                      @click="down()"
                    >
                      下载
                    </div>
                    <!-- <div
                  style="cursor: pointer"
                  v-if="text != ''"
                  class="clbutton3"
                  @click="down()"
                >
                  导出
                </div> -->
                    <div
                      style="cursor: pointer"
                      v-if="text != ''"
                      class="clbutton3"
                      @click="copyresult()"
                    >
                      复制
                    </div>
                  </div>
                </div>
                <vue-editor
                  class="fir-textarea fir-textarea-max"
                  type="textarea"
                  placeholder=""
                  v-model="text"
                  style="margin-bottom: 20px"
                  ref="editor"
                  @contextmenu.native="showContextMenu"
                >
                </vue-editor>
                <div
                  v-if="contextMenuVisible"
                  class="context-menu"
                  :style="contextMenuStyle"
                >
                  <ul>
                    <li @click="sendToBackend4()">
                      <i class="el-icon-magic-stick"></i>
                      润色
                    </li>
                  </ul>
                </div>
                <!-- <div style="clear: both"></div>
            <div
              style="
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
              "
            >
              <div class="fir-title">
                <div class="fir-kuai"></div>
                <p class="fir-title-p">章节速览</p>
              </div>
              <div
                style="cursor: pointer"
                v-if="text != ''"
                class="clbutton3"
                @click="zhaiyao"
              >
                摘要
              </div>
            </div> -->
                <!-- <vue-editor
              class="fir-textarea fir-textarea-max"
              type="textarea"
              placeholder=""
              v-model="text1"
            ></vue-editor> -->
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="翻译" name="fanyi">
            <div class="input-container">
              <div class="input-group">
                <div class="fir-title">
                  <div class="fir-kuai"></div>
                  <p class="fir-title-p">翻译内容</p>
                </div>
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                  "
                >
                  <el-select
                    v-model="targetLanguage"
                    placeholder="请选择目标语言"
                  >
                    <el-option
                      v-for="item in languageOptionsWithLabels"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
                <div>
                  <el-input
                    type="textarea"
                    :rows="30"
                    placeholder="请输入您想翻译的内容"
                    v-model="transtext"
                  ></el-input>

                  <el-button @click="translatearticle()" type="primary" style="margin-left: 850px;">
                    翻译
                  </el-button>
                </div>
              </div>

              <div class="input-group">
                <div class="fir-title">
                  <div class="fir-kuai"></div>
                  <p class="fir-title-p">翻译结果</p>
                </div>
               
                <div style="margin-top: 40px">
                  <el-input
                    type="textarea"
                    :rows="30"
                    v-model="retext"
                  ></el-input>

                  <el-button @click="getSummary1()" type="primary" style="margin-left: 850px;">总结</el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
    <el-dialog title="文章摘要" :visible.sync="zyVisible" width="50%">
      <vue-editor
        class="fir-textarea fir-textarea-max"
        type="textarea"
        placeholder=""
        v-model="text1"
      ></vue-editor>
      <span slot="footer" class="dialog-footer">
        <el-button
          style="
            font-size: calc(100vw * 10 / 1920);
            line-height: 10px;
            padding: 10px 20px;
            background-color: #2975e6;
            color: white;
            margin-top: 35px !important;
          "
          @click="zyVisible = false"
          >关闭</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="50%"
      class="custom-dialog"
    >
      <span
        style="
          font-size: calc(100vw * 18 / 1920);
          line-height: 20px;
          margin-top: 300px;
        "
        >校验已完成，是否下载校验结果？</span
      >
      <span
        slot="footer"
        class="dialog-footer"
        style="padding: 35px 379px 20px"
      >
        <el-button
          style="
            font-size: calc(100vw * 10 / 1920);
            line-height: 10px;
            padding: 10px 20px;
            background-color: #2975e6;
            color: white;
            margin-top: 35px !important;
          "
          @click="download_word()"
          >确认</el-button
        >
        <el-button
          style="
            font-size: calc(100vw * 10 / 1920);
            line-height: 10px;
            padding: 10px 20px;
            margin-top: 35px;
            background-color: #ffffff;
            border: 1px solid #cccccc;
            color: #cccccc !important;
          "
          @click="dialogVisible = false"
          >取 消</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title=""
      :visible.sync="show_check"
      width="50%"
      class="custom-dialog"
    >
      <!-- <span style="font-size: calc(100vw *  18 / 1920); line-height: 20px; margin-top: 300px"
        >校验已完成，是否下载校验结果？</span
      > -->
      <span
        slot="footer"
        class="dialog-footer"
        style="padding: 35px 379px 20px"
      >
        <!-- <div> -->
        <!-- <el-upload> -->
        <el-button
          style="
            font-size: calc(100vw * 10 / 1920);
            line-height: 10px;
            padding: 10px 20px;
            background-color: #2975e6;
            color: white;
            margin-top: 35px !important;
          "
          @click="download_word()"
          >核稿</el-button
        >
        <!-- </el-upload> -->
        <!-- </div> -->
        <el-button
          style="
            font-size: calc(100vw * 10 / 1920);
            line-height: 10px;
            padding: 10px 20px;
            margin-top: 35px;
            background-color: #ffffff;
            border: 1px solid #cccccc;
            color: #cccccc !important;
          "
          @click="show_check = false"
          >取 消</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      v-loading="loading"
      element-loading-text="请稍候..."
      element-loading-background="rgba(255, 255, 255, 0.7)"
      @close="
        newText = '';
        oldText = '';
        show_db = false;
      "
      title=""
      :visible.sync="show_db"
      width="50%;max-height: 500px;"
      class="com-dialog"
    >
      <div style="max-height: 500px; overflow: scroll" class="com-dialog">
        <div class="diff-container">
          <h1 class="title">文本对比工具</h1>
          <!-- <p class="description">输入或上传两段文本，点击"对比"按钮查看差异</p> -->

          <div class="input-container">
            <div class="input-group">
              <div class="input-header">
                <h3>原始文本</h3>
                <el-upload
                  :show-file-list="false"
                  class="upload-demo"
                  action="#"
                  :http-request="uploadFile1"
                  ref="upload"
                  accept=".png,.pdf,.docx,.doc"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">
                    只能上传doc、docx、pdf、png文件
                  </div>
                </el-upload>
              </div>
              <el-input
                type="textarea"
                :rows="10"
                placeholder="请输入原始文本"
                v-model="oldText"
              ></el-input>
            </div>

            <div class="input-group">
              <!-- <h3>对比文本</h3>
              <el-upload
                :show-file-list="false"
                class="upload-demo"
                action="#"
                :http-request="uploadFile2"
                ref="upload"
                accept=".jpg,.png,.pdf,.docx,.doc,.jpeg,"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">
                  只能上传doc、docx、pdf、png、jpg、jpeg文件
                </div>
              </el-upload> -->
              <div class="input-header">
                <h3>对比文本</h3>
                <el-upload
                  :show-file-list="false"
                  class="upload-demo"
                  action="#"
                  :http-request="uploadFile2"
                  ref="upload2"
                  accept=".png,.pdf,.docx,.doc,"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">
                    只能上传doc、docx、pdf、png文件
                  </div>
                </el-upload>
              </div>
              <el-input
                type="textarea"
                :rows="10"
                placeholder="请输入要进行对比的文本"
                v-model="newText"
              ></el-input>
            </div>
          </div>

          <div class="actions">
            <el-button type="primary" @click="compareDiff">对比</el-button>
            <el-button @click="clearAll">清空</el-button>
            <!-- <el-button @click="loadSampleData">加载示例</el-button> -->
          </div>

          <div v-if="showDiff" class="diff-result">
            <h2>对比结果</h2>
            <div class="diff-options">
              <!-- <el-radio-group v-model="diffType" @change="compareDiff">
            <el-radio-button label="split">分栏对比</el-radio-button>
            <el-radio-button label="unified">统一视图</el-radio-button>
          </el-radio-group>
          
          <el-switch
            v-model="highlightLines"
            active-text="高亮整行"
            inactive-text="高亮文字"
            @change="compareDiff"
          ></el-switch> -->
            </div>

            <div class="diff-view">
              <code-diff
                :old-string="oldText"
                :new-string="newText"
                :context="10"
                :output-format="diffType"
                :highlight-lines="highlightLines"
              ></code-diff>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="
            show_db = false;
            oldText = '';
            newText = '';
          "
          style="
            font-size: calc(100vw * 10 / 1920);
            line-height: 10px;
            padding: 10px 20px;
            background-color: #2975e6;
            color: white;
            margin-top: 35px !important;
          "
          >关闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import HeadNavigation from "../components/HeadNavigation.vue"; // 引入侧边导航组件
import CodeDiff from "vue-code-diff";
import pdf from "vue-pdf";
import { VueEditor } from "vue2-editor";
import Quill from "quill";
let BlockEmbed = Quill.import("blots/block/embed");
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute(
      "style",
      "padding: 20px; border: 1px solid #488aff; color:red; position: relative;"
    );
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement("button");
    insertButton.innerHTML = "应用";
    insertButton.className = "btn-insert";
    insertButton.style.position = "absolute";
    // 按钮颜色
    insertButton.style.backgroundColor = "#488aff";
    // 字体为白色
    insertButton.style.color = "white";
    // insertButton.style.top = '10px';
    insertButton.style.right = "70px";
    insertButton.addEventListener("click", () => {
      if (value.flag == 1) {
        value.editorClass.insertText(
          value.Position + value.selectedTextlength,
          value.newText
        );
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement("button");
    cancelButton1.innerHTML = "取消";
    cancelButton1.className = "btn-cancel";
    cancelButton1.style.position = "absolute";
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = "#e64721";
    // 字体为白色
    cancelButton1.style.color = "white";
    cancelButton1.style.right = "10px";
    cancelButton1.addEventListener("click", () => {
      node.remove();
    });

    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = "customBlock";
CustomBlock.tagName = "div";

Quill.register({
  "formats/customBlock": CustomBlock,
});
import {
  rewrite,
  getOcr,
  testcheck,
  getOcrtxt,
  summary_string,
  download_word,
  download_identify,
  upfile,
  getTranslateType,
  translate_article,
} from "../api/home.js"; // 接口请求
import { mapState, mapActions } from "vuex";
import store from "../store/index";
// import mammoth from 'mammoth'
export default {
  data() {
    return {
      transtext: "",
      retext: "",
      targetLanguage: "",
      languageOptionsWithLabels: [],
      languageOptions: [],
      activeTab: "shibie",
      // 原始文本
      oldText: "",
      // 修改后文本
      newText: "",
      // 是否显示对比结果
      showDiff: false,
      // 对比类型：split(分栏)或unified(统一视图)
      diffType: "split",
      // 是否高亮整行
      highlightLines: true,
      show_db: false,
      zyVisible: false,
      show_check: false,
      loading1: false,
      selectedText: "",
      newText3: "",
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      res1: [],
      dialogVisible: false,
      paramsxz: {},
      text1: "",
      OldText: "",
      con_loading: false,
      jiaoyanjc: false,
      jydis: false,
      activeNames: [],
      correctionList: [],
      navShow: true,
      fileList: [],
      file: "",
      filename: "",
      input: "",
      imgURl: "",
      imgURlss: "",
      imgURlcw: "",
      loading: false,
      text: "",
      imgListRes: [],
      imgpd: "",
      type: "",
      filePdfUrl: "",
      filePdfUrlcw: "",
      reverse: true,
      numPages: 0, // 初始化页数
      type: "",
      imgURlcw: "",
      filePdfUrl: "",
      numPages: 0,
      wordHtmlContent: "",
      txtContent: "",
      activities: [
        {
          content: "活动按期开始",
          timestamp: "2018-04-15",
        },
        {
          content: "通过审核",
          timestamp: "2018-04-13",
        },
        {
          content: "通过审核",
          timestamp: "2018-04-13",
        },
        {
          content: "创建成功",
          timestamp: "2018-04-11",
        },
      ],
    };
  },
  components: {
    VueEditor,
    pdf,
    HeadNavigation,
    CodeDiff,
  },
  mounted() {
    // window.addEventListener('message', this.handleIframeMessage);
    this.imgListRes = JSON.parse(JSON.stringify(this.imgList));
    // 使用数组的length属性减1来获取最后一个元素的索引
    let lastIndex = this.imgListRes.length - 1;
    // 通过索引获取最后一条数据
    let lastItem = this.imgListRes[lastIndex];
    this.changeBtn(lastItem);
    this.getTranslate();
  },
  // beforeDestroy() {
  //   // 移除监听
  //   window.removeEventListener('message', this.handleIframeMessage);
  // },
  computed: {
    ...mapState(["id"]), // 映射 username 和 id

    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 500}px`,
      };
    },
    ...mapState(["imgList"]),
    username() {
      return this.$store.state.username;
    },
  },
  methods: {
    async translatearticle() {
      this.loading = true;
      this.mask = true;
      let dataArray = {
        text: this.transtext,
        translateType: this.targetLanguage,
        userId: this.id,
      };
      let res = await translate_article(dataArray);
      // let resdata = await res.json();
      console.log(res.data);
      if (res.data.status_code == 200) {
        this.loading = false;
        this.mask = false;
        this.$message({
          message: "翻译成功",
          type: "success",
        });
        this.retext = res.data.data;
      } else {
        this.loading = false;
        this.mask = false;
      }
    },
    convertLanguageOptions() {
      this.languageOptionsWithLabels = this.languageOptions.map((option) => {
        return {
          value: option,
          label: option,
        };
      });
    },
    async getTranslate() {
      let resdata = await getTranslateType();
      // let res = await resdata.json();
      console.log(resdata.data.data);
      this.languageOptions = resdata.data.data;
      this.convertLanguageOptions();
    },

    removePTags(text) {
      // 使用正则表达式过滤掉 <p> 和 </p> 标签
      return text.replace(/<p>|<\/p>/g, "");
    },
    /**
     * 执行文本对比
     */
    compareDiff() {
      // 如果两个文本框都有内容，则显示对比结果
      if (this.oldText.trim() && this.newText.trim()) {
        this.showDiff = true;
      } else {
        this.$message.warning("请在两个文本框中都输入内容");
      }
    },

    /**
     * 清空所有输入和结果
     */
    clearAll() {
      this.oldText = "";
      this.newText = "";
      this.showDiff = false;
    },

    /**
     * 加载示例数据
     */
    loadSampleData() {
      this.oldText = `function greeting(name) {
    console.log('Hello, ' + name + '!');
    return 'Hello, ' + name + '!';
  }
  
  // 调用函数
  greeting('World');
  
  // 这是一个注释
  const user = {
    name: 'John',
    age: 30,
    isActive: true
  };
  
  console.log(user);`;

      this.newText = `function greeting(name) {
    // 添加了一个新的注释
    const message = 'Hello, ' + name + '!';
    console.log(message);
    return message;
  }
  
  // 调用函数
  greeting('Vue');
  
  // 这是一个修改后的注释
  const user = {
    name: 'Jane',
    age: 25,
    isActive: true,
    role: 'admin'
  };
  
  console.log(user);`;

      this.showDiff = true;
    },
    onIframeLoad() {
      console.log("接收页面(iframe)加载完成");
      // 可以在这里发送初始化消息
    },
    copyresult() {
      let textToCopy = this.text;
      // 去掉 <p> 标签
      textToCopy = textToCopy.replace(/<p>|<\/p>/g, "");

      // 检查 navigator.clipboard 是否可用
      // if (window.navigator.clipboard) {
      //   window.navigator.clipboard
      //     .writeText(textToCopy)
      //     .then(() => {
      //       console.log("文本已复制到剪贴板");
      //       this.$message({
      //         message: "文本已复制到剪贴板",
      //         type: "success",
      //       });
      //     })
      //     .catch((err) => {
      //       console.error("无法复制文本: ", err);
      //       this.$message({
      //         message: "无法复制文本",
      //         type: "error",
      //       });
      //     });
      // } else {
      //   console.error("当前浏览器不支持 navigator.clipboard API");
      //   this.$message({
      //     message: "当前浏览器不支持复制到剪贴板的功能",
      //     type: "warning",
      //   });
      // }
      this.$copyText(textToCopy)
        .then(() => {
          this.$message.success("文本已复制");
        })
        .catch(() => {
          this.$message.warning("复制失败");
        });
    },

    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    async down() {
      let params = {
        identify_content: this.text,
      };
      let data = await download_identify(params);
      // 假设data是Blob对象
      const url = URL.createObjectURL(data); // 创建Blob URL
      // 创建一个a标签
      const a = document.createElement("a");
      a.href = url;
      a.download = "识别结果.docx"; // 设置下载文件名，这里可以根据需要修改
      // 触发点击事件
      document.body.appendChild(a); // 将a标签添加到文档中
      a.click(); // 点击a标签
      document.body.removeChild(a); // 点击后移除a标签
      URL.revokeObjectURL(url); // 释放创建的URL
    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener("click", this.hideContextMenu);
    },
    showContextMenu(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener("click", this.hideContextMenu);
      }
    },
    // 4.润色接口
    async sendToBackend4() {
      this.loading = true;
      this.mask = true;
      let params4 = {
        text: this.selectedText,
        flag: "4",
      };
      let res = await rewrite(params4);
      this.newText3 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading = false;
        this.mask = false;
        this.$message({
          message: "润色成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText3,
              newText: this.newText3,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 4,
            },
            Quill.sources.USER
          );
          this.$nextTick(() => {
            // 确保 DOM 更新完成后再进行操作
            this.highlightText(this.newText2, "red");
          });
        }
        //  else {
        //   // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
        //   editor.insertText(editor.getLength(), this.newText3);

        //   // 将新插入的文本格式化为红色
        //   console.log('Formatting text at position:', editor.getLength() - this.newText3.length);
        //   console.log('Formatting text length:', this.newText3.length);
        //   editor.formatText(editor.getLength() - this.newText3.length, this.newText3.length,);

        //   editor.setSelection(editor.getLength(), 0);
        // }
      } else {
        this.loading = false;
        this.mask = true;
        this.$message({
          message: "润色失败",
          type: "error",
        });
      }
    },
    toxg() {
      // alert(1)
      this.$router.push("/grzx4");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    wdsc() {
      // alert(1)
      this.$router.push("/wdsc");
    },
    togrzx() {
      // alert(1)
      this.$router.push("/grzx1");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    async getSummary() {
      this.con_loading = true;
      let params = {
        text: this.text,
        user_id: this.id,
      };
      let data = await summary_string(params);
      this.text1 = data.data.data;
      let that = this;
      this.zyVisible = true;
      if (data.data.status_code == 200) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "success",
        });
      } else if (data.data.status_code == 500) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "error",
        });
      } else if (data.data.status_code == 10001) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "error",
        });
      } else if (data.data.status_code == 10002) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "error",
        });
      }
    },
    async getSummary1() {
      this.con_loading = true;
      let params = {
        text: this.retext,
        user_id: this.id,
      };
      let data = await summary_string(params);
      this.text1 = data.data.data;
      let that = this;
      this.zyVisible = true;
      if (data.data.status_code == 200) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "success",
        });
      } else if (data.data.status_code == 500) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "error",
        });
      } else if (data.data.status_code == 10001) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "error",
        });
      } else if (data.data.status_code == 10002) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "error",
        });
      }
    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    highlightError(error, word) {
      this.text = this.removetext(this.text);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.text = this.text.replace(error, a);
      console.log(this.text);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    // 高亮方法
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(
        this.$refs.editor.$el.querySelector("div.ql-editor"),
        "editorIframe"
      );
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    ignore() {
      this.text = this.removetext(this.text);
      this.activeNames = [];
    },
    highlightChange(text1, text2) {
      console.log(this.text);
      let changeData = text2;
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.text = this.text.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.text = this.removetext(this.text);
      this.text = this.text.replace(text1, changeData);
    },
    save() {
      // this.text = this.removetext(this.text)
      // this.activeNames = []
      this.jiaoyanjc = false;
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    // shibie() {
    //   this.text = this.oldText
    // },
    shibie() {
      if (this.text == "") {
        this.text = this.OldText;

        this.$message({
          message: "识别成功",
          type: "success",
        });
      } else {
        this.$message({
          message: "识别已完成，请勿重复识别",
          type: "warning",
        });
      }
    },
    zhaiyao() {
      this.getSummary();
    },
    async jiaoyan() {
      // console.log(this.type, "type");
      // console.log(this.imgURl, "imgURl");
      this.con_loading = true;

      this.text = this.removetext(this.text);
      testcheck({
        test_all: this.text,
      }).then(async (data) => {
        this.correctionList = data.data.data;

        if (this.correctionList.length == 0) {
          this.jiaoyanjc = false;
          this.loading = false;
          this.con_loading = false;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          if (this.type == "img") {
            let fd = {
              imgs_name: "1.png",
              base_string: this.imgURlss,
            };
            getOcrtxt(fd).then(async (data) => {
              console.log(data, "362");
              this.imgURlcw = data.data;
              this.con_loading = false;
            });
          } else {
            this.con_loading = false;
          }
          console.log(data, "619");
          this.jiaoyanjc = true;
        }
      });
    },
    ...mapActions(["addItemAction"]),
    ...mapActions(["addSczyAction"]),
    changeBtn(val) {
      this.text = "";
      this.text1 = "";

      // console.log(val.imgs_base);
      if (val) {
        this.imgpd = "data:image/jpeg;base64," + val.re_imgs_base;
        // this.imgURl = val.imgs_base;
        this.imgURl = val.re_imgs_base;
        this.imgURlss = val.base_string;
        if (val.type == "pdf" || val.type == "ofd" || val.type == "txt") {
          this.filePdfUrl = "data:application/pdf;base64," + val.re_imgs_base;
          const loadingTask = pdf.createLoadingTask(this.filePdfUrl);
          loadingTask.promise
            .then((pdf) => {
              this.numPages = pdf.numPages;
            })
            .catch((err) => {
              console.error("Error while loading the PDF", err);
            });
          this.$nextTick(() => {
            this.$refs.pdfContainer.scrollTop = 0;
          });
        }
        this.type = val.type;
        this.OldText = val.txt;
        // this.zyVisible=true;
        // this.text = val.txt;
        // this.getSummary();
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickTopAi() {
      this.$router.push("/index");
    },
    clickTopLg() {
      this.$router.push("/lgsc");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    clickTopsfw() {
      this.$router.push("/sfw");
    },
    // beforeAvatarUpload(file) {
    //   const isJPG = file.type === "image/jpeg";
    //   const isPNG = file.type === "image/png";
    //   const isPDF = file.type === "application/pdf";
    //   const isDOCX =
    //     file.type ===
    //     "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    //   const isDOC = file.type === "application/msword";

    //   if (!isJPG && !isPNG && !isPDF && !isDOCX && !isDOC) {
    //     this.$message.error("上传文件只能是 JPG/PNG/PDF/DOCX/DOC 格式!");
    //   }
    //   return isJPG || isPNG || isPDF || isDOCX || isDOC;
    // },

    uploadFile(item) {
      let file;
      this.filename = item.file.name;
      this.blobToBase64(item.file, (dataurl) => {
        file = dataurl.split(",")[1];
        this.file = JSON.parse(JSON.stringify(file));
        // if (
        //   item.file.type ===
        //     "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
        //   item.file.type === "application/msword"
        // ) {
        // this.uploadMp1(this.file);
        // } else {
        this.uploadMp(this.file);
        //   }
      });
    },
    uploadFile1(item) {
      this.up1(item.file);
    },

    uploadFile2(item) {
      this.up2(item.file);
    },
    async up1(file) {
      this.loading = true;
      let fd = new FormData();
      fd.append("pdp_file", file);
      let resData = await upfile(fd);
      // let res = await resData.json();
      if (resData.data.status_code == 200) {
        this.oldText = resData.data.data;
        this.$message({
          message: "识别成功",
          type: "success",
        });
        this.loading = false;
      }
    },
    async up2(file) {
      this.loading = true;

      let fd = new FormData();
      fd.append("pdp_file", file);
      let resData = await upfile(fd);
      // let res = await resData.json();
      if (resData.data.status_code == 200) {
        this.newText = resData.data.data;
        this.$message({
          message: "识别成功",
          type: "success",
        });
        this.loading = false;
      }
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    async uploadMp(val) {
      console.log(val);
      console.log(this.filename, "文件名");
      let that = this;
      this.loading = true;
      let fd = new FormData();
      fd.append("imgs_name", this.filename);
      fd.append("base_string", val);
      let resData = await getOcr(fd);
      this.$refs.upload.clearFiles();
      if (resData.data.type == "1") {
        // console.log(resData.data.data, "resData.data.dataresData.data.data");
        that.addItemAction(resData.data.data);
        this.imgListRes = JSON.parse(JSON.stringify(this.imgList));
        // 使用数组的length属性减1来获取最后一个元素的索引
        let lastIndex = this.imgListRes.length - 1;
        // 通过索引获取最后一条数据
        let lastItem = this.imgListRes[lastIndex];
        this.changeBtn(lastItem);

        // that.$store.commit("addItem", resData.data.data);
        this.$message({
          showClose: true,
          message: resData.data.message,
          type: "success",
        });
        this.loading = false;
        // this.imgURl = resData.data.data.re_imgs_base;
        // this.text = resData.data.data.txt;
        this.jydis = true;
        // let zpxx = this.zpzm(resData.data.imgs_base);
        // this.imgURl = zpxx;
      } else if (resData.data.status_code == 400) {
        this.$message({
          showClose: true,
          message: resData.data.message,
          type: "error",
        });
      } else if (resData.data.status_code == 500) {
        // this.loading = false;

        this.$message({
          showClose: true,

          message: resData.data.message,
          type: "error",
        });
      }
      if (resData.data.type == "2") {
        if (resData.data.status_code == 200) {
          this.$message({
            showClose: true,
            message: resData.data.message,
            type: "success",
          });
          this.res1 = resData;
          this.loading = false;
          this.dialogVisible = true;
        } else if (resData.data.status_code == 9999) {
          this.loading = false;
          this.$message({
            showClose: true,
            message: resData.data.message,
            type: "error",
          });
          this.loading = false;
        } else if (resData.data.status_code == 500) {
          // this.loading = false;
          this.$message({
            showClose: true,
            message: resData.data.message,
            type: "error",
          });
          this.loading = false;
        }
      }
    },
    download_word() {
      this.paramsxz = {
        // resData.data.
        base_string1: this.res1.data.data.re_imgs_base.replace('"', ""),
        imgs_name1: this.res1.data.data.name.replace('"', ""),
        // base_string:resData.data.data.re_imgs_base,
      };
      download_word(this.paramsxz).then((blob) => {
        // 创建一个 URL 对象
        const url = window.URL.createObjectURL(blob);
        // 创建一个 <a> 元素
        const a = document.createElement("a");
        // 设置下载属性
        a.href = url;
        a.download = this.paramsxz.imgs_name1 || "download.docx"; // 设置默认文件名
        // 将 <a> 元素添加到文档中
        document.body.appendChild(a);
        // 触发点击事件
        a.click();
        // 移除 <a> 元素
        document.body.removeChild(a);
        // 释放 URL 对象
        window.URL.revokeObjectURL(url);
        this.dialogVisible = false;
      });
    },
    // async uploadMp1(val) {
    //   console.log(val);
    //   console.log(this.filename);
    //   let that = this;
    //   this.loading = true;
    //   let fd = new FormData();
    //   fd.append("imgs_name", this.filename);
    //   fd.append("base_string", val);
    //   let resData = await check_word(fd);
    //   this.$refs.upload.clearFiles();
    //   if (resData.data.status_code == 200) {
    //     this.$message({
    //       showClose: true,
    //       message: resData.data.message,
    //       type: "success",
    //     });
    //     this.res1 = resData;
    //     this.loading = false;
    //     this.dialogVisible = true;
    //   } else if (resData.data.status_code == 9999) {
    //     this.loading = false;
    //     this.$message({
    //       showClose: true,
    //       message: resData.data.message,
    //       type: "error",
    //     });
    //     this.loading = false;
    //   } else if (resData.data.status_code == 500) {
    //     // this.loading = false;
    //     this.$message({
    //       showClose: true,
    //       message: resData.data.message,
    //       type: "error",
    //     });
    //     this.loading = false;
    //   }
    // },
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    zpzm(zp) {
      const iamgeBase64 = "data:image/jpeg;base64," + zp;
      let zpxx;
      if (typeof iamgeBase64 === "string") {
        // 复制某条消息
        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64)) {
          // debugger;
          // let that = this;

          function previwImg(item) {
            zpxx = item;
          }
          previwImg(iamgeBase64);
        }
      }
      return zpxx;
    },
  },
};
</script>
<style lang="less" scoped>
.context-menu {
  margin-left: calc(-100vw * 350 / 1920);
  position: absolute;
  background: #fff;
  box-shadow: 0 calc(100vh * 2 / 1080) calc(100vh * 10 / 1080)
    rgba(0, 0, 0, 0.1);
  z-index: 1080;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.context-menu li {
  padding: calc(100vh * 8 / 1080) calc(100vw * 16 / 1920);
  cursor: pointer;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}
.upload-demo {
  // width: 100%;
  // height: 100%;
  font-size: calc(100vw * 16 / 1920);
}
::v-deep .custom-dialog .el-dialog__body {
  padding-top: calc(100vh * 120 / 1080); /* 调整这个值以达到你想要的移动距离 */
}

::v-deep .custom-dialog .el-dialog {
  height: calc(100vh * 400 / 1080);
  background: url("../assets/tc.png") no-repeat center !important;
  background-size: cover !important; /* 确保背景图片覆盖整个对话框 */
}

::v-deep .custom-dialog .dialog-footer {
  padding-top: calc(100vh * 330 / 1080); /* 调整这个值以达到你想要的移动距离 */
}

.el-button span {
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 16 / 1920);
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  width: calc(100vw * 300 / 1920);
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: linear-gradient(76deg, #07389c 0%, #3d86d1 0%, #3448b3 100%);

  img {
    width: calc(100vw * 120 / 1920);
    height: calc(100vh * 48 / 1080);
    position: absolute;
  }

  p {
    border: calc(100vw * 1.54 / 1920) solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: calc(100vw * 5 / 1920);
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px calc(100vh * 2 / 1080) calc(100vh * 4 / 1080) 0px
    rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vw * 40 / 1920);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: calc(100vw * 1 / 1920) solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vw * 130 / 1920);
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vw * 16 / 1920);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vw * 14 / 1920);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0px
                rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0px
              rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: calc(100vw * 93 / 1920);
        height: calc(100vh * 80 / 1080);

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 16 / 1920);
            height: calc(100vh * 16 / 1080);
          }
        }

        .btn {
          margin-left: calc(100vw * 30 / 1920);
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 28 / 1920);
            height: calc(100vh * 28 / 1080);
            float: left;
          }

          p {
            float: left;
            margin-left: calc(100vw * 22 / 1920);
            margin-top: calc(100vh * -2 / 1080);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: calc(100vh * 20 / 1080);
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw * 14 / 1920);
  letter-spacing: 0;
  line-height: calc(100vh * 16 / 1080);
  overflow: hidden;
  height: calc(100vh * 20 / 1080);

  .fir-kuai {
    width: calc(100vw * 6 / 1920);
    height: calc(100vh * 16 / 1080);
    margin-right: calc(100vw * 8 / 1920);
    float: left;
    background: #4081ff;
    border-radius: calc(100vw * 1.5 / 1920);
  }

  .fir-title-p {
    color: #222;
    font-weight: 500;
    font-size: calc(100vw * 14 / 1920);
    letter-spacing: 0;
    line-height: calc(100vh * 16 / 1080);
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}

.xxjs {
  width: calc(100vw * 902 / 1920);
  height: calc(100vh * 40 / 1080);
  word-wrap: break-word;
  text-align: left;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #999999;
  letter-spacing: 0;
  margin-top: calc(100vh * 12 / 1080);
}

.flexLd {
  display: flex;
  justify-content: space-between;
  height: auto;
}

.btnLeft {
  width: calc(100vw * 332 / 1920);
  height: auto;

  .el-button {
    width: calc(100vw * 104 / 1920);
    height: calc(100vh * 30 / 1080);
    line-height: calc(100vh * 15 / 1080);
    padding: calc(100vh * 5 / 1080);
    font-size: calc(100vw * 14 / 1920);
    margin-left: 0;
    margin-right: calc(100vw * 9 / 1920);
    margin-top: calc(100vh * 10 / 1080);
    background: #f5f8fc;
    border: calc(100vw * 1 / 1920) solid rgba(31, 82, 176, 1);
    border-radius: calc(100vw * 4 / 1920);
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw * 14 / 1920);
    color: #1f52b0;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;

    span {
      font-family: SourceHanSansSC-Medium;
      font-size: calc(100vw * 16 / 1920);
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }
  }
}

::v-deep(.el-button) {
  span {
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw * 16 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
}

.con {
  width: 100%;
  height: auto;
  margin-top: calc(100vh * 20 / 1080);
  background: #ffffff;
  padding: calc(100vh * 20 / 1080);

  .leftTp {
    width: calc(100vw * 1030 / 1920);
    height: auto;

    .tpBG {
      width: 100%;
      height: calc(100vh * 735 / 1080);
      margin-bottom: calc(100vh * 20 / 1080);

      .zxTp {
        width: calc(100vw * 162 / 1920);
        height: 100%;
        overflow-y: scroll;

        .xTpk {
          width: 100%;
          height: calc(100vh * 136 / 1080);
          background: #dcdfe5;
          margin-bottom: calc(100vh * 8 / 1080);

          &:hover {
            border: calc(100vw * 1.35 / 1920) solid rgba(26, 102, 255, 1);
          }

          .tpbj {
            width: 100%;
            height: calc(100vh * 110 / 1080);
            background-color: #ffffff;

            img {
              width: calc(100vw * 140 / 1920);
              height: calc(100vh * 110 / 1080);
              object-fit: contain;
            }
          }

          &:active {
            border: calc(100vw * 1.35 / 1920) solid rgba(26, 102, 255, 1);
            width: 100%;
            height: calc(100vh * 136 / 1080);
            background: #dcdfe5;
            margin-bottom: calc(100vh * 8 / 1080);

            .tpbj {
              width: calc(100vw * 140 / 1920);
              height: calc(100vh * 110 / 1080);
              background-color: #ffffff;

              img {
                width: 140px;
                height: 110px;
              }
            }
          }
        }

        .activeImg {
          border: 1.35px solid rgba(26, 102, 255, 1);
          width: 100%;
          height: 136px;
          background: #dcdfe5;
          margin-bottom: 8px;

          .tpbj {
            width: 140px;
            height: 110px;
            background-color: #ffffff;

            img {
              width: 140px;
              height: 110px;
            }
          }
        }
      }

      .dxTp {
        width: 848px;
        height: 100%;
        background: #dcdfe5;

        .tpbj {
          width: 100%;
          height: 100%;
          background-color: #ffffff;

          img {
            // width: 350px;
            height: 450px;
            object-fit: contain;
          }
        }
      }

      .zwTp {
        background: url(../img/bga.png) no-repeat center;
        // background: url(../img/LOGO.jpg) no-repeat center;

        width: 88%;
        height: 100%;
        background-size: 50%;
      }
    }

    .tpUpload {
      width: 70%;
      height: 82px;
      padding-left: 18px;
      align-items: center;

      .upBtn {
        // width: 100%;
        height: calc(100vh * 40 / 1080);
        font-size: calc(100vh * 40 / 1920);

        // margin-left: 120px;
        ::v-deep(.el-button) {
          background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
          // box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 4px;
          width: calc(100vw * 160 / 1920);
          height: calc(100vh * 40 / 1080);
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw * 16 / 1920);
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }

        .sizeColor {
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
          margin: 10px;
        }

        ::v-deep(.el-input__inner) {
          background: #ffffff;
          border: 1px solid rgba(186, 203, 228, 1);
          border-radius: 4px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
        }
      }

      .upBtn1 {
        // width: 100%;
        height: calc(100vh * 40 / 1080);
        margin-left: 60px;

        ::v-deep(.el-button) {
          background: #1a66ff;
          border: 1px solid rgba(31, 82, 176, 1);
          border-radius: 4px;
          width: calc(100vw * 160 / 1920);
          height: calc(100vh * 40 / 1080);
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw * 16 / 1920);
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }

        .sizeColor {
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
          margin: 10px;
        }

        ::v-deep(.el-input__inner) {
          background: #ffffff;
          border: 1px solid rgba(186, 203, 228, 1);
          border-radius: 4px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
        }
      }

      .zcSize {
        font-family: PingFangSC-Semibold;
        font-size: calc(100vw * 14 / 1920);
        color: #999999;
        letter-spacing: 0;
        height: auto;
        text-align: left;
        margin-left: calc(100vw * 10 / 1920);
        width: calc(100vw * 600 / 1920);
      }

      .zcSize1 {
        font-family: PingFangSC-Semibold;
        font-size: calc(100vw * 14 / 1920);
        color: #999999;
        letter-spacing: 0;
        // font-weight: 600;
        height: auto;
        text-align: left;
        margin-left: 10px;
        width: 550px;
        vertical-align: middle;
      }
    }
  }

  .rightJson {
    width: calc(100vw * 735 / 1080);
    height: calc(100vh * 837 / 1080);
    background: #f2f4f9;
    padding: 20px;
    position: relative;
    // .fir-textarea {
    //   height: calc(100% - 85px);

    //   ::v-deep(.el-textarea__inner) {
    //     height: 100%;
    //   }
    // }
    .fir-textarea {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #f8f9fd;
      border: 1px solid rgba(229, 232, 245, 1);
      border-radius: 4px;
      margin-top: 20px;
      margin-bottom: 0;
      height: 158px;

      ::v-deep(.el-textarea__inner) {
        font-size: 14px !important;
        background-color: #f8f9fd !important;
        height: 100% !important;
        font-family: PingFangSC-Regular;
        padding: 13px 18px 33px 16px;
      }
    }

    .fir-textarea-max {
      height: 92% !important;
    }

    .jsTitle {
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 16 / 1920);
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
      height: auto;
      text-align: left;
      margin-bottom: 5px;
    }

    .jsbor {
      border: 1px solid rgba(150, 171, 214, 1);
      height: auto;
      margin-bottom: 14px;
    }

    .jsConNr {
      width: 100%;
      height: calc(100% - 84px);
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 16 / 1920);
      color: #333333;
      letter-spacing: 0;
      line-height: 35px;
      font-weight: 400;
      overflow-y: scroll;
      text-align: left;
    }
  }
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: 10px;
  height: 10px;
}

::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
  display: none !important;
}

::v-deep(.ql-blank) {
  display: none !important;
}

::v-deep(.ql-container.ql-snow) {
  border: 0;
}

.user-menu-content {
  width: calc(100vw * 246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;
    // font-size: calc(100vw *  15 / 1920);

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw * 15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw * 12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.clbutton1 {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}
.clbutton3 {
  font-size: calc(100vw * 16 / 1920);
  height: calc(100vh * 30 / 1080);
  width: calc(80vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  float: right;
  margin: calc(100vh * 10 / 1080) calc(100vw * 10 / 1920) 0 0;
}

.clbutton2 {
  left: 39%;
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: calc(100vh * 48 / 1080);

  .elcol-title-text {
    padding-left: calc(100vw * 10 / 1920);
    text-align: left;
    width: 40%;
    height: 100%;
  }

  .elcol-title-text2 {
    font-size: calc(100vw * 14 / 1920);
    color: #303133;
    width: calc(100vw * 75 / 1920);
  }

  .elcol-input {
    float: left;
    width: calc(60% - calc(100vw * 75 / 1920));
    border: none !important;
  }
}

.fir-timeline {
  width: 100%;
  height: calc(100vh * 158 / 1080);
  margin-top: calc(100vh * 20 / 1080);
  margin-bottom: 0;
  overflow-y: scroll;
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: calc(100vh * 20 / 1080);
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

::v-deep(.el-textarea__inner) {
  padding: calc(100vh * 16 / 1080);
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: calc(100vh * 20 / 1080);
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

::v-deep(.el-collapse) {
  border: 0 solid #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: calc(100vw * 1 / 1920) solid #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40 / 1080);
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: calc(100vh * 4 / 1080);
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: calc(-100vw * 10 / 1920);
    top: calc(100vh * 2 / 1080);
    width: calc(100vw * 4 / 1920);
    height: calc(100vh * 15 / 1080);
    background: #5585f0;
    border-radius: calc(100vh * 2 / 1080);
  }
}

.elcol-title-left {
  width: calc(100vw * 8 / 1920);
  height: calc(100vh * 8 / 1080);
  border-radius: 50%;
  margin-top: calc(100vh * 20 / 1080);
  margin-left: calc(100vw * 10 / 1920);
}

.highlight {
  background-color: yellow !important;
  cursor: pointer;
}

::v-deep .custom-dialog .el-dialog__body {
  padding-top: calc(100vh * 120 / 1080);
}

::v-deep .custom-dialog .el-dialog {
  height: calc(100vh * 400 / 1080);
  background: url("../assets/tc.png") no-repeat center !important;
  background-size: cover !important;
}

::v-deep .custom-dialog .dialog-footer {
  position: absolute;
  bottom: calc(100vh * 20 / 1080);
  left: 50%;
  transform: translateX(-50%);
  padding-top: 0;
  width: 100%;
  display: flex;
  justify-content: center;
}

.el-button span {
  font-family: SourceHanSansSC-Medium;
}
::v-deep(.ql-editor) {
  font-size: calc(100vw * 18 / 1920);
}
::v-deep .el-dialog__footer {
  padding: 20px 20px 20px;
  text-align: right;
  box-sizing: border-box;
  margin-top: -80px;
}

.diff-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.title {
  text-align: center;
  margin-bottom: 10px;
  color: #303133;
}

.description {
  text-align: center;
  margin-bottom: 30px;
  color: #606266;
}

.input-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.input-group {
  flex: 1;
}

.input-group h3 {
  margin-bottom: 10px;
  color: #303133;
}

.actions {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 10px;
}

.diff-result {
  margin-top: 30px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.diff-result h2 {
  margin-bottom: 20px;
  color: #303133;
  text-align: center;
}

.diff-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.diff-view {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: auto;
  background-color: #fafafa;
}

/* 自定义vue-code-diff样式 */
:deep(.vue-code-diff) {
  font-family: "Courier New", Courier, monospace;
  font-size: calc(100vw * 14 / 1920);
}

:deep(.vue-code-diff table) {
  width: 100%;
}

:deep(.vue-code-diff td) {
  padding: 0 10px;
}

:deep(.d2h-code-line-ctn) {
  padding: 0 8px;
}

:deep(.d2h-code-linenumber) {
  color: #999;
  background-color: #f5f5f5;
}

:deep(.d2h-ins) {
  background-color: #e6ffed;
}

:deep(.d2h-del) {
  background-color: #ffeef0;
}

:deep(.d2h-code-side-linenumber) {
  color: #999;
  background-color: #f5f5f5;
}
::v-deep(.el-button--small) {
  padding: calc(100vh * 9 / 1080) calc(100vw * 15 / 1920);
}
/* 响应式调整 */
@media (max-width: 768px) {
  .input-container {
    flex-direction: column;
  }
}
// ***滚动条样式
.com-dialog ::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
}

.com-dialog ::-webkit-scrollbar-track {
  background: #fff;
}

.com-dialog ::-webkit-scrollbar-thumb {
  background: #808080;
}

// .input-header {
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   margin-bottom: 10px;
// }
/* 响应式调整 */
@media (max-width: 768px) {
  .input-container {
    flex-direction: column;
  }
  .input-header {
    flex-direction: column;
    align-items: flex-start;
  }
  .input-header .el-upload {
    margin-top: 10px;
  }
}
</style>
