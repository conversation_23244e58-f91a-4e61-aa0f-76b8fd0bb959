<!-- 本页为角色页，点击跳转到写作、提示词页 -->
<template>
    <div class="work-container" v-loading="con_loading">
      <el-drawer title="" :visible.sync="drawer" :direction="direction" style="width: 80%;"
        :before-close="handleClosedrawer">
      </el-drawer>
      <div :style="navShow ? 'width: 300px' : 'width: 136px'" class="ejdhl">
        <div class="ejdhlTitle">
          <!-- <img src="../assets/lo.png" alt="" />
          <p v-if="navShow">思盒</p> -->
          <img src="../img/logo05.png" width="100%" alt="" />
        </div>
        <div class="ai-nav" :style="navShow ? 'width: 300px' : 'width: 64px'">
          <!-- <p class="title" v-if="navShow">请选择公文文种</p> -->
          <div class="nav-list">
            <div class="nav-item" @click="clickTopLd" :style="navShow ? 'width: 272px' : 'width: 50px'">
              <img src="../assets/icon04.png" alt="" />
              <p v-if="navShow">角色</p>
            </div>
            <div class="nav-item" :style="navShow ? 'width: 272px' : 'width: 50px'" @click="clickszbg">
              <img src="../assets/icon01.png" alt="" />
              <p v-if="navShow">写作</p>
            </div>
  
            <div class="nav-item choose" :style="navShow ? 'width: 272px' : 'width: 50px'">
              <img src="../assets/icon07.png" alt="" />
              <p v-if="navShow">提示词</p>
            </div>
          </div>
          <div :class="navShow ? 'showUp' : 'showUp1'" :style="navShow ? 'width: 170px' : 'width: 50px'" @click="upShow">
            <img v-if="navShow" src="../assets/icon09.png" alt="" />
            <img v-else src="../assets/img-00.png" alt="" />
            <p v-if="navShow">向左收起</p>
          </div>
        </div>
      </div>
      <el-container>
        <el-header style="transition: ease-out 0.4s" :style="navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
    ">
        <div class="ai-header">
 <div class="flex">
          <div class="ai-left-bar-li actived">
            定制专属你的ai角色
          </div>
        </div> 
        </div>
      </el-header>
        <el-main  style="transition: ease-out 0.4s; display: flex;" :style="navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'">
          <div class="left" style="width: 50%;">
            <!-- 123 -->
            <el-form ref="form" :model="form" label-width="80px">
              <el-form-item label="姓名">
                <el-input v-model="form.name"></el-input>
              </el-form-item>
              <el-form-item label="性别">
                <el-radio-group v-model="form.resource">
                  <el-radio label="男生"></el-radio>
                  <el-radio label="女生"></el-radio>
                </el-radio-group>
              </el-form-item>
  
                <el-form-item label="年龄">
                  <el-input v-model="form.name"></el-input>
                </el-form-item>
                <el-form-item label="所在单位">
                  <el-input v-model="form.name"></el-input>
                </el-form-item>
                <el-form-item label="所属岗位">
                  <el-input v-model="form.name"></el-input>
                </el-form-item>
              <!-- </el-form-item> -->
              <el-form-item label="涉及工作领域">
                  <el-input v-model="form.name"></el-input>
                </el-form-item>
                <el-form-item label="所属区域">
                <el-radio-group v-model="form.resource">
                  <el-radio label="省"></el-radio>
                  <el-radio label="市"></el-radio>
                  <el-radio label="县"></el-radio>
                  <el-radio label="乡"></el-radio>
                </el-radio-group>
              </el-form-item>
              <!-- <el-form-item>
      <el-button type="primary" @click="onSubmit">立即创建</el-button>
      <el-button>取消</el-button>
      
    </el-form-item> -->
    <el-form-item>
    <el-button type="primary" @click="onSubmit">确定</el-button>
    <el-button>取消</el-button>
  </el-form-item>
            </el-form>
  
          </div>
          <div class="right" style="width: 50%;">
            <!-- sdf -->
            <Wordcloud></Wordcloud>
  
          </div>
        </el-main>
      </el-container>
    </div>
  </template>
  <script>
  import Wordcloud from '../components/Wordcloud.vue'
  import { VueEditor } from "vue2-editor";
  import CustomSteps from "../components/Step.vue";
  import store from '../store/index'
  import { mapState } from 'vuex';
  // ****************************
  import Quill from 'quill';
  let BlockEmbed = Quill.import('blots/block/embed');
  class CustomBlock extends BlockEmbed {
    static create(value) {
      let node = super.create();
      node.innerHTML = value.html;
      node.setAttribute('style', 'padding: 20px; border: 1px solid #488aff; color:red; position: relative;');
      // 保存当前光标位置
      const selection = value.editorClass.getSelection();
      value.savedSelection = selection;
      // 创建插入按钮
      let insertButton = document.createElement('button');
      insertButton.innerHTML = '应用';
      insertButton.className = 'btn-insert';
      insertButton.style.position = 'absolute';
      // 按钮颜色
      insertButton.style.backgroundColor = '#488aff';
      // 字体为白色
      insertButton.style.color = 'white';
      // insertButton.style.top = '10px';
      insertButton.style.right = '70px';
      insertButton.addEventListener('click', () => {
        // console.log(value.editorClass, '编辑器')
        // console.log(value.newText, '续写内容！')
        // // 这里的位置，是替换的传position，是插入的传position加length
        // console.log(value.Position, '位置')
        // console.log(value.selectedTextlength, '长度？？？？？？？？')
        // console.log(value.editorClass.getSelection(), '光标位置？？？？')
        if (value.flag == 1) {
          value.editorClass.insertText(value.Position + value.selectedTextlength, value.newText);
          node.remove();
        } else {
          value.editorClass.deleteText(value.Position, value.selectedTextlength);
          value.editorClass.insertText(value.Position, value.newText);
          node.remove();
        }
      });
      // 创建取消按钮
      let cancelButton1 = document.createElement('button');
      cancelButton1.innerHTML = '取消';
      cancelButton1.className = 'btn-cancel';
      cancelButton1.style.position = 'absolute';
      // cancelButton.style.top = '10px';
      cancelButton1.style.backgroundColor = '#e64721';
      // 字体为白色
      cancelButton1.style.color = 'white';
      cancelButton1.style.right = '10px';
      cancelButton1.addEventListener('click', () => {
        node.remove();
        // alert('取消按钮点击');
        // // 这里可以添加取消按钮的逻辑
      });
  
      // 将按钮添加到node中
      node.appendChild(insertButton);
      node.appendChild(cancelButton1);
  
      return node;
    }
  
    static value(node) {
      return node.innerHTML;
    }
  }
  CustomBlock.blotName = 'customBlock';
  CustomBlock.tagName = 'div';
  
  Quill.register({
    'formats/customBlock': CustomBlock
  });
  import {
    getSecDtDatas,
    getSuccessInfo,
    getSuccessInfoItem,
    getLabel1,
    getLabel2,
    testcheck,
    bcwz,
    rewrite,
  } from "../api/home.js"; // 接口请求
  export default {
    computed: {
      username() {
        return this.$store.state.username;
      },
      contextMenuStyle() {
        return {
          top: `${this.contextMenuPosition.y - 80}px`,
          // left: `${this.contextMenuPosition.x-200}px`
          left: `${this.contextMenuPosition.x - 600}px`
        };
      }
    },
    data() {
      return {
        fzqwnr: "",
        newText: "",
        newText1: "",
        newText2: "",
        newText3: "",
        xxdialog: false,
        xxcontent: "",
        kxdialog: false,
        sxdialog: false,
        rsdialog: false,
        contextMenuVisible: false,
        contextMenuPosition: { x: 100, y: 0 },
        selectedText: '',
        con_loading: false,
        loading1: false,
        correctionList: [],
        jydis: true,
        navShow: true,
        textarea: "",
        textarea2: [],
        textarea3: "",
        textarea4: "",
        textarea4: "",
        dialogShow: false,
        dialog2Show: false,
        gzbj: [],
        stepActived: 1,
        loading: false,
        curIndex: "1",
        articles: "",
        artShow: false,
        mask: false,
        maskAll: false,
        /**
         * @description:  功能性按钮
         * @return {*}    传入组件->FunctionTable
         */
        steps: [{ title: "基本信息" }, { title: "大纲" }, { title: "文章" }],
        currentStep: 1, // 初始化当前步骤为第二个步骤
        secRecommend: [], // 第二步推荐
        dynamicTags: [],
        inputVisible: true,
        inputValue: "",
        resDataItem: [],
        progressPercent: 0,
        fetchResPrams: [],
        buttonShow: true,
        drawer: false,
        direction: 'ltr',
        activeNames: [],
        // asyncStatus: true
        // return {
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
          // }
        }
      };
    },
    components: {
      // 注册组件
      CustomSteps,
      VueEditor,
      Wordcloud,
  
    },
    mounted() {
      // this.getInfo()
    },
           methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
     togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
      showContextMenu(event) {
        event.preventDefault();
        this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
        this.selectedText = window.getSelection().toString();
        if (this.selectedText) {
          this.contextMenuPosition = { x: event.clientX, y: event.clientY };
          this.contextMenuVisible = true;
          document.addEventListener('click', this.hideContextMenu);
        };
  
      },
      hideContextMenu() {
        this.contextMenuVisible = false;
        document.removeEventListener('click', this.hideContextMenu);
      },
      // 续写接口
      async sendToBackend() {
        this.loading1 = true;
        this.mask = true;
        let params1 = {
          "text": this.selectedText,
          "flag": "1"
        };
        let res = await rewrite(params1);
        this.newText = res.data.data;
        if (res.data.status_code == 200) {
          this.loading1 = false;
          this.mask = false;
          this.$message({
            message: '续写成功',
            type: 'success'
          });
          // 注册自定义样式
          const editor = this.$refs.editor.quill;
          // 获取编辑器中的所有文本
          const allText = editor.getText();
          // 找到你传给后端的那段文字的位置
          const position = allText.indexOf(this.selectedText);
          if (position !== -1) {
            const editor = this.$refs.editor.quill;
            editor.insertEmbed(position + this.selectedText.length, 'customBlock', {
              html: this.newText,
              newText: this.newText,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 1,
            }, Quill.sources.USER);
            // 将后端返回的续写文字插入到该位置后面
            // editor.insertText(position + this.selectedText.length, this.newText);
            // editor.formatText(position + this.selectedText.length, this.newText.length, 'color', 'red');
            // // 将光标移动到新插入的文本后面
            // editor.setSelection(position + this.selectedText.length + this.newText.length, 0);
          } else {
            // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
            editor.insertText(editor.getLength(), this.newText);
            // 将新插入的文本格式化为红色
            console.log('Formatting text at position:', editor.getLength() - this.newText.length);
            console.log('Formatting text length:', this.newText.length);
            editor.formatText(editor.getLength() - this.newText.length, this.newText.length,);
  
            editor.setSelection(editor.getLength(), 0);
          }
        }
        else {
          this.$message({
            message: '续写失败',
            type: 'error'
          })
        };
  
      },
  
      // 2.扩写接口
      async sendToBackend2() {
        this.loading1 = true;
        this.mask = true;
        let params2 = {
          "text": this.selectedText,
          "flag": "2"
        };
        let res = await rewrite(params2);
        this.newText1 = res.data.data;
        if (res.data.status_code == 200) {
          this.loading1 = false;
          this.mask = false;
          this.$message({
            message: '扩写成功',
            type: 'success'
          });
          // this.loading = false;
          //         // this.xxdialog=true;
          //         this.xxcontent=this.newText;
          const editor = this.$refs.editor.quill;
          // 获取编辑器中的所有文本
          const allText = editor.getText();
  
          // 找到你传给后端的那段文字的位置
          const position = allText.indexOf(this.selectedText);
  
          if (position !== -1) {
            const editor = this.$refs.editor.quill;
            const position = allText.indexOf(this.selectedText);
  
            editor.insertEmbed(position + this.selectedText.length, 'customBlock', {
              html: this.newText1,
              newText: this.newText1,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 2,
            }, Quill.sources.USER);
          } else {
            // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
            editor.insertText(editor.getLength(), this.newText1);
  
            // 将新插入的文本格式化为红色
            console.log('Formatting text at position:', editor.getLength() - this.newText1.length);
            console.log('Formatting text length:', this.newText1.length);
            editor.formatText(editor.getLength() - this.newText1.length, this.newText1.length,);
            editor.setSelection(editor.getLength(), 0);
          }
  
        }
        else {
          this.$message({
            message: '扩写失败',
            type: 'error'
          })
        };
      },
      // 3.缩写接口
      async sendToBackend3() {
        this.loading1 = true;
        this.mask = true;
        let params3 = {
          "text": this.selectedText,
          "flag": "3"
        };
        let res = await rewrite(params3);
        this.newText2 = res.data.data;
        if (res.data.status_code == 200) {
          this.loading1 = false;
          this.mask = false;
          this.$message({
            message: '缩写成功',
            type: 'success'
          });
          const editor = this.$refs.editor.quill;
          // 获取编辑器中的所有文本
          const allText = editor.getText();
  
          // 找到你传给后端的那段文字的位置
          const position = allText.indexOf(this.selectedText);
  
          if (position !== -1) {
            const editor = this.$refs.editor.quill;
            editor.insertEmbed(position + this.selectedText.length, 'customBlock', {
              html: this.newText2,
              newText: this.newText2,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 3,
            }, Quill.sources.USER);
          } else {
            // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
            editor.insertText(editor.getLength(), this.newText2);
  
            // 将新插入的文本格式化为红色
            console.log('Formatting text at position:', editor.getLength() - this.newText2.length);
            console.log('Formatting text length:', this.newText2.length);
            editor.formatText(editor.getLength() - this.newText2.length, this.newText2.length,);
            editor.setSelection(editor.getLength(), 0);
          }
  
        }
        else {
          this.$message({
            message: '缩写失败',
            type: 'error'
          });
        };
  
      },
      // 4.润色接口
      async sendToBackend4() {
        this.loading1 = true;
        this.mask = true;
        let params4 = {
          "text": this.selectedText,
          "flag": "4"
        };
        let res = await rewrite(params4);
        this.newText3 = res.data.data;
        if (res.data.status_code == 200) {
          this.loading1 = false;
          this.mask = false;
          this.$message({
            message: '润色成功',
            type: 'success'
          });
          const editor = this.$refs.editor.quill;
          // 获取编辑器中的所有文本
          const allText = editor.getText();
  
          // 找到你传给后端的那段文字的位置
          const position = allText.indexOf(this.selectedText);
  
          if (position !== -1) {
            const editor = this.$refs.editor.quill;
            editor.insertEmbed(position + this.selectedText.length, 'customBlock', {
              html: this.newText3,
              newText: this.newText3,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 4,
            }, Quill.sources.USER);
          } else {
            // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
            editor.insertText(editor.getLength(), this.newText3);
  
            // 将新插入的文本格式化为红色
            console.log('Formatting text at position:', editor.getLength() - this.newText3.length);
            console.log('Formatting text length:', this.newText3.length);
            editor.formatText(editor.getLength() - this.newText3.length, this.newText3.length,);
  
            editor.setSelection(editor.getLength(), 0);
          }
  
        }
        else {
          this.$message({
            message: '润色失败',
            type: 'error'
          })
        };
      },
      copyText() {
        // 复制逻辑
        console.log('复制');
      },
      pasteText() {
        // 粘贴逻辑
        console.log('粘贴');
      },
      cutText() {
        // 剪切逻辑
        console.log('剪切');
      },
      // }
      async baocun() {
        console.log(this.$route.query.userName)
        let params = {
          wznr: this.textarea4.replace('"', '/"'),
          wzlx: "述职报告",
          userName: this.username,
        };
        let resbcwz = await bcwz(params);
        //  console.log(resbcwz)
        resbcwz.data.status_code == 200 ? this.$message({
          message: '保存成功,请前往我的文档查看',
          type: 'success'
        }) : this.$message({
          message: '保存失败', type: 'error'
        })
      },
      marked(markdown) {
        // 将 Markdown 文本按行分割
        const lines = markdown.split('\n');
        let html = '';
        let inList = false;
        let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
        lines.forEach(line => {
          // 处理标题
          // 处理标题
          if (line.startsWith('#')) {
            const level = line.indexOf(' ');
            const content = line.slice(level + 1);
            if (isFirstHeading) {
              html += `<h${level} style="text-align: center;">${content}</h${level}>`;
              isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
            } else {
              html += `<h${level} style="text-align: left;">${content}</h${level}>`;
            }
          }
          // 处理无序列表
          else if (line.startsWith('-')) {
            if (!inList) {
              html += '<ul style="text-align: left;">';
              inList = true;
            }
            const content = line.slice(2);
            html += `<li>${content}</li>`;
          }
          // 处理段落
          else {
            if (inList) {
              html += '</ul>';
              inList = false;
            }
            html += `<p style="text-align: left;">${line}</p>`;
          }
        });
  
        // 如果列表没有关闭，关闭它
        if (inList) {
          html += '</ul>';
        }
        console.log(html, '************************************');
        // this.textarea3 = html;
        // return html;
      },
      getPlainText(html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
      },
      //     getHtmlContent(html) {
      //   const div = document.createElement('div');
      //   div.innerHTML = html;
      //   return div.innerHTML;
      // },
  
      onCopySuccess() {
        console.log('复制成功');
      },
      onCopyError() {
        console.log('复制失败');
      },
      wdwd() {
        // alert(1)
        this.$router.push("/wdwd");
      },
      logout() {
        clearVuexAlong()
        store.commit('addNewToken', '')
        this.$router.push("/login");
      },
      //按钮颜色
      getBackgroundColor(index) {
        const colors = ['#ff6403', '#fbff00', '#01fffe', '#e958ea'];
        // 使用取余运算符来循环数组中的颜色
        return colors[index % colors.length];
      },
      ignore() {
        this.textarea4 = this.removetext(this.textarea4)
        this.activeNames = []
      },
      highlightChange(text1, text2) {
        console.log(this.textarea4);
        let changeData = text2
        // 高亮显示文章中的错误文字
        // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
        // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
        this.textarea4 = this.removetext(this.textarea4)
        this.textarea4 = this.textarea4.replace(text1, changeData);
      },
      highlightError(error, word) {
        this.textarea4 = this.removetext(this.textarea4)
        let a = error.replace(word, `<span class="highlight" style="background-color:yellow!important;">${word}</span>`);
        this.textarea4 = this.textarea4.replace(error, a);
        console.log(this.textarea4);
        this.$nextTick(() => {
          // 确保 DOM 更新完成后，滚动到高亮文字
          this.scrollToHighlight();
        });
      },
      scrollToHighlight() {
        // 获取 vue-editor 中的 iframe
        const editorIframe = this.$refs.editor.$el.querySelector('div.ql-editor');
        console.log(this.$refs.editor.$el, 'editorIframe');
        if (editorIframe) {
          const highlightedElement = editorIframe.querySelector('span.highlight');
          console.log(highlightedElement, 'highlightedElement');
          if (highlightedElement) {
            // 滚动到第一个高亮文字
            highlightedElement.scrollIntoView({ behavior: "smooth", block: "center" });
          }
        }
      },
      removetext(text) {
        let str = text
        let reg1 = new RegExp(`<span class="highlight" style="background-color:yellow!important;">`, 'g');
        let a1 = str.replace(reg1, '');
        let reg2 = new RegExp(`</span>`, 'g');
        text = a1.replace(reg2, '');
        return text
      },
      removetextp(text) {
        let str = text
        let reg1 = new RegExp(`<p>`, 'g');
        let a1 = str.replace(reg1, '');
        let reg2 = new RegExp(`</p>`, 'g');
        text = a1.replace(reg2, '');
        return text
      },
      onCopySuccess() {
        this.$message({
          message: "内容已复制到剪贴板！",
          type: "success",
        });
      },
      onCopyError() {
        this.$message({
          message: "复制失败，请手动复制!",
          type: "warning",
        });
      },
      handleInputConfirm() {
        let inputValue = this.inputValue;
        if (inputValue) {
          this.dynamicTags.push(inputValue);
          // 
          this.textarea2 = this.dynamicTags
        }
        // this.inputVisible = false;
        this.inputValue = "";
      },
      async jiaoyan() {
        this.textarea4 = this.removetext(this.textarea4)
        this.con_loading = true;
        // let text = this.removetextp(this.textarea4)
        testcheck({
          test_all: this.textarea4,
        }).then(async (data) => {
          this.correctionList = data.data.data;
          this.con_loading = false;
          if (this.correctionList.length == 0) {
            this.jydis = true
            this.$message({
              message: '不存在文字错误',
              type: 'warning'
            });
          } else {
            console.log(data, "619");
            this.jydis = false
          }
        });
  
      },
      handleClosedrawer() {
        this.textarea4 = this.removetext(this.textarea4)
        this.activeNames = []
        this.drawer = false
      },
      // async jiaoyan(){
      //   this.jydis = false
      // },
      save() {
        this.textarea4 = this.removetext(this.textarea4)
        this.activeNames = []
        this.jydis = true
      },
      fanhui() {
        this.textarea4 = this.removetext(this.textarea4)
        this.activeNames = []
        this.drawer = false
      },
      handleClose(tag) {
        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      },
      handleStepClick(index) {
        this.lock++;
        this.activeIndex = index; // 更新activeIndex的值
      },
      upShow() {
        this.navShow = !this.navShow;
      },
      async textareaFocus() {
        let data = await getLabel1();
        this.gzbj = data.data;
        this.dialogShow = true;
      },
      closeDialog() {
        this.dialogShow = false;
      },
      textarea2Focus() {
  
        this.dialog2Show = true;
      },
      closeDialog2() {
        this.dialog2Show = false;
      },
      async getText(e) {
        console.log(e);
        let params = {
          label: e,
        };
        let data = await getLabel2(params);
        console.log(data, "583");
        this.secRecommend = data.data;
        this.textarea = e;
        this.textarea2 = [];
      },
      getText2(e) {
        if (!this.dynamicTags.includes(e)) {
          this.textarea2.push(e);
          this.dynamicTags.push(e);
          // this.textarea = e.srcElement.innerText;
          this.handleInputConfirm(e);
        } else {
          console.log(111);
        }
  
      },
      clickTopNav() {
        this.$notify({
          title: "提示",
          message: "暂未开发，敬请期待！",
          type: "warning",
        });
      },
      clickszbg() {
        this.$router.push("/xz");
  
  
      },
  
  
      // 角色
      clickTopLd() {
        this.$router.push("/grzx");
  
      },
  
      clickToptsc() {
        this.$router.push("/tsc");
  
      },
      firstNextStep() {
        if (this.textarea == "") {
          this.$notify({
            title: "提示",
            message: "请填写工作背景",
            type: "warning",
          });
        } else if (this.textarea2.length == 0) {
          this.$notify({
            title: "提示",
            message: "请填写工作要点关键词",
            type: "warning",
          });
        } else {
          this.mask = true;
          if (this.mask == true) {
            this.getInfo(this.textarea, this.dynamicTags);
          }
        }
      },
      async getInfo(c1, c2) {
        this.loading = true;
        let params = {
          test_1: c1,
          test_2: c2,
        };
        let res = await getSecDtDatas(params);
        // console.log(res.data.status_code,'我是状态码11111')
        // console.log(res.data.message,'我是提示33332222')
        if (res.data.status_code == 200) {
          // 弹出提示
          this.$message({
            message: res.data.message,
            type: 'success'
          });
          this.textarea3 = "";
  
          // console.log(res.data, 55555555);
          this.textarea3 = res.data.data;
          // console.log(this.textarea3,66666);
          // 这里放置需要执行的逻辑或调用其他方法
          this.buttonShow = true;
          // this.stepActived = 2;
          this.currentStep = 2;
          this.loading = false;
          this.mask = false;
          this.curIndex = "2";
        }
        else if (res.data.status_code == 500) {
  
          this.$message({
            message: res.data.message,
            type: 'error'
          })
        }
        else if (res.data.status_code == 10001) {
  
          this.$message({
            message: res.data.message,
            type: 'error'
          })
        }
        else if (res.data.status_code == 10002) {
  
          this.$message({
            message: res.data.message,
            type: 'error'
          })
        }
      },
      repeatStep() {
        this.loading = true;
        this.mask = true;
        if (this.mask == true) {
          this.getInfo(this.textarea, this.dynamicTags);
        }
      },
      success() {
        this.artShow = true;
        this.loading = true;
        // 这里放置需要执行的逻辑或调用其他方法
        if (this.textarea3 == "") {
          this.$notify({
            title: "提示",
            message: "大纲内容为空，无法生成",
            type: "warning",
          });
        } else {
          this.progressPercent = 0;
          this.getSuccessInfo(this.textarea3, this.textarea);
        }
      },
      // 依次请求接口
      async fetchResData(index, nextIndex) {
        if (index >= this.fetchResPrams.length) {
          this.fetchData(nextIndex);
          return;
        }
        console.log(this.fetchResPrams[index], "675");
        let pams = {
          string: this.fetchResPrams[index],
          id_name: this.textarea,
        };
        const response = await getSuccessInfoItem(pams);
        if (response.data.status_code == 200) {
          // this.$message({
          //   message: response.data.message,
          //   type: 'success'
          // });
          this.textarea4 += response.data.data;
  
          let totalLength = 0;
          let allSubArrays = this.resDataItem.filter(
            (item) => typeof item != "string"
          );
          allSubArrays.forEach((subArray) => {
            totalLength += subArray.length;
          });
          let kuai = 100 / totalLength;
          this.progressPercent += Math.floor(kuai);
          await this.fetchResData(index + 1, nextIndex);
        }
        else if (response.data.status_code == 500) {
  
          this.$message({
            message: response.data.message,
            type: 'error'
          })
        }
        else if (response.data.status_code == 10001) {
  
          this.$message({
            message: response.data.message,
            type: 'error'
          })
        }
        else if (response.data.status_code == 10002) {
  
          this.$message({
            message: response.data.message,
            type: 'error'
          })
        }
      },
      // 一段式生成
      async fetchData(index) {
        if (index >= this.resDataItem.length) {
          this.progressPercent = 100;
          this.maskAll = false;
          this.buttonShow = false;
          return;
        }
        if (typeof this.resDataItem[index] == "string") {
          this.textarea4 += this.resDataItem[index];
          await this.fetchData(index + 1);
        } else {
          this.fetchResPrams = this.resDataItem[index];
          this.fetchResData(0, index + 1);
        }
        // this.stepActived = 3;
        this.currentStep = 3;
        this.loading = false;
      },
      async getSuccessInfo(c1, c2) {
        this.loading = true;
        this.maskAll = true;
        let params = {
          string: c1,
          work: c2,
        };
        let res = await getSuccessInfo(params);
        if (res.data.status_code == 200) {
          // this.$message({
          //   message: res.data.message,
          //   type: 'success'
          // })
          this.textarea4 = "";
          this.resDataItem = res.data.data;
          this.fetchData(0);
          this.currentStep = 3;
          // this.stepActived = 3;
          this.loading = false;
          // }
        }
        else if (res.data.status_code == 500) {
          this.$message({
            message: res.data.message,
            type: 'error'
          })
        }
  
      },
      clickTab(i) {
        // if (this.currentStep != 2) {
        //   this.$notify({
        //     title: "提示",
        //     message: "请先输入工作背景和工作要点生成大纲",
        //     type: "warning",
        //   });
        // } else {
        this.curIndex = i;
        // }
      },
    },
  };
  </script>
  <style lang="less" scoped>
  .flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh *  50 / 1080);
            width: 400px;
            color: #000;
        
            line-height: 16px;
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  19 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            

          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            color: #fff;
          }
  .context-menu {
    margin-left: -200px;
    position: absolute;
    // background: white;
    background: #fff;
  
    // border: 1px solid #ccc;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }
  
  .context-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
    // margin-left:-200px;
  
  }
  
  .context-menu li {
    padding: 8px 16px;
    cursor: pointer;
    // margin-left:-200px;
    color: #0b0b0b;
  
  }
  
  .context-menu li:hover {
    background: #f0f0f0;
  }
  
  .fr {
    float: right
  }
  
  .work-container {
    height: 100%;
  
    .result-container {
      padding: 30px;
  
      .elcol-title {
        display: flex;
        overflow: hidden;
        width: 100%;
        height: 48px;
  
        .elcol-title-text {
          // float: left;
          padding-left: 10px;
          text-align: left;
          width: 40%;
          height: 100%;
        }
  
        .elcol-title-text2 {
          font-size: calc(100vw *  14 / 1920);
          // color: #d1d7de;
          // float: left;
          width: 75px;
        }
  
  
  
        .elcol-input {
          float: left;
          width: calc(60% - 75px);
          border: none !important;
        }
      }
    }
  }
  
  .elcol-input ::v-deep(.el-input__inner) {
    border: none !important;
    background-color: #f9fafe;
    height: 20px;
  }
  
  .elcol-input::v-deep(.el-input__inner) {
    overflow: hidden;
    /* 隐藏超出部分 */
    text-overflow: ellipsis;
    /* 超出部分显示省略号 */
    white-space: nowrap;
    /* 不换行 */
  }
  
  
  .elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
    width: 43% !important;
  }
  
  .ejdhl {
    height: 100%;
    // height: 1080px;
    background: url(../assets/img-left.png) no-repeat center;
    background-size: cover;
    background-position: center;
    transition: ease-out 0.4s;
  }
  
  .ejdhlTitle {
    height: calc(100vh * 80 / 1080);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: ease-out 0.4s;
    background-image: linear-gradient(76deg,
        #07389c 0%,
        #3d86d1 0%,
        #3448b3 100%);
  
    img {
      width: 120px;
      height: 48px;
    }
  
    p {
      border: 1.54px solid rgba(0, 0, 0, 0);
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  24 / 1920);
      color: #000000;
      text-align: left;
      color: #fff;
      display: flex;
      align-items: center;
      margin-left: 5px;
    }
  }
  
  .el-header {
    // width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: calc(100vh * 80 / 1080) !important;
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
    position: fixed;
    top: 0;
    right: 0;
    z-index: 20;
    width: calc(100% - 300px);
  
    .ai-header {
      width: 100%;
  
      .ai-bar {
        width: calc(100% - 300px);
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        height: calc(100vh * 80 / 1080);
        background-color: #fff;
  
        .ai-left-bar {
          display: flex;
          justify-content: center;
          align-items: center;
          column-gap: 40px;
          height: calc(100vh * 40/ 1080);
          border-radius: 20px;
          background: #f4f6f8;
          border: 1px solid #e6e6e6;
  
          .ai-left-bar-ul {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            height: 100%;
  
            .ai-left-bar-li {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 42px;
              width: 130px;
              color: #000;
              font-size: calc(100vw *  14 / 1920);
              line-height: 16px;
              white-space: nowrap;
              cursor: pointer;
              z-index: 9999;
              font-family: PingFangSC-Semibold;
              font-size: calc(100vw *  14 / 1920);
              color: #000000;
              letter-spacing: 0;
              text-align: center;
  
              img {
                width: 16px;
                height: 16px;
                margin-right: 14px;
              }
  
              &:hover {
                background-image: linear-gradient(107deg,
                    #3a6bc6 0%,
                    #488aff 100%);
                box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                border-radius: 20px;
                color: #fff;
              }
            }
  
            .actived {
              background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: 20px;
              color: #fff;
            }
          }
        }
  
        .ai-right-bar {
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
          align-items: center;
          position: fixed;
          right: 93px;
          height: calc(100vh * 80 / 1080);
          // column-gap: 16px;
          // height: 100%;
          // margin-left: 30px;
  
          .top-button {
            display: flex;
            justify-content: center;
            align-items: center;
  
            img {
              width: 16px;
              height: 16px;
            }
          }
  
          .btn {
            margin-left: 30px;
          }
  
          .el-dropdown-link {
            cursor: pointer;
            color: #409eff;
          }
  
          .el-icon-arrow-down {
            font-size: calc(100vw *  12 / 1920);
          }
  
          .ai-avant {
            overflow: hidden;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
  
            img {
              width: 28px;
              height: 28px;
              float: left;
            }
  
            p {
              float: left;
              margin-left: 22px;
              margin-top: -2px;
              display: flex;
              justify-content: center;
              align-items: center;
              font-family: SourceHanSansSC-Regular;
              font-size: calc(100vw *  14 / 1920);
              color: #000000;
              letter-spacing: 0;
              font-weight: 400;
            }
          }
        }
      }
    }
  }
  
  .el-main {
    background-color: #f8f9fd;
    color: #333;
    text-align: center;
    // line-height: 160px;
    width: calc(100% - 300px);
    height: calc(100% - calc(100vh * 80 / 1080));
    position: absolute;
    right: 0;
    top: calc(100vh * 80 /1080);
    padding: 20px 10px;
    // overflow: hidden;
  
    .ai-gai {
      position: fixed;
      /* top: 0; */
      left: 0;
      bottom: 0;
      height: 91%;
      // background: rgba(122, 151, 255, 0.6);
      z-index: 999;
    }
  
    .ai-body {
      transition: ease-out 0.4s;
      width: 100%;
      height: 100%;
      // background: red;
      float: left;
      overflow: hidden;
  
      .ai-body-left {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        width: 444px;
        height: 100%;
        background: #ffffff;
        border-radius: 8px;
        overflow-x: hidden;
        overflow-y: auto;
        margin-left: 10px;
        float: left;
  
        .ai-body-left-top {
          width: 100%;
          height: calc(100% - calc(100vh * 80 / 1080));
          overflow-y: scroll;
        }
  
        .ai-body-left-bottom {
          width: 100%;
          height: 90px;
  
          // padding-top: 30px;
          .ai-body-left-bottom-button {
            height: calc(100vh * 40/ 1080);
            // font-weight: 600;
            font-size: calc(100vw *  14 / 1920);
            letter-spacing: 0px;
            flex-grow: 1;
            color: #fff;
            margin: 0 25px;
            cursor: pointer;
            line-height: calc(100vh * 40/ 1080);
            margin-top: 30px;
            background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            font-family: PingFangSC-Regular;
          }
        }
  
        .ai-body-left-bottom2 {
          width: 100%;
          height: calc(100vh * 80 / 1080);
          padding-top: 30px;
  
          .repeat {
            width: 188px;
            float: left;
            height: calc(100vh * 40/ 1080);
            font-size: calc(100vw *  14 / 1920);
            flex-grow: 1;
            margin-left: 25px;
            cursor: pointer;
            line-height: calc(100vh * 40/ 1080);
            background: #f4f6f8;
            border: 1px solid rgba(230, 230, 230, 1);
            border-radius: 20px;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  14 / 1920);
            color: #666666;
            letter-spacing: 0;
            text-align: center;
            // font-weight: 600;
          }
  
          .ai-body-left-bottom-button {
            width: 188px;
            float: left;
            height: calc(100vh * 40/ 1080);
            flex-grow: 1;
            margin-left: 14px;
            cursor: pointer;
            line-height: calc(100vh * 40/ 1080);
            background-image: linear-gradient(102deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            margin-top: 0px;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  14 / 1920);
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
            // font-weight: 600;
          }
        }
  
        .ai-step {
          margin-top: 20px;
          width: calc(100% - 40px);
          margin: 0 20px;
          margin-top: 5px;
          height: calc(100vh * 40/ 1080);
        }
      }
  
      .ai-body-right {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        width: calc(100% - 480px);
        height: 100%;
        background: #ffffff;
        border-radius: 8px;
        overflow-x: hidden;
        overflow-y: auto;
        margin-left: 20px;
        float: left;
  
        .ai-body-start {
          width: 100%;
          height: 100%;
  
          .pic_bkg1 {
            // width: calc(100vw * 670 / 1920);
            // height: calc(100vw * 590 / 1920);
            width: 670px;
            height: 590px;
            background: url(../assets/img-bg1.png) no-repeat center;
            // background: url(../assets/img-bg1.png) no-repeat center;
  
            background-size: 100% 100%;
            margin: 0 auto;
            margin-top: 70px;
            position: relative;
          }
  
          .pic_bkg {
            width: 528px;
            height: 518px;
            // z-index: 15;
            background: url(../assets/img-ai.png) no-repeat center;
            background-size: 100% 100%;
            margin: 0 auto;
            margin-top: 118px;
            position: relative;
          }
  
          .pic_font {
            position: absolute;
            margin-left: auto;
            margin-right: auto;
            left: 0;
            right: 0;
            bottom: 116px;
            width: 255px;
            height: calc(100vh * 40/ 1080);
            border: 1.54px solid rgba(0, 0, 0, 0);
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  28 / 1920);
            color: #000000;
            text-align: center;
            font-weight: 600;
          }
  
          .title_message {
            position: absolute;
            margin-left: auto;
            margin-right: auto;
            left: 0;
            right: 0;
            bottom: 82px;
            text-align: center;
            line-height: 16px;
            margin-top: 10px;
            height: 22px;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #999999;
            letter-spacing: 0;
            font-weight: 400;
          }
  
          .pic_step {
            width: 551px;
            height: 142px;
            z-index: 15;
            background: url("../assets/pic_step.png");
            background-size: contain;
            margin: auto;
          }
        }
  
        .ai-body-art {
          width: 100%;
          height: 100%;
          overflow: hidden;
          position: relative;
  
          .over {
            overflow: auto;
          }
  
          .fir-textarea {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 96.5%;
            background: #f8f9fd;
            border: 1px solid rgba(229, 232, 245, 1);
            border-radius: 4px;
            margin: 20px;
            margin-bottom: 0;
            height: 158px;
  
            ::v-deep(.el-textarea__inner) {
              font-size: 14px !important;
              background-color: #f8f9fd !important;
              height: 100% !important;
              font-family: PingFangSC-Regular;
              padding: 13px 18px 33px 16px;
            }
          }
  
          .fir-textarea-max {
            height: 95% !important;
          }
  
          ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
            display: none !important;
          }
  
          ::v-deep(.ql-blank) {
            display: none !important;
          }
  
          ::v-deep(.ql-container.ql-snow) {
            border: 0;
          }
        }
      }
  
      .ai-tab {
        width: 230px;
        height: calc(100vh * 40/ 1080);
        margin: 0 auto;
        margin-top: 30px;
        background: #f4f6f8;
        border: 1px solid #eeeff0;
        border-radius: 20px;
  
        .tab-item {
          width: 50%;
          height: calc(100vh * 40/ 1080);
          line-height: 16px;
          float: left;
          line-height: calc(100vh * 40/ 1080);
          cursor: pointer;
  
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #9094a5;
          letter-spacing: 0;
          text-align: center;
        }
  
        .activedTab {
          border-radius: 20px;
  
          background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          color: #ffffff;
        }
      }
  
      .tab-item-fir {
        width: 100%;
        height: 536px;
        padding: 0 25px;
  
        .fir-title {
          color: #222;
          font-weight: 500;
          font-size: calc(100vw *  14 / 1920);
          letter-spacing: 0;
          line-height: 16px;
          overflow: hidden;
          margin-top: 20px;
          margin-bottom: 20px;
          height: 20px;
  
          .fir-kuai {
            width: 6px;
            height: 16px;
            margin-right: 8px;
            float: left;
            // margin-top: 2px;
            background: #4081ff;
            border-radius: 1.5px;
          }
  
          .fir-title-p {
            line-height: 16px;
            float: left;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #333333;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
  
        .fir-alert {
          margin-top: 10px;
          height: 35px;
        }
  
        .ai-dialog {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          row-gap: 1px;
          width: 100%;
          height: -moz-fit-content;
          height: 290px;
          max-height: 294px;
          padding: 7px;
          box-shadow: 0 20px 40px 4px #e4e4e524;
          margin-top: 10px;
          transition: ease-out 0.4s;
          background: #ace9ff;
          border: 1px solid rgba(90, 206, 255, 1);
          border-radius: 4px;
  
          .ai-d-title {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: flex-start;
            width: 100%;
            height: -moz-fit-content;
            height: fit-content;
            margin: 0;
            padding: 1px 3px 2px 2px;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #313733;
            letter-spacing: 0;
            font-weight: 400;
  
            .ai-d-title-p {
              flex-grow: 1;
              line-height: 16px;
              text-align: left;
              font-family: PingFangSC-Regular;
              font-size: calc(100vw *  14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 400;
              margin-bottom: 10px;
            }
  
            img {
              width: 18px;
              height: 18px;
              cursor: pointer;
            }
          }
  
          .ai-d-body {
            width: 100%;
            height: calc(100% - 44px);
            overflow: hidden;
            background: #ffffff;
            border-radius: 4px;
  
            .hints-control {
              display: flex;
              flex-direction: row;
              justify-content: flex-start;
              align-items: center;
              width: 100%;
              padding: 14px 14px 0;
              height: 30px;
  
  
              .hint-icon {
                flex-grow: 0;
                flex-shrink: 0;
                width: 20px;
                height: 20px;
                margin-right: 6px;
                background-size: contain;
                background-image: url("../assets/icon_fire.png");
              }
  
              .hint-description {
                font-weight: 600;
                line-height: 14px;
                font-family: SourceHanSansSC-Bold;
                font-size: calc(100vw *  14 / 1920);
                color: #313733;
                letter-spacing: 0;
                font-weight: 700;
              }
            }
  
            .ai-tj-body {
              width: 100%;
              // height: 100%;
              // overflow: hidden;
              height: 200px;
              overflow-y: auto;
  
              /* 垂直滚动条 */
              .ai-tj-item {
                padding: 14px 14px 0;
                line-height: 12px;
                width: 50%;
                text-align: left;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                float: left;
                cursor: pointer;
                height: 30px;
                font-family: PingFangSC-Regular;
                font-size: calc(100vw *  14 / 1920);
                color: #313733;
                letter-spacing: 0;
                font-weight: 400;
                // &:hover {
  
                // }
              }
            }
          }
        }
  
        // ***滚动条样式
        .ai-tj-body::-webkit-scrollbar {
          width: 6px;
          /* 滚动条的宽度 */
        }
  
        .ai-tj-body::-webkit-scrollbar-track {
          background: #fff;
          /* 滚动条的背景色 */
        }
  
        .ai-tj-body::-webkit-scrollbar-thumb {
          background: #488aff;
          /* 滚动条的滑块颜色 */
        }
  
        .fir-textarea {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          margin-top: 14px;
          height: 158px;
          background: #f8f9fd;
          // border: 1px solid rgba(229, 232, 245, 1);
          border-radius: 4px;
  
          ::v-deep(.el-textarea__inner) {
            font-size: 14px !important;
            background-color: #f8f9fd !important;
            height: 100% !important;
            font-family: PingFangSC-Regular;
          }
        }
  
        .fir-textarea-height {
          height: 460px !important;
        }
      }
    }
  }
  
  ::v-deep(.el-textarea__inner) {
    padding: 16px;
  }
  
  ::v-deep(.el-textarea .el-input__count) {
    color: #909399;
    background: none;
    position: absolute;
    font-size: calc(100vw *  12 / 1920);
    right: 10px;
    height: 10px;
  }
  
  .ai-nav {
    // width: 180px;
    // height: 100%;
    height: calc(100% - calc(100vh * 80 / 1080));
    background-position: left;
    background-repeat: no-repeat;
    background-size: cover;
    transition: ease-out 0.4s;
  
    .title {
      width: 300px;
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  16 / 1920);
      color: #ffffff;
      letter-spacing: 0;
      letter-spacing: 0px;
      height: 20px;
      line-height: 20px;
      padding-top: 20px;
    }
  
    .nav-list {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      width: 100%;
      padding-top: 45px;
      height: calc(100% - calc(100vh * 80 / 1080));
    }
  }
  
  .user-menu-content {
    width: calc(100vw *  246 / 1920);
    height: 24px;
    padding: 0 20px;
    background: #ffffff;
  
    .user-info {
      display: flex;
      align-items: center;
      width: 100%;
      margin-top: 17px;
  
      .avatar-wrapper {
        position: relative;
        width: 52px;
        height: 52px;
        border: 1px solid rgba(122, 151, 255, 0.6);
        border-radius: 50%;
        background-size: contain;
        background-repeat: no-repeat;
  
        img {
          width: 52px;
          height: 52px;
        }
      }
  
      .name-wrapper {
        width: 300px;
        display: flex;
        flex-direction: column;
        margin-left: 12px;
  
        .name {
          width: 300px;
          color: #222;
          font-weight: 600;
          // font-size: calc(100vw *  16 / 1920);
          font-size: calc(100vw *  15 / 1920);
          letter-spacing: 0px;
          line-height: 16px;
        }
  
  
        .id {
          margin-top: 7px;
          color: #7c86ac;
          font-weight: 400;
          font-size: calc(100vw *  12 / 1920);
          letter-spacing: 0px;
          line-height: 12px;
        }
      }
    }
  
    .divider {
      width: 100%;
      margin: 20px 0 18px;
      border-bottom: 1px solid #f0f3fa;
    }
  
    .options {
  
      .option {
        display: flex;
        align-items: center;
        margin-top: 20px;
        cursor: pointer;
  
        :first-child {
          margin-top: 0px;
        }
  
        .icon {
          width: 24px;
          height: 24px;
          background-size: contain;
        }
  
        .text {
          margin-left: 6px;
          color: #000000d9;
          font-weight: 700;
          font-size: calc(100vw *  14 / 1920);
          letter-spacing: 0;
          line-height: 22px;
          cursor: pointer;
        }
  
        .personal-center {
          background-image: url(../assets/icon_grzx.png);
        }
  
        .my-document {
          background-image: url(../assets/icon_wdwd.png);
        }
  
        .my-favourite {
          background-image: url(../assets/icon_wdsc.png);
        }
  
        .logout {
          background-image: url(../assets/icon_tc.png);
        }
      }
    }
  }
  
  ::v-deep(.el-alert--success.is-light) {
    background: #ebf9f7 !important;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  12 / 1920);
    color: #09873f;
    letter-spacing: 0;
    font-weight: 400;
  }
  
  // 导航项
  .nav-item {
    // width: 272px;
    height: calc(100vh * 40/ 1080);
    background: transparent;
    line-height: 20px;
    white-space: pre-wrap;
    background-image: linear-gradient(270deg,
        rgba(30, 75, 202, 0.39) 0%,
        rgba(59, 130, 234, 0.28) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 600;
    cursor: pointer;
    margin: 0 auto;
  
    &:hover {
      background: #59bce1;
    }
  
    img {
      width: 18px;
      height: 18px;
      float: left;
    }
  
    p {
      float: left;
      margin-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  16 / 1920);
      color: #ffffff;
      letter-spacing: 0;
    }
  }
  
  .choose {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: #59bce1;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 600;
  }
  
  .clbutton {
    // height: ;
    position: absolute;
    height: 30px;
    width: 100px;
    background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    bottom: 40px;
    left: 40px;
  }
  
  .clbutton1 {
    // height: ;
    position: absolute;
    height: 30px;
    width: 100px;
    background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    bottom: 40px;
    left: 160px;
  }
  
  .clbutton2 {
    left: 180px;
  }
  
  .clbutton12 {
    // left: 200px;
    position: absolute;
    height: 30px;
    width: 100px;
    background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    bottom: 40px;
    left: 280px;
  }
  
  .showUp {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: ease-out 0.4s;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: 20px;
    position: absolute;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    bottom: 10px;
    left: 60px;
    height: calc(100vh * 40/ 1080);
    bottom: 20px;
    // z-index: 9999;
    cursor: pointer;
  
    &:hover {
      // background-color: rgba(46,98,245,.1)!important;
      background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
    }
  
    img {
      width: 18px;
      height: 18px;
      float: left;
    }
  
    p {
      float: left;
      margin-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  14 / 1920);
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
    }
  }
  
  .showUp1 {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: ease-out 0.4s;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: 20px;
    position: absolute;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    bottom: 10px;
    left: 6px;
    height: calc(100vh * 40/ 1080);
    bottom: 20px;
    // z-index: 9999;
    cursor: pointer;
  
    &:hover {
      // background-color: rgba(46,98,245,.1)!important;
      background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
    }
  
    img {
      width: 18px;
      height: 18px;
      float: left;
    }
  
    p {
      float: left;
      margin-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  14 / 1920);
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
    }
  }
  
  ::v-deep(.el-loading-spinner) {
    /*这个是自己想设置的 gif 加载动图*/
    background-image: url("../img/icegif-1259.gif");
    background-repeat: no-repeat;
    background-size: 150px 130px;
    height: 100px;
    width: 100%;
    background-position: center;
    /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
    top: 40%;
  }
  
  ::v-deep(.el-loading-spinner .circular) {
    /*隐藏 之前  element-ui  默认的 loading 动画*/
  
    display: none;
  }
  
  ::v-deep(.el-loading-spinner .el-loading-text) {
    /*为了使得文字在loading图下面*/
    margin: 85px 0px;
  }
  
  ::v-deep(.el-step.is-simple .el-step__arrow::before) {
    transform: rotate(-90deg) translateY(-20px) translateX(-16px);
    transform-origin: 0 0;
  }
  
  ::v-deep(.el-drawer__header) {
    display: none;
  }
  
  ::v-deep(.el-step.is-simple .el-step__arrow::after) {
    transform: rotate(60deg) translateY(-0px);
    transform-origin: 100% 100%;
    content: "";
    display: inline-block;
    position: absolute;
    height: 0px;
    width: 0px;
    background: #c0c4cc;
  }
  
  ::v-deep(.el-notification__group) {
    height: auto !important;
  }
  
  ::v-deep(.el-step.is-simple .el-step__arrow::before) {
    content: "";
    display: inline-block;
    position: absolute;
    height: 30px;
    width: 1px;
    background: #c0c4cc;
  }
  
  ::v-deep(.el-step.is-simple .el-step__title) {
    font-size: 14px !important;
  }
  
  ::v-deep(.el-step__title.is-success) {
    color: #1b2126;
    // ::v-deep(.el-step__icon) {
    //   background-color: #1B2126 !important;
    // }
    // border-color: #1B2126;
  }
  
  ::v-deep(.el-step__head.is-success) {
    color: #1b2126;
    border-color: #1b2126;
  }
  
  ::v-deep(.el-step__title.is-process) {
    color: #bbc6d3;
  }
  
  ::v-deep(.el-step__head.is-process) {
    color: #bbc6d3;
    border-color: #bbc6d3;
  }
  
  ::v-deep(.el-steps--simple) {
    background: none !important;
  }
  
  .progressClass {
    width: 100%;
    height: 26px;
    position: fixed;
    bottom: 48px;
    right: 0;
    z-index: 999;
  
    .progress {
      position: absolute;
      right: 52px;
      transition: ease-out 0.4s;
    }
  }
  
  ::v-deep(.el-progress-bar__innerText) {
    padding: 7px !important;
    color: #fff !important;
  }
  
  .menu_label {
    width: 100%;
    border-radius: 4px;
    margin-top: 10px;
    min-height: 158px;
    height: auto;
    padding: 15px 16px;
    // max-height: 158px;
    background: #f8f9fd;
    border: 1px solid rgba(229, 232, 245, 1);
    border-radius: 4px;
    padding: 15px 0px;
  }
  
  .el-tag {
    height: auto;
    white-space: normal !important;
    max-width: 383px;
    word-wrap: break-word;
    text-align: left;
    margin-left: 5px;
    margin-bottom: 5px;
    float: left;
  
  }
  
  .el-main .ai-body .tab-item-fir .menu_label:active {
    background: #fff;
    border: 1px solid #4170f6;
  }
  
  // .el-main .ai-body .tab-item-fir .menu_label:focus {
  //   background: #fff;
  //   border: 1px solid #4170f6;
  // }
  
  .pass_input {
    // float: left;
    width: 100%;
    height: calc(100vh * 40/ 1080);
  
    // margin-left: 10px;
    // el-input__inner是el-input的input类名
    & /deep/ .el-input__inner {
      border: none;
      background-color: rgba(122, 151, 255, 0) !important;
      font-family: PingFangSC-Regular;
      font-size: calc(100vw *  14 / 1920);
      color: #666666;
      letter-spacing: 0;
      font-weight: 400;
    }
  }
  
  ::v-deep(.el-alert__title) {
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  12 / 1920);
    color: #09873f;
    letter-spacing: 0;
    font-weight: 400;
  }
  
  ::v-deep(.el-drawer__open .el-drawer.ltr) {
    width: 100% !important;
  }
  
  ::v-deep(.el-drawer__header) {
    margin-bottom: 0;
  }
  
  ::v-deep(.el-alert) {
    padding: 8px 8px;
  }
  
  .fir-textarea1 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 96.5%;
    background: #f8f9fd;
    border: 1px solid rgba(229, 232, 245, 1);
    border-radius: 4px;
    margin: 20px;
    margin-bottom: 0;
    height: 158px;
  
    ::v-deep(.el-textarea__inner) {
      font-size: 14px !important;
      background-color: #f8f9fd !important;
      height: 100% !important;
      font-family: PingFangSC-Regular;
      padding: 13px 18px 33px 16px;
    }
  }
  
  ::v-deep(.el-collapse) {
    border: 0px solid #ebeef5;
  }
  
  ::v-deep(.el-collapse-item__header.is-active) {
    border-bottom: 1px solid #ebeef5;
  }
  
  ::v-deep(.el-collapse-item__header) {
    background-color: #f9fafe;
    height: calc(100vh * 40/ 1080);
    border-radius: 4px;
  }
  
  ::v-deep(.el-collapse-item__wrap) {
    border-radius: 4px;
  }
  
  ::v-deep(.el-collapse-item__content) {
    background-color: #f9fafe;
    border-radius: 4px;
  }
  
  .fir-textarea-max1 {
    height: 95% !important;
  }
  
  .highlight {
    background-color: yellow;
  }
  
  .result-title {
    position: relative;
  
    &:before {
      position: absolute;
      content: '';
      left: -10px;
      top: 2px;
      width: 4px;
      height: 15px;
      background: #5585F0;
      border-radius: 2px;
    }
  }
  
  // ***滚动条样式
  .result-content::-webkit-scrollbar {
    width: 6px;
    /* 滚动条的宽度 */
  }
  
  .result-content::-webkit-scrollbar-track {
    background: #fff;
    /* 滚动条的背景色 */
  }
  
  .result-content::-webkit-scrollbar-thumb {
    background: #488aff;
    /* 滚动条的滑块颜色 */
  }
  
  .elcol-title-left {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-top: 20px;
    margin-left: 10px;
  }
  
  .elcol-title {
    display: flex;
    overflow: hidden;
    width: 100%;
    height: 48px;
  }
  
  .elcol-title-text {
    // float: left;
    padding-left: 10px;
    text-align: left;
    width: 40%;
    height: 100%;
  }
  
  .elcol-title-text2 {
    font-size: calc(100vw *  14 / 1920);
    color: #d1d7de;
    // float: left;
    width: 75px;
  }
  
  
  
  .elcol-input {
    float: left;
    width: calc(60% - 75px);
    border: none !important;
  }
  
  /* 可以在样式中定义更详细的按钮样式 */
  // .btn-insert{
  //   // background-color: #fff;
  //   background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%); 
  //   color:white !important;
  //   border: 1px solid #ccc;
  //   padding: 5px;
  //   cursor: pointer;
  //   z-index: 1000;
  // }
  // .btn-cancel{
  //   background-image: linear-gradient(107deg, #d65050 0%, #d43d3d 100%); 
  //   color:white !important;
  //   border: 1px solid #ccc;
  //   padding: 5px;
  //   cursor: pointer;
  //   z-index: 1000;
  // }</style>