<!-- 工作总结界面 -->
<template>
    <div class="work-container" v-loading="con_loading">
      <el-drawer title="" :visible.sync="drawer" :direction="direction" style="width: 80%;"
        :before-close="handleClosedrawer">
        <div style="display: flex; height: 94%;">
          <div style="    width: 50%;
      height: calc(100% - 30px);
      margin-top: 20px;">
            <!-- <el-input class="fir-textarea1 fir-textarea-max1" type="textarea" placeholder=""
              v-model="textarea4"></el-input> -->
            <div style="    margin-left: 20px;
              height: 100%;
              overflow: overlay;
              background-color: rgb(255, 255, 255);
              padding: 0px 20px;
              font-size: calc(100vw *  14 / 1920);" v-html="textarea4" contenteditable="true" class="result-content"></div>
          </div>
          <div class="result-container" style="width: calc(50% - 40px);
              height: calc(100% - 30px);
              background-color: rgb(225, 231, 243);
              margin: 20px;padding-top:16px">
            <div class="result-title" style="text-align: left;font-size: calc(100vw *  16 / 1920);margin-bottom: 10px;font-weight: bold;">
              全部校对结果</div>
            <div style="height: 96%; overflow: overlay;" class="result-content">
              <div v-for="(item, index) in correctionList">
                <!-- <template slot="title"> -->
                <!-- <div class="elcol-title">
                <p class="elcol-title-text">{{ item.source }}</p>
                <p class="elcol-title-text2">建议替换</p>
                <p class="elcol-title-text">{{ item.target }}</p> -->
                <!-- <el-input class="elcol-input" v-model="input" placeholder="请输入内容"></el-input> -->
                <!-- </div> -->
                <!-- </template> -->
                <el-collapse v-model="activeNames" @change="highlightError(item.source, index)" style="margin-bottom: 10px;"
                  accordion>
                  <el-collapse-item :name="index">
                    <template slot="title">
                      <div style="width: 96%;">
                        <div class="elcol-title">
                          <div class="elcol-title-left" :style="{ backgroundColor: getBackgroundColor(index) }"></div>
                          <!-- <p class="elcol-title-text">{{ item.source }}</p> -->
                          <el-input class="elcol-input" v-model="item.source" placeholder="请输入内容"></el-input>
                          <p class="elcol-title-text2">建议替换</p>
                          <!-- <p class="elcol-title-text">{{ item.target }}</p> -->
                          <el-input class="elcol-input" v-model="item.target" placeholder="请输入内容"></el-input>
                        </div>
                      </div>
                    </template>
                    <div
                      style="height: calc(100vh * 40/ 1080);text-align: left;padding-left: 20px;line-height: calc(100vh * 40/ 1080);border-bottom: 1px solid #e1e7f3;">
                      拼写：政治语素错误</div>
                    <!-- <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                  <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div> -->
                    <div style="height: 8px;margin-top: 6px;">
                      <span @click="ignore()" style="float: right;margin-right: 10px;color: red;cursor: pointer;">忽略</span>
                      <span @click="highlightChange(item.source, item.target)"
                        style="float: right;margin-right: 10px;color: #66b1ff;cursor: pointer;">替换</span>
                    </div>
  
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div type="primary" class="fr clbutton1" style="margin-right: 20px;cursor: pointer;" @click="save">关 闭</div>
          <!-- <el-button type="danger" @click="fanhui"></el-button> -->
        </div>
      </el-drawer>
      <div :style="navShow ? 'width: 300px' : 'width: 136px'" class="ejdhl">
        <!-- <el-button @click="drawer = true" type="primary" style="margin-left: 16px;">
          点我打开
        </el-button> -->
        <div class="ejdhlTitle">
          <!-- <img src="../assets/logo-11.png" alt="" />
          <p v-if="navShow">思盒</p> -->
          <img src="../img/logo05.png" width="100%" alt="" />
        </div>
        <div class="ai-nav" :style="navShow ? 'width: 300px' : 'width: 64px'">
          <p class="title" v-if="navShow">请选择公文文种</p>
          <div class="nav-list">
            <div class="nav-item" @click="clickTopSzbg" :style="navShow ? 'width: 272px' : 'width: 50px'">
              <img src="../assets/icon01.png" alt="" />
              <p v-if="navShow">述职报告</p>
            </div>
            <div class="nav-item" @click="clickTopXdth" :style="navShow ? 'width: 272px' : 'width: 50px'">
              <img src="../assets/icon03.png" alt="" />
              <p v-if="navShow">心得体会</p>
            </div>
            <div class="nav-item" @click="clickTopLd" :style="navShow ? 'width: 272px' : 'width: 50px'">
              <img src="../assets/icon04.png" alt="" />
              <p v-if="navShow">领导讲话</p>
            </div>
            <div class="nav-item" @click="clickTopFa" :style="navShow ? 'width: 272px' : 'width: 50px'">
              <img src="../assets/icon05.png" alt="" />
              <p v-if="navShow">工作方案</p>
            </div>
            <div class="nav-item" @click="clickTopDy" :style="navShow ? 'width: 272px' : 'width: 50px'">
              <img src="../assets/icon06.png" alt="" />
              <p v-if="navShow">调研报告</p>
            </div>
            <div class="nav-item" @click="clickTopXc" :style="navShow ? 'width: 272px' : 'width: 50px'">
              <img src="../assets/icon07.png" alt="" />
              <p v-if="navShow">宣传材料</p>
            </div>
            <!-- <div class="nav-item" @click="clickTopNav" :style="navShow ? 'width: 272px' : 'width: 50px'">
              <img src="../assets/icon08.png" alt="" />
              <p v-if="navShow">敬请期待</p>
            </div> -->
          </div>
          <div :class="navShow ? 'showUp' : 'showUp1'" :style="navShow ? 'width: 170px' : 'width: 50px'" @click="upShow">
            <img v-if="navShow" src="../assets/icon09.png" alt="" />
            <img v-else src="../assets/img-00.png" alt="" />
            <p v-if="navShow">向左收起</p>
          </div>
        </div>
      </div>
      <el-container>
        <el-header style="transition: ease-out 0.4s" :style="navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
          ">
          <div class="ai-header">
            <div class="ai-bar">
              <div class="ai-left-bar">
                <ul class="ai-left-bar-ul">
                  <li @click="clickTopTy" class="ai-left-bar-li">
                    <img src="../assets/icon15-h.png" alt="" />Ai读
                  </li>
                  <li class="ai-left-bar-li actived">
                    <img src="../assets/icon10.png" alt="" />AI写
                  </li>
                  <!-- <li @click="clickTopNav" class="ai-left-bar-li">
                    <img src="../assets/icon102-h.png" alt="" />
                    AI校对润色
                  </li>
                  <li @click="clickTopNav" class="ai-left-bar-li">
                    <img src="../assets/icon12-h.png" alt="" />笔墨文库
                  </li> -->
                  <li @click="clickTopLg" class="ai-left-bar-li">
                    <img src="../assets/icon13-h.png" alt="" />Ai想
                  </li>
                  <li @click="clickTopJq" class="ai-left-bar-li">
                    <img src="../assets/icon14-h.png" alt="" />Ai听
                  </li>
  
                  <!-- <li @click="clickTopFz" class="ai-left-bar-li">
                    <img src="../assets/icon-jj.png" alt="" />辅助定密
                  </li> -->
                  <li @click="clickTopbmai" class="ai-left-bar-li">
                    <img src="../assets/icon-jj.png" alt="" />助手
                  </li>
                </ul>
              </div>
              <div class="ai-right-bar">
                <div class="top-button" @click="clickTopNav">
                  <!-- <i class="el-icon-s-platform"></i>
                  敬请期待 -->
                  <img src="../assets/icon16.png" alt="" />
                </div>
                <div class="top-button btn" @click="clickTopNav">
                  <!-- <i class="el-icon-s-flag"></i>
                  敬请期待 -->
                  <img src="../assets/icon17.png" alt="" />
                </div>
                <el-dropdown :hide-on-click="false" trigger="click">
                  <div class="ai-avant btn">
                    <img src="../assets/icon18.png" alt="" />
                    <p>{{username}}</p>
                  </div>
                  <el-dropdown-menu slot="dropdown" style="height: 50%;width:20%;margin-right: -50px;">
  
                    <div class="user-menu-content">
                      <div class="user-info">
                        <div class="avatar-wrapper">
                          <img src="../assets/icon_tx.png" alt="" />
                        </div>
                        <div class="name-wrapper">
                          <div class="name">{{username}}</div>
                          <div class="id">ID：001</div>
                        </div>
                      </div>
                      <div class="divider"></div>
                      <div class="options">
                        <a rel="opener" class="option" href="" target="_blank">
                          <div class="icon personal-center"></div>
                          <div class="text">个人中心</div>
                        </a>
                        <a rel="opener" class="option" href="" target="_blank">
                          <div class="icon my-document"></div>
                          <div class="text">我的文档</div>
                        </a>
                        <a rel="opener" class="option" href="" target="_blank">
                          <div class="icon my-favourite"></div>
                          <div class="text">我的收藏</div>
                        </a>
                        <a class="option" @click="logout()">
                          <div class="icon logout"></div>
                          <div class="text" >退出登录</div>
                        </a>
                      </div>
                    </div>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>
        </el-header>
        <el-main style="transition: ease-out 0.4s" :style="navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
          ">
          <div class="ai-gai" v-loading="loading" element-loading-text="大纲生成中，请稍候..."
            element-loading-background="rgba(255, 255, 255, 0.7)" :style="navShow ? 'width: 770px' : 'width: 528px'"
            style="transition: ease-out 0.4s" v-if="mask"></div>
          <!-- <div
            class="ai-gai"
            v-loading="loading"
            element-loading-text="大纲生成中，请稍后..."
            element-loading-background="rgba(255, 255, 255, 0.7)"
            :style="navShow ? 'width: 770px' : 'width: 528px'"
            style="transition: ease-out 0.4s"
          ></div> -->
          <div v-if="maskAll" class="progressClass">
            <el-progress class="progress" :style="navShow
              ? 'width: calc(100% - 870px)'
              : 'width: calc(100% - 637px)'
              " :text-inside="true" :stroke-width="26" :percentage="progressPercent"></el-progress>
          </div>
          <!-- <div class="progressClass">
            <el-progress
              class="progress"
              :style="
                navShow
                  ? 'width: calc(100% - 870px)'
                  : 'width: calc(100% - 637px)'
              "
              style="height: 20px"
              :text-inside="true"
              :stroke-width="26"
              :percentage="progressPercent"
            ></el-progress>
          </div> -->
          <div class="ai-body">
            <div class="ai-body-left">
              <div class="ai-body-left-top">
                <div v-if="jydis">
                  <custom-steps :steps="steps" :current-step="currentStep"></custom-steps>
                  <!-- <el-steps
                  :active="stepActived"
                  finish-status="success"
                  simple
                  class="ai-step"
                >
                  <el-step title="基本信息"></el-step>
                  <el-step title="大纲"></el-step>
                  <el-step title="文章"></el-step>
                </el-steps> -->
                  <div class="ai-tab">
                    <div @click="clickTab('1')" class="tab-item" :class="{ activedTab: curIndex == 1 }">
                      基本信息
                    </div>
                    <div @click="clickTab('2')" class="tab-item" :class="{ activedTab: curIndex == 2 }">
                      AI大纲
                    </div>
                  </div>
                  <div class="tab-item-fir" v-if="curIndex === '1'">
                    <div class="fir-title">
                      <div class="fir-kuai"></div>
                      <p class="fir-title-p">填写工作背景（必填）</p>
                    </div>
                    <el-input class="fir-textarea" type="textarea" placeholder="参考：国企办公室" v-model="textarea"
                      @focus="textareaFocus" show-word-limit>
                    </el-input>
  
                    <div class="ai-dialog" v-if="dialogShow">
                      <div class="ai-d-title">
                        <p class="ai-d-title-p">
                            请在工作背景部分填写部门及岗位信息或某专项工作主题，建议您参考或直接选择推荐词汇，生成效果更佳
                        </p>
                        <img src="../assets/close.png" @click="closeDialog" alt="" />
                      </div>
                      <div class="ai-d-body">
                        <div class="hints-control">
                          <div class="hint-icon hot"></div>
                          <div class="hint-description">热门推荐</div>
                        </div>
                        <div class="ai-tj-body">
                          <!-- {{ item }} -->
                          <!-- <p @click="getText" class="ai-tj-item">
                          市政府办公厅主任
                        </p>
                        <p @click="getText" class="ai-tj-item">市委宣传部部长</p>
                        <p @click="getText" class="ai-tj-item">市委政法委书记</p>
                        <p @click="getText" class="ai-tj-item">市网信办主任</p> -->
                          <p @click="getText(item)" class="ai-tj-item" v-for="(item, index) in gzbj" :key="index">
                            {{ item }}
                          </p>
                        </div>
                      </div>
                    </div>
  
                    <div class="fir-title">
                      <div class="fir-kuai"></div>
                      <p class="fir-title-p">填写工作要点关键词（必填）</p>
                    </div>
                    <!-- <el-input
                    class="fir-textarea"
                    type="textarea"
                    placeholder="参考：市政府办公厅"
                    v-model="textarea2"
                    maxlength="100"
                    @focus="textarea2Focus"
                    show-word-limit
                  >
                  </el-input> -->
                    <div class="menu_label" @click="textarea2Focus">
                      <el-tag :key="tag" type="info" v-for="tag in dynamicTags" closable :disable-transitions="false"
                        @close="handleClose(tag)">
                        {{ tag }}
                      </el-tag>
                      <el-input class="input-new-tag pass_input" v-if="inputVisible" v-model="inputValue" ref="saveTagInput"
                        size="small" placeholder="参考：公文管理、会议筹办、信息宣传、信访维稳" @keyup.enter.native="handleInputConfirm"
                        @blur="handleInputConfirm">
                      </el-input>
                    </div>
  
                    <div class="ai-dialog" v-if="dialog2Show">
                      <div class="ai-d-title">
                        <p class="ai-d-title-p">
                          请在关键词部分填写工作要点，建议您直接选择或参考推荐词汇，生成效果更佳
                        </p>
                        <img src="../assets/close.png" @click="closeDialog2" alt="" />
                      </div>
                      <div class="ai-d-body">
                        <div class="hints-control">
                          <div class="hint-icon hot"></div>
                          <div class="hint-description">热门推荐</div>
                        </div>
                        <div class="ai-tj-body">
                          <p v-for="(item, index) in secRecommend" :key="index" @click="getText2(item)" class="ai-tj-item"
                            :title="item">
                            {{ item }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="tab-item-fir" v-if="curIndex === '2'">
                    <div class="fir-title">
                      <div class="fir-kuai"></div>
                      <p class="fir-title-p">大纲</p>
                    </div>
                    <el-alert class="fir-alert" title="根据自己的需要修改大纲，这样生成的报告更加精准哦~" type="success">
                    </el-alert>
                    <el-input class="fir-textarea fir-textarea-height" type="textarea" placeholder="参考：市政府办公厅"
                      v-model="textarea3">
                    </el-input>
                  </div>
                </div>
                <div class="tab-item-fir" v-else>
                  <div style="height: 100%; overflow: overlay; margin-top: 20px" class="result-content">
                    <div v-for="(item, index) in correctionList">
                      <!-- <template slot="title"> -->
                      <!-- <div class="elcol-title">
                <p class="elcol-title-text">{{ item.source }}</p>
                <p class="elcol-title-text2">建议替换</p>
                <p class="elcol-title-text">{{ item.target }}</p> -->
                      <!-- <el-input class="elcol-input" v-model="input" placeholder="请输入内容"></el-input> -->
                      <!-- </div> -->
                      <!-- </template> -->
                      <el-collapse v-model="activeNames" @change="highlightError(item.source, item.wordNo)"
                        style="margin-bottom: 10px;" accordion>
                        <el-collapse-item :name="index">
                          <template slot="title">
                            <div style="width: 96%;">
                              <div class="elcol-title" style="display: flex;">
                                <div class="elcol-title-left" :style="{ backgroundColor: getBackgroundColor(index) }">
                                </div>
                                <p class="elcol-title-text elcol-input">{{ item.wordNo }}</p>
                                <!-- <el-input :title="item.wordNo" class="elcol-input" v-model="item.wordNo"
                                  placeholder="请输入内容" style="width: 40%;"></el-input> -->
                                <p class="elcol-title-text2">建议替换</p>
                                <!-- <p class="elcol-title-text">{{ item.target }}</p> -->
                                <el-input :title="item.wordYes" class="elcol-input" v-model="item.wordYes"
                                  placeholder="请输入内容" style="width: 40%;"></el-input>
                              </div>
                            </div>
                          </template>
                          <div
                            style="height: calc(100vh * 40/ 1080);text-align: left;padding-left: 20px;line-height: calc(100vh * 40/ 1080);border-bottom: 1px solid #e1e7f3;">
                            {{ item.eq }}</div>
                          <!-- <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                  <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div> -->
                          <div style="height: 8px;margin-top: 6px;">
                            <span @click="ignore()"
                              style="float: right;margin-right: 10px;color: red;cursor: pointer;">忽略</span>
                            <span @click="highlightChange(item.source, item.target)"
                              style="float: right;margin-right: 10px;color: #66b1ff;cursor: pointer;">替换</span>
                          </div>
  
                        </el-collapse-item>
                      </el-collapse>
                    </div>
                  </div>
                  <div type="primary" class="clbutton2 clbutton1" style="margin-right: 20px;cursor: pointer;" @click="save">
                    关
                    闭</div>
                </div>
              </div>
              <div class="ai-body-left-bottom" v-if="jydis == true">
                <div v-if="curIndex == '1'" @click="firstNextStep" class="ai-body-left-bottom-button">
                  下一步：生成大纲
                </div>
                <div v-if="curIndex == '2'" class="ai-body-left-bottom2">
                  <div class="repeat" @click="repeatStep">重新生成大纲</div>
                  <div @click="success" class="ai-body-left-bottom-button">
                    <span v-if="buttonShow">下一步：生成文章</span>
                    <span v-else>重新生成文章</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="ai-body-right">
              <div class="ai-body-start" v-if="!artShow">
                <!-- <div class="ai-body-start" v-if="false"> -->
                <!-- <div class="pic_bkg">
                  <div class="pic_font">欢迎使用AI写作</div>
                  <div class="title_message">
                    采用AI大模型智能生成文章，仅需3步即可一键成文，快去试试吧~
                  </div>
                </div>
  
                <div class="pic_step"></div> -->
                <div class="pic_bkg1"></div>
              </div>
  
              <div class="ai-body-art" v-else>
                <!-- <el-input class="fir-textarea fir-textarea-max" type="textarea" placeholder="" v-model="textarea4">
                  <template #prepend>{{ textarea4 }}</template>
  
                </el-input> -->
                <vue-editor class="fir-textarea fir-textarea-max" v-model="textarea4" ref="editor"></vue-editor>
                <div style="cursor: pointer;" v-if="progressPercent == 100" class="clbutton" v-clipboard:copy="textarea4"
                  v-clipboard:success="onCopySuccess" v-clipboard:error="onCopyError">
                  复制全文
                </div>
                <div style="cursor: pointer;" v-if="progressPercent == 100" class="clbutton1" @click="jiaoyan">
                  校验
                </div>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
    </div>
  </template>
  <script>
  import { VueEditor } from "vue2-editor";
  import CustomSteps from "../components/Step.vue";
import store from '../store/index'

  import {
    getSecDtDatas,
    getSuccessInfo,
    getSuccessInfoItem,
    getLabel1,
    getLabel2,
    testcheck,
  } from "../api/home.js"; // 接口请求
  export default {
    data() {
      return {
        con_loading: false,
        correctionList: [],
        jydis: true,
        navShow: true,
        textarea: "",
        textarea2: [],
        textarea3: "",
        textarea4: "",
        textarea4: "",
        dialogShow: false,
        dialog2Show: false,
        gzbj: [],
        stepActived: 1,
        loading: false,
        // loading: true,
        curIndex: "1",
        articles: "",
        artShow: false,
        mask: false,
        maskAll: false,
        /**
         * @description:  功能性按钮
         * @return {*}    传入组件->FunctionTable
         */
        steps: [{ title: "基本信息" }, { title: "大纲" }, { title: "文章" }],
        currentStep: 1, // 初始化当前步骤为第二个步骤
        secRecommend: [], // 第二步推荐
        dynamicTags: [],
        inputVisible: true,
        inputValue: "",
        resDataItem: [],
        progressPercent: 0,
        fetchResPrams: [],
        buttonShow: true,
        drawer: false,
        direction: 'ltr',
        activeNames: []
        // asyncStatus: true
      };
    },
    components: {
      // 注册组件
      CustomSteps,
      VueEditor
    },
    mounted() {
      // this.getInfo()
    },
           methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
     togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
      wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
      logout() {
      clearVuexAlong()
      store.commit('addNewToken', '')
      this.$router.push("/login");

    },
      //按钮颜色
      getBackgroundColor(index) {
        const colors = ['#ff6403', '#fbff00', '#01fffe', '#e958ea'];
        // 使用取余运算符来循环数组中的颜色
        return colors[index % colors.length];
      },
      ignore() {
        this.textarea4 = this.removetext(this.textarea4)
        this.activeNames = []
      },
      highlightChange(text1, text2) {
        console.log(this.textarea4);
        let changeData = text2
        // 高亮显示文章中的错误文字
        // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
        // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
        this.textarea4 = this.removetext(this.textarea4)
        this.textarea4 = this.textarea4.replace(text1, changeData);
      },
      highlightError(error, word) {
        this.textarea4 = this.removetext(this.textarea4)
        let a = error.replace(word, `<span class="highlight" style="background-color:yellow!important;">${word}</span>`);
        this.textarea4 = this.textarea4.replace(error, a);
        console.log(this.textarea4);
        this.$nextTick(() => {
          // 确保 DOM 更新完成后，滚动到高亮文字
          this.scrollToHighlight();
        });
      },
      scrollToHighlight() {
        // 获取 vue-editor 中的 iframe
        const editorIframe = this.$refs.editor.$el.querySelector('div.ql-editor');
        console.log(this.$refs.editor.$el, 'editorIframe');
        if (editorIframe) {
          const highlightedElement = editorIframe.querySelector('span.highlight');
          console.log(highlightedElement, 'highlightedElement');
          if (highlightedElement) {
            // 滚动到第一个高亮文字
            highlightedElement.scrollIntoView({ behavior: "smooth", block: "center" });
          }
        }
      },
      removetext(text) {
        let str = text
        let reg1 = new RegExp(`<span class="highlight" style="background-color:yellow!important;">`, 'g');
        let a1 = str.replace(reg1, '');
        let reg2 = new RegExp(`</span>`, 'g');
        text = a1.replace(reg2, '');
        return text
      },
      removetextp(text) {
        let str = text
        let reg1 = new RegExp(`<p>`, 'g');
        let a1 = str.replace(reg1, '');
        let reg2 = new RegExp(`</p>`, 'g');
        text = a1.replace(reg2, '');
        return text
      },
      onCopySuccess() {
        this.$message({
          message: "内容已复制到剪贴板！",
          type: "success",
        });
      },
      onCopyError() {
        this.$message({
          message: "复制失败，请手动复制!",
          type: "warning",
        });
      },
      handleInputConfirm() {
        let inputValue = this.inputValue;
        if (inputValue) {
          this.dynamicTags.push(inputValue);
          // 
          this.textarea2 = this.dynamicTags
        }
        // this.inputVisible = false;
        this.inputValue = "";
      },
      async jiaoyan() {
        this.textarea4 = this.removetext(this.textarea4)
        this.con_loading = true;
        // let text = this.removetextp(this.textarea4)
        testcheck({
          test_all: this.textarea4,
        }).then(async (data) => {
          this.correctionList = data.data;
          this.con_loading = false;
          if (this.correctionList.length == 0) {
            this.jydis = true
            this.$message({
              message: '不存在文字错误',
              type: 'warning'
            });
          } else {
            console.log(data, "619");
            this.jydis = false
          }
        });
  
      },
      handleClosedrawer() {
        this.textarea4 = this.removetext(this.textarea4)
        this.activeNames = []
        this.drawer = false
      },
      // async jiaoyan(){
      //   this.jydis = false
      // },
      save() {
        this.textarea4 = this.removetext(this.textarea4)
        this.activeNames = []
        this.jydis = true
      },
      fanhui() {
        this.textarea4 = this.removetext(this.textarea4)
        this.activeNames = []
        this.drawer = false
      },
      handleClose(tag) {
        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      },
      handleStepClick(index) {
        this.lock++;
        this.activeIndex = index; // 更新activeIndex的值
      },
      upShow() {
        this.navShow = !this.navShow;
      },
      async textareaFocus() {
        let data = await getLabel1();
        this.gzbj = data.data;
        this.dialogShow = true;
      },
      closeDialog() {
        this.dialogShow = false;
      },
      textarea2Focus() {
        // if (this.textarea == "市政府办公厅主任") {
        //   this.secRecommend = [
        //     { id: 1, title: "负责市政府办公厅日常管理" },
        //     { id: 2, title: "组织协调市政府办公厅工作" },
        //     { id: 3, title: "研究决定办公厅重大事项" },
        //     { id: 4, title: "协助市长处理办公厅相关事务" },
        //   ];
        // } else if (this.textarea == "市委宣传部部长") {
        //   this.secRecommend = [
        //     { id: 1, title: "制定宣传工作总体规划" },
        //     { id: 2, title: "指导媒体宣传工作" },
        //     { id: 3, title: "组织重大新闻发布" },
        //     { id: 4, title: "策划重大宣传活动" },
        //   ];
        // } else if (this.textarea == "市委政法委书记") {
        //   this.secRecommend = [
        //     { id: 1, title: "研究制定政法工作规划" },
        //     { id: 2, title: "领导政法工作会议" },
        //     { id: 3, title: "指导政法系统改革" },
        //     { id: 4, title: "协调处理重大政法问题" },
        //   ];
        // } else if (this.textarea == "市网信办主任") {
        //   this.secRecommend = [
        //     { id: 1, title: "落实上级部署" },
        //     { id: 2, title: "制定工作计划" },
        //     { id: 3, title: "监督网络安全" },
        //     { id: 4, title: "推动信息化建设" },
        //   ];
        // } else {
        //   this.secRecommend = [];
        // }
        this.dialog2Show = true;
      },
      closeDialog2() {
        this.dialog2Show = false;
      },
      async getText(e) {
        console.log(e);
        let params = {
          label: e,
        };
        let data = await getLabel2(params);
        console.log(data, "583");
        this.secRecommend = data.data;
        this.textarea = e;
        this.textarea2 = [];
      },
      getText2(e) {
        if (!this.dynamicTags.includes(e)) {
          this.textarea2.push(e);
          this.dynamicTags.push(e);
          // this.textarea = e.srcElement.innerText;
          this.handleInputConfirm(e);
        } else {
          console.log(111);
        }
  
      },
      clickTopNav() {
        this.$notify({
          title: "提示",
          message: "暂未开发，敬请期待！",
          type: "warning",
        });
      },
      clickszbg() {
        // const picBkg1 = document.getElementsByClassName('.pic_bkg1')
        //   picBkg1.style.background = 'url(../assets/img-bg1.png) no-repeat center';
  
      },
      clickTopXdth() {
        this.$router.push("/xdth");
        // let parentElement = document.getElementById('parent-div');
      },
    //   述职报告
    clickTopSzbg() {
        this.$router.push("/index");
      },
      clickTopLg() {
        this.$router.push("/lgsc");
      },
      clickTopJq() {
        this.$router.push("/jqkz");
      },
      clickTopTy() {
        this.$router.push("/tywzsb");
      },
      clickTopFz() {
        this.$router.push("/Fzdm");
      },
      clickTopbmai() {
        this.$router.push("/bmai");
      },
          // 工作总结
          clickTopGz() {
        this.$router.push("/gzzj");
      },
      // 领导讲话
      clickTopLd(){
          this.$router.push("/ldjh");
        
      },
      // 工作方案
      clickTopFa(){
          this.$router.push("/gzfa");
        
      },
      // 调研报告
      clickTopDy(){
          this.$router.push("/dybg");
        
      },
      // 宣传材料
      clickTopXc(){
          this.$router.push("/xccl");
        
      },
      firstNextStep() {
        if (this.textarea == "") {
          this.$notify({
            title: "提示",
            message: "请填写工作背景",
            type: "warning",
          });
        } else if (this.textarea2.length == 0) {
          this.$notify({
            title: "提示",
            message: "请填写工作要点关键词",
            type: "warning",
          });
        } else {
          this.mask = true;
          if (this.mask == true) {
            this.getInfo(this.textarea, this.dynamicTags);
          }
        }
      },
      async getInfo(c1, c2) {
        this.loading = true;
        let params = {
          test_1: c1,
          test_2: c2,
        };
        let res = await getSecDtDatas(params);
        if (res.status == 200) {
          this.textarea3 = res.data;
          // 这里放置需要执行的逻辑或调用其他方法
          this.buttonShow = true;
          // this.stepActived = 2;
          this.currentStep = 2;
          this.loading = false;
          this.mask = false;
          this.curIndex = "2";
        }
      },
      repeatStep() {
        this.loading = true;
        this.mask = true;
        if (this.mask == true) {
          this.getInfo(this.textarea, this.dynamicTags);
        }
      },
      success() {
        this.artShow = true;
        this.loading = true;
        // 这里放置需要执行的逻辑或调用其他方法
        if (this.textarea3 == "") {
          this.$notify({
            title: "提示",
            message: "大纲内容为空，无法生成",
            type: "warning",
          });
        } else {
          this.progressPercent = 0;
          this.getSuccessInfo(this.textarea3, this.textarea);
        }
      },
      // 依次请求接口
      async fetchResData(index, nextIndex) {
        if (index >= this.fetchResPrams.length) {
          this.fetchData(nextIndex);
          return;
        }
        console.log(this.fetchResPrams[index], "675");
        let pams = {
          string: this.fetchResPrams[index],
          id_name: this.textarea,
        };
        const response = await getSuccessInfoItem(pams);
        this.textarea4 += response.data;
        let totalLength = 0;
        let allSubArrays = this.resDataItem.filter(
          (item) => typeof item != "string"
        );
        allSubArrays.forEach((subArray) => {
          totalLength += subArray.length;
        });
        let kuai = 100 / totalLength;
        this.progressPercent += Math.floor(kuai);
        await this.fetchResData(index + 1, nextIndex);
      },
      // 一段式生成
      async fetchData(index) {
        if (index >= this.resDataItem.length) {
          this.progressPercent = 100;
          this.maskAll = false;
          this.buttonShow = false;
          return;
        }
        if (typeof this.resDataItem[index] == "string") {
          this.textarea4 += this.resDataItem[index];
          await this.fetchData(index + 1);
        } else {
          this.fetchResPrams = this.resDataItem[index];
          this.fetchResData(0, index + 1);
        }
        // this.stepActived = 3;
        this.currentStep = 3;
        this.loading = false;
      },
      async getSuccessInfo(c1, c2) {
        this.loading = true;
        this.maskAll = true;
        let params = {
          string: c1,
          work: c2,
        };
        let res = await getSuccessInfo(params);
        if (res.status == 200) {
          this.textarea4 = "";
          this.resDataItem = res.data;
          this.fetchData(0);
          this.currentStep = 3;
          // this.stepActived = 3;
          this.loading = false;
        }
      },
      clickTab(i) {
        if (this.currentStep != 2) {
          this.$notify({
            title: "提示",
            message: "请先输入工作背景和工作要点生成大纲",
            type: "warning",
          });
        } else {
          this.curIndex = i;
        }
      },
    },
  };
  </script>
  <style lang="less" scoped>
  .fr {
    float: right
  }
  
  .work-container {
    height: 100%;
  
    .result-container {
      padding: 30px;
  
      .elcol-title {
        display: flex;
        overflow: hidden;
        width: 100%;
        height: 48px;
  
        .elcol-title-text {
          // float: left;
          padding-left: 10px;
          text-align: left;
          width: 40%;
          height: 100%;
        }
  
        .elcol-title-text2 {
          font-size: calc(100vw *  14 / 1920);
          // color: #d1d7de;
          // float: left;
          width: 75px;
        }
  
  
  
        .elcol-input {
          float: left;
          width: calc(60% - 75px);
          border: none !important;
        }
      }
    }
  }
  
  .elcol-input ::v-deep(.el-input__inner) {
    border: none !important;
    background-color: #f9fafe;
    height: 20px;
  }
  
  .elcol-input::v-deep(.el-input__inner) {
    overflow: hidden;
    /* 隐藏超出部分 */
    text-overflow: ellipsis;
    /* 超出部分显示省略号 */
    white-space: nowrap;
    /* 不换行 */
  }
  
  
  .elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
    width: 43% !important;
  }
  
  .ejdhl {
    height: 100%;
    // height: 1080px;
    background: url(../assets/img-left.png) no-repeat center;
    background-size: cover;
    background-position: center;
    transition: ease-out 0.4s;
  }
  
  .ejdhlTitle {
    height: calc(100vh * 80 / 1080);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: ease-out 0.4s;
    background-image: linear-gradient(76deg,
        #07389c 0%,
        #3d86d1 0%,
        #3448b3 100%);
  
    img {
      width: 120px;
      height: 48px;
    }
  
    p {
      border: 1.54px solid rgba(0, 0, 0, 0);
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  24 / 1920);
      color: #000000;
      text-align: left;
      color: #fff;
      display: flex;
      align-items: center;
      margin-left: 5px;
    }
  }
  
  .el-header {
    // width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: calc(100vh * 80 / 1080) !important;
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
    position: fixed;
    top: 0;
    right: 0;
    z-index: 20;
    width: calc(100% - 300px);
  
    .ai-header {
      width: 100%;
  
      .ai-bar {
        width: calc(100% - 300px);
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        height: calc(100vh * 80 / 1080);
        background-color: #fff;
  
        .ai-left-bar {
          display: flex;
          justify-content: center;
          align-items: center;
          column-gap: 40px;
          height: calc(100vh * 40/ 1080);
          border-radius: 20px;
          background: #f4f6f8;
          border: 1px solid #e6e6e6;
  
          .ai-left-bar-ul {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            height: 100%;
  
            .ai-left-bar-li {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 42px;
              width: 130px;
              color: #000;
              font-size: calc(100vw *  14 / 1920);
              line-height: 16px;
              white-space: nowrap;
              cursor: pointer;
              z-index: 9999;
              font-family: PingFangSC-Semibold;
              font-size: calc(100vw *  14 / 1920);
              color: #000000;
              letter-spacing: 0;
              text-align: center;
  
              img {
                width: 16px;
                height: 16px;
                margin-right: 14px;
              }
  
              &:hover {
                background-image: linear-gradient(107deg,
                    #3a6bc6 0%,
                    #488aff 100%);
                box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                border-radius: 20px;
                color: #fff;
              }
            }
  
            .actived {
              background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: 20px;
              color: #fff;
            }
          }
        }
  
        .ai-right-bar {
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
          align-items: center;
          position: fixed;
          right: 93px;
          height: calc(100vh * 80 / 1080);
          // column-gap: 16px;
          // height: 100%;
          // margin-left: 30px;
  
          .top-button {
            display: flex;
            justify-content: center;
            align-items: center;
  
            img {
              width: 16px;
              height: 16px;
            }
          }
  
          .btn {
            margin-left: 30px;
          }
  
          .el-dropdown-link {
            cursor: pointer;
            color: #409eff;
          }
  
          .el-icon-arrow-down {
            font-size: calc(100vw *  12 / 1920);
          }
  
          .ai-avant {
            overflow: hidden;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
  
            img {
              width: 28px;
              height: 28px;
              float: left;
            }
  
            p {
              float: left;
              margin-left: 22px;
              margin-top: -2px;
              display: flex;
              justify-content: center;
              align-items: center;
              font-family: SourceHanSansSC-Regular;
              font-size: calc(100vw *  14 / 1920);
              color: #000000;
              letter-spacing: 0;
              font-weight: 400;
            }
          }
        }
      }
    }
  }
  
  .el-main {
    background-color: #f8f9fd;
    color: #333;
    text-align: center;
    // line-height: 160px;
    width: calc(100% - 300px);
    height: calc(100% - calc(100vh * 80 / 1080));
    position: absolute;
    right: 0;
    top: calc(100vh * 80 /1080);
    padding: 20px 10px;
    // overflow: hidden;
  
    .ai-gai {
      position: fixed;
      /* top: 0; */
      left: 0;
      bottom: 0;
      height: 91%;
      // background: rgba(122, 151, 255, 0.6);
      z-index: 999;
    }
  
    .ai-body {
      transition: ease-out 0.4s;
      width: 100%;
      height: 100%;
      // background: red;
      float: left;
      overflow: hidden;
  
      .ai-body-left {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        width: 444px;
        height: 100%;
        background: #ffffff;
        border-radius: 8px;
        overflow-x: hidden;
        overflow-y: auto;
        margin-left: 10px;
        float: left;
  
        .ai-body-left-top {
          width: 100%;
          height: calc(100% - calc(100vh * 80 / 1080));
          overflow-y: scroll;
        }
  
        .ai-body-left-bottom {
          width: 100%;
          height: 90px;
  
          // padding-top: 30px;
          .ai-body-left-bottom-button {
            height: calc(100vh * 40/ 1080);
            // font-weight: 600;
            font-size: calc(100vw *  14 / 1920);
            letter-spacing: 0px;
            flex-grow: 1;
            color: #fff;
            margin: 0 25px;
            cursor: pointer;
            line-height: calc(100vh * 40/ 1080);
            margin-top: 30px;
            background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            font-family: PingFangSC-Regular;
          }
        }
  
        .ai-body-left-bottom2 {
          width: 100%;
          height: calc(100vh * 80 / 1080);
          padding-top: 30px;
  
          .repeat {
            width: 188px;
            float: left;
            height: calc(100vh * 40/ 1080);
            font-size: calc(100vw *  14 / 1920);
            flex-grow: 1;
            margin-left: 25px;
            cursor: pointer;
            line-height: calc(100vh * 40/ 1080);
            background: #f4f6f8;
            border: 1px solid rgba(230, 230, 230, 1);
            border-radius: 20px;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  14 / 1920);
            color: #666666;
            letter-spacing: 0;
            text-align: center;
            // font-weight: 600;
          }
  
          .ai-body-left-bottom-button {
            width: 188px;
            float: left;
            height: calc(100vh * 40/ 1080);
            flex-grow: 1;
            margin-left: 14px;
            cursor: pointer;
            line-height: calc(100vh * 40/ 1080);
            background-image: linear-gradient(102deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            margin-top: 0px;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  14 / 1920);
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
            // font-weight: 600;
          }
        }
  
        .ai-step {
          margin-top: 20px;
          width: calc(100% - 40px);
          margin: 0 20px;
          margin-top: 5px;
          height: calc(100vh * 40/ 1080);
        }
      }
  
      .ai-body-right {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        width: calc(100% - 480px);
        height: 100%;
        background: #ffffff;
        border-radius: 8px;
        overflow-x: hidden;
        overflow-y: auto;
        margin-left: 20px;
        float: left;
  
        .ai-body-start {
          width: 100%;
          height: 100%;
  
          .pic_bkg1 {
            // width: calc(100vw * 670 / 1920);
            // height: calc(100vw * 590 / 1920);
            width: 670px;
            height: 590px;
            background: url(../assets/img-bg1.png) no-repeat center;
            // background: url(../assets/img-bg1.png) no-repeat center;
  
            background-size: 100% 100%;
            margin: 0 auto;
            margin-top: 70px;
            position: relative;
          }
  
          .pic_bkg {
            width: 528px;
            height: 518px;
            // z-index: 15;
            background: url(../assets/img-ai.png) no-repeat center;
            background-size: 100% 100%;
            margin: 0 auto;
            margin-top: 118px;
            position: relative;
          }
  
          .pic_font {
            position: absolute;
            margin-left: auto;
            margin-right: auto;
            left: 0;
            right: 0;
            bottom: 116px;
            width: 255px;
            height: calc(100vh * 40/ 1080);
            border: 1.54px solid rgba(0, 0, 0, 0);
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  28 / 1920);
            color: #000000;
            text-align: center;
            font-weight: 600;
          }
  
          .title_message {
            position: absolute;
            margin-left: auto;
            margin-right: auto;
            left: 0;
            right: 0;
            bottom: 82px;
            text-align: center;
            line-height: 16px;
            margin-top: 10px;
            height: 22px;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #999999;
            letter-spacing: 0;
            font-weight: 400;
          }
  
          .pic_step {
            width: 551px;
            height: 142px;
            z-index: 15;
            background: url("../assets/pic_step.png");
            background-size: contain;
            margin: auto;
          }
        }
  
        .ai-body-art {
          width: 100%;
          height: 100%;
          overflow: hidden;
          position: relative;
  
          .fir-textarea {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 96.5%;
            // background: #f8f9fd;
            border: 1px solid rgba(229, 232, 245, 1);
            border-radius: 4px;
            margin: 20px;
            margin-bottom: 0;
            height: 158px;
  
            ::v-deep(.el-textarea__inner) {
              font-size: 14px !important;
              background-color: #f8f9fd !important;
              height: 100% !important;
              font-family: PingFangSC-Regular;
              padding: 13px 18px 33px 16px;
            }
          }
  
          .fir-textarea-max {
            height: 95% !important;
          }
  
          ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
            display: none !important;
          }
  
          ::v-deep(.ql-blank) {
            display: none !important;
          }
  
          ::v-deep(.ql-container.ql-snow) {
            border: 0;
          }
        }
      }
  
      .ai-tab {
        width: 230px;
        height: calc(100vh * 40/ 1080);
        margin: 0 auto;
        margin-top: 30px;
        background: #f4f6f8;
        border: 1px solid #eeeff0;
        border-radius: 20px;
  
        .tab-item {
          width: 50%;
          height: calc(100vh * 40/ 1080);
          line-height: 16px;
          float: left;
          line-height: calc(100vh * 40/ 1080);
          cursor: pointer;
  
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #9094a5;
          letter-spacing: 0;
          text-align: center;
        }
  
        .activedTab {
          border-radius: 20px;
  
          background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          color: #ffffff;
        }
      }
  
      .tab-item-fir {
        width: 100%;
        height: 536px;
        padding: 0 25px;
  
        .fir-title {
          color: #222;
          font-weight: 500;
          font-size: calc(100vw *  14 / 1920);
          letter-spacing: 0;
          line-height: 16px;
          overflow: hidden;
          margin-top: 20px;
          margin-bottom: 20px;
          height: 20px;
  
          .fir-kuai {
            width: 6px;
            height: 16px;
            margin-right: 8px;
            float: left;
            // margin-top: 2px;
            background: #4081ff;
            border-radius: 1.5px;
          }
  
          .fir-title-p {
            line-height: 16px;
            float: left;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #333333;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
  
        .fir-alert {
          margin-top: 10px;
          height: 35px;
        }
  
        .ai-dialog {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          row-gap: 1px;
          width: 100%;
          height: -moz-fit-content;
          height: 290px;
          max-height: 294px;
          padding: 7px;
          box-shadow: 0 20px 40px 4px #e4e4e524;
          margin-top: 10px;
          transition: ease-out 0.4s;
          background: #ace9ff;
          border: 1px solid rgba(90, 206, 255, 1);
          border-radius: 4px;
  
          .ai-d-title {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: flex-start;
            width: 100%;
            height: -moz-fit-content;
            height: fit-content;
            margin: 0;
            padding: 1px 3px 2px 2px;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #313733;
            letter-spacing: 0;
            font-weight: 400;
  
            .ai-d-title-p {
              flex-grow: 1;
              line-height: 16px;
              text-align: left;
              font-family: PingFangSC-Regular;
              font-size: calc(100vw *  14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 400;
              margin-bottom: 10px;
            }
  
            img {
              width: 18px;
              height: 18px;
              cursor: pointer;
            }
          }
  
          .ai-d-body {
            width: 100%;
            height: calc(100% - 44px);
            overflow: hidden;
            background: #ffffff;
            border-radius: 4px;
  
            .hints-control {
              display: flex;
              flex-direction: row;
              justify-content: flex-start;
              align-items: center;
              width: 100%;
              padding: 14px 14px 0;
              height: 30px;
  
  
              .hint-icon {
                flex-grow: 0;
                flex-shrink: 0;
                width: 20px;
                height: 20px;
                margin-right: 6px;
                background-size: contain;
                background-image: url("../assets/icon_fire.png");
              }
  
              .hint-description {
                font-weight: 600;
                line-height: 14px;
                font-family: SourceHanSansSC-Bold;
                font-size: calc(100vw *  14 / 1920);
                color: #313733;
                letter-spacing: 0;
                font-weight: 700;
              }
            }
  
            .ai-tj-body {
              width: 100%;
              // height: 100%;
              // overflow: hidden;
              height: 200px;
              overflow-y: auto;
  
              /* 垂直滚动条 */
              .ai-tj-item {
                padding: 14px 14px 0;
                line-height: 12px;
                width: 50%;
                text-align: left;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                float: left;
                cursor: pointer;
                height: 30px;
                font-family: PingFangSC-Regular;
                font-size: calc(100vw *  14 / 1920);
                color: #313733;
                letter-spacing: 0;
                font-weight: 400;
                // &:hover {
  
                // }
              }
            }
          }
        }
  
        // ***滚动条样式
        .ai-tj-body::-webkit-scrollbar {
          width: 6px;
          /* 滚动条的宽度 */
        }
  
        .ai-tj-body::-webkit-scrollbar-track {
          background: #fff;
          /* 滚动条的背景色 */
        }
  
        .ai-tj-body::-webkit-scrollbar-thumb {
          background: #488aff;
          /* 滚动条的滑块颜色 */
        }
  
        .fir-textarea {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          margin-top: 14px;
          height: 158px;
          background: #f8f9fd;
          // border: 1px solid rgba(229, 232, 245, 1);
          border-radius: 4px;
  
          ::v-deep(.el-textarea__inner) {
            font-size: 14px !important;
            background-color: #f8f9fd !important;
            height: 100% !important;
            font-family: PingFangSC-Regular;
          }
        }
  
        .fir-textarea-height {
          height: 460px !important;
        }
      }
    }
  }
  
  ::v-deep(.el-textarea__inner) {
    padding: 16px;
  }
  
  ::v-deep(.el-textarea .el-input__count) {
    color: #909399;
    background: none;
    position: absolute;
    font-size: calc(100vw *  12 / 1920);
    right: 10px;
    height: 10px;
  }
  
  .ai-nav {
    // width: 180px;
    // height: 100%;
    height: calc(100% - calc(100vh * 80 / 1080));
    background-position: left;
    background-repeat: no-repeat;
    background-size: cover;
    transition: ease-out 0.4s;
  
    .title {
      width: 300px;
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  16 / 1920);
      color: #ffffff;
      letter-spacing: 0;
      letter-spacing: 0px;
      height: 20px;
      line-height: 20px;
      padding-top: 20px;
    }
  
    .nav-list {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      width: 100%;
      padding-top: 45px;
      height: calc(100% - calc(100vh * 80 / 1080));
    }
  }
  
  .user-menu-content {
    width: calc(100vw *  246 / 1920);
    height: 24px;
    padding: 0 20px;
    background: #ffffff;
  
    .user-info {
      display: flex;
      align-items: center;
      width: 100%;
      margin-top: 17px;
  
      .avatar-wrapper {
        position: relative;
        width: 52px;
        height: 52px;
        border: 1px solid rgba(122, 151, 255, 0.6);
        border-radius: 50%;
        background-size: contain;
        background-repeat: no-repeat;
  
        img {
          width: 52px;
          height: 52px;
        }
      }
  
      .name-wrapper {
        width: 300px;
        display: flex;
        flex-direction: column;
        margin-left: 12px;
  
        .name {
          width: 300px;
          color: #222;
          font-weight: 600;
          // font-size: calc(100vw *  16 / 1920);
          font-size: calc(100vw *  15 / 1920);
          letter-spacing: 0px;
          line-height: 16px;
        }
  
  
        .id {
          margin-top: 7px;
          color: #7c86ac;
          font-weight: 400;
          font-size: calc(100vw *  12 / 1920);
          letter-spacing: 0px;
          line-height: 12px;
        }
      }
    }
  
    .divider {
      width: 100%;
      margin: 20px 0 18px;
      border-bottom: 1px solid #f0f3fa;
    }
  
    .options {
      .option {
        display: flex;
        align-items: center;
        margin-top: 20px;
  
        :first-child {
          margin-top: 0px;
        }
  
        .icon {
          width: 24px;
          height: 24px;
          background-size: contain;
        }
  
        .text {
          margin-left: 6px;
          color: #000000d9;
          font-weight: 700;
          font-size: calc(100vw *  14 / 1920);
          letter-spacing: 0;
          line-height: 22px;
        cursor: pointer;

        }
  
        .personal-center {
          background-image: url(../assets/icon_grzx.png);
        }
  
        .my-document {
          background-image: url(../assets/icon_wdwd.png);
        }
  
        .my-favourite {
          background-image: url(../assets/icon_wdsc.png);
        }
  
        .logout {
          background-image: url(../assets/icon_tc.png);
        }
      }
    }
  }
  
  ::v-deep(.el-alert--success.is-light) {
    background: #ebf9f7 !important;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  12 / 1920);
    color: #09873f;
    letter-spacing: 0;
    font-weight: 400;
  }
  
  // 导航项
  .nav-item {
    // width: 272px;
    height: calc(100vh * 40/ 1080);
    background: transparent;
    line-height: 20px;
    white-space: pre-wrap;
    background-image: linear-gradient(270deg,
        rgba(30, 75, 202, 0.39) 0%,
        rgba(59, 130, 234, 0.28) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 600;
    cursor: pointer;
    margin: 0 auto;
  
    &:hover {
      background: #59bce1;
    }
  
    img {
      width: 18px;
      height: 18px;
      float: left;
    }
  
    p {
      float: left;
      margin-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  16 / 1920);
      color: #ffffff;
      letter-spacing: 0;
    }
  }
  
  .choose {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: #59bce1;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 600;
  }
  
  .clbutton {
    // height: ;
    position: absolute;
    height: 30px;
    width: 100px;
    background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    bottom: 40px;
    left: 40px;
  }
  
  .clbutton1 {
    // height: ;
    position: absolute;
    height: 30px;
    width: 100px;
    background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    bottom: 40px;
    left: 160px;
  }
  
  .clbutton2 {
    left: 180px;
  }
  
  .showUp {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: ease-out 0.4s;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: 20px;
    position: absolute;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    bottom: 10px;
    left: 60px;
    height: calc(100vh * 40/ 1080);
    bottom: 20px;
    // z-index: 9999;
    cursor: pointer;
  
    &:hover {
      // background-color: rgba(46,98,245,.1)!important;
      background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
    }
  
    img {
      width: 18px;
      height: 18px;
      float: left;
    }
  
    p {
      float: left;
      margin-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  14 / 1920);
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
    }
  }
  
  .showUp1 {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: ease-out 0.4s;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: 20px;
    position: absolute;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    bottom: 10px;
    left: 6px;
    height: calc(100vh * 40/ 1080);
    bottom: 20px;
    // z-index: 9999;
    cursor: pointer;
  
    &:hover {
      // background-color: rgba(46,98,245,.1)!important;
      background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
    }
  
    img {
      width: 18px;
      height: 18px;
      float: left;
    }
  
    p {
      float: left;
      margin-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw *  14 / 1920);
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
    }
  }
  
  ::v-deep(.el-loading-spinner) {
    /*这个是自己想设置的 gif 加载动图*/
    background-image: url("../img/icegif-1259.gif");
    background-repeat: no-repeat;
    background-size: 150px 130px;
    height: 100px;
    width: 100%;
    background-position: center;
    /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
    top: 40%;
  }
  
  ::v-deep(.el-loading-spinner .circular) {
    /*隐藏 之前  element-ui  默认的 loading 动画*/
  
    display: none;
  }
  
  ::v-deep(.el-loading-spinner .el-loading-text) {
    /*为了使得文字在loading图下面*/
    margin: 85px 0px;
  }
  
  ::v-deep(.el-step.is-simple .el-step__arrow::before) {
    transform: rotate(-90deg) translateY(-20px) translateX(-16px);
    transform-origin: 0 0;
  }
  
  ::v-deep(.el-drawer__header) {
    display: none;
  }
  
  ::v-deep(.el-step.is-simple .el-step__arrow::after) {
    transform: rotate(60deg) translateY(-0px);
    transform-origin: 100% 100%;
    content: "";
    display: inline-block;
    position: absolute;
    height: 0px;
    width: 0px;
    background: #c0c4cc;
  }
  
  ::v-deep(.el-notification__group) {
    height: auto !important;
  }
  
  ::v-deep(.el-step.is-simple .el-step__arrow::before) {
    content: "";
    display: inline-block;
    position: absolute;
    height: 30px;
    width: 1px;
    background: #c0c4cc;
  }
  
  ::v-deep(.el-step.is-simple .el-step__title) {
    font-size: 14px !important;
  }
  
  ::v-deep(.el-step__title.is-success) {
    color: #1b2126;
    // ::v-deep(.el-step__icon) {
    //   background-color: #1B2126 !important;
    // }
    // border-color: #1B2126;
  }
  
  ::v-deep(.el-step__head.is-success) {
    color: #1b2126;
    border-color: #1b2126;
  }
  
  ::v-deep(.el-step__title.is-process) {
    color: #bbc6d3;
  }
  
  ::v-deep(.el-step__head.is-process) {
    color: #bbc6d3;
    border-color: #bbc6d3;
  }
  
  ::v-deep(.el-steps--simple) {
    background: none !important;
  }
  
  .progressClass {
    width: 100%;
    height: 26px;
    position: fixed;
    bottom: 48px;
    right: 0;
    z-index: 999;
  
    .progress {
      position: absolute;
      right: 52px;
      transition: ease-out 0.4s;
    }
  }
  
  ::v-deep(.el-progress-bar__innerText) {
    padding: 7px !important;
    color: #fff !important;
  }
  
  .menu_label {
    width: 100%;
    border-radius: 4px;
    margin-top: 10px;
    min-height: 158px;
    height: auto;
    padding: 15px 16px;
    // max-height: 158px;
    background: #f8f9fd;
    border: 1px solid rgba(229, 232, 245, 1);
    border-radius: 4px;
    padding: 15px 0px;
  }
  
  .el-tag {
    height: auto;
    white-space: normal !important;
    max-width: 383px;
    word-wrap: break-word;
    text-align: left;
    margin-left: 5px;
    margin-bottom: 5px;
    float: left;
  
  }
  
  .el-main .ai-body .tab-item-fir .menu_label:active {
    background: #fff;
    border: 1px solid #4170f6;
  }
  
  // .el-main .ai-body .tab-item-fir .menu_label:focus {
  //   background: #fff;
  //   border: 1px solid #4170f6;
  // }
  
  .pass_input {
    // float: left;
    width: 100%;
    height: calc(100vh * 40/ 1080);
  
    // margin-left: 10px;
    // el-input__inner是el-input的input类名
    & /deep/ .el-input__inner {
      border: none;
      background-color: rgba(122, 151, 255, 0) !important;
      font-family: PingFangSC-Regular;
      font-size: calc(100vw *  14 / 1920);
      color: #666666;
      letter-spacing: 0;
      font-weight: 400;
    }
  }
  
  ::v-deep(.el-alert__title) {
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  12 / 1920);
    color: #09873f;
    letter-spacing: 0;
    font-weight: 400;
  }
  
  ::v-deep(.el-drawer__open .el-drawer.ltr) {
    width: 100% !important;
  }
  
  ::v-deep(.el-drawer__header) {
    margin-bottom: 0;
  }
  
  ::v-deep(.el-alert) {
    padding: 8px 8px;
  }
  
  .fir-textarea1 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 96.5%;
    background: #f8f9fd;
    border: 1px solid rgba(229, 232, 245, 1);
    border-radius: 4px;
    margin: 20px;
    margin-bottom: 0;
    height: 158px;
  
    ::v-deep(.el-textarea__inner) {
      font-size: 14px !important;
      background-color: #f8f9fd !important;
      height: 100% !important;
      font-family: PingFangSC-Regular;
      padding: 13px 18px 33px 16px;
    }
  }
  
  ::v-deep(.el-collapse) {
    border: 0px solid #ebeef5;
  }
  
  ::v-deep(.el-collapse-item__header.is-active) {
    border-bottom: 1px solid #ebeef5;
  }
  
  ::v-deep(.el-collapse-item__header) {
    background-color: #f9fafe;
    height: calc(100vh * 40/ 1080);
    border-radius: 4px;
  }
  
  ::v-deep(.el-collapse-item__wrap) {
    border-radius: 4px;
  }
  
  ::v-deep(.el-collapse-item__content) {
    background-color: #f9fafe;
    border-radius: 4px;
  }
  
  .fir-textarea-max1 {
    height: 95% !important;
  }
  
  .highlight {
    background-color: yellow;
  }
  
  .result-title {
    position: relative;
  
    &:before {
      position: absolute;
      content: '';
      left: -10px;
      top: 2px;
      width: 4px;
      height: 15px;
      background: #5585F0;
      border-radius: 2px;
    }
  }
  
  // ***滚动条样式
  .result-content::-webkit-scrollbar {
    width: 6px;
    /* 滚动条的宽度 */
  }
  
  .result-content::-webkit-scrollbar-track {
    background: #fff;
    /* 滚动条的背景色 */
  }
  
  .result-content::-webkit-scrollbar-thumb {
    background: #488aff;
    /* 滚动条的滑块颜色 */
  }
  
  .elcol-title-left {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-top: 20px;
    margin-left: 10px;
  }
  
  .elcol-title {
    display: flex;
    overflow: hidden;
    width: 100%;
    height: 48px;
  }
  
  .elcol-title-text {
    // float: left;
    padding-left: 10px;
    text-align: left;
    width: 40%;
    height: 100%;
  }
  
  .elcol-title-text2 {
    font-size: calc(100vw *  14 / 1920);
    color: #d1d7de;
    // float: left;
    width: 75px;
  }
  
  
  
  .elcol-input {
    float: left;
    width: calc(60% - 75px);
    border: none !important;
  }
  </style>