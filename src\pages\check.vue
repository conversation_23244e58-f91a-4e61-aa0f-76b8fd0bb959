<template>
  <!-- 核稿 -->
  <div class="work-container" v-loadin g="con_loading">
    <el-container>
      <!-- <div :style="navShow ? 'width: -10px' : 'width: 10px'" class="ejdhl">
        <div class="ai-nav" :style="navShow ? 'width: 300px' : 'width: 64px'">
        </div>
      </div> -->
      <!-- <el-header class="el-header1">
        <div class="ai-header">
          <div class="flex">
            <div class="ai-left-bar-li actived"> </div>    
          </div>
        </div>
      </el-header> -->
      <el-header>
        <div class="ejdhlTitle">
          <!-- <img src="../img/logo05.png" width="100%" alt="" /> -->
          <!-- <img src="../assets/lo1.png" width="100%" alt="" /> -->
        </div>
        <div class="ai-header">
          <div class="ai-bar">
            <HeadNavigation />
           
          </div>
        </div>
      </el-header>
      <el-main style="transition: ease-out 0.4s; display: flex">
        <div style=" width: 100%; argin-top: 100px">
          <!-- <button
            @click="tomanage()"
            data-v-819514c6=""
            aria-disabled="false"
            type="button"
            class="el-button el-button--primary el-button--default"
           
          >
         
          <div style="margin-top:4px;">
           模板管理
            </div>
          </button> -->
        <div
          style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-left: calc(100vw * 35 / 1920);
          height: calc(100vh * 35 / 1080);
        "
        >
        <div>
          <button
            @click="adddia = true"
            data-v-819514c6=""
            aria-disabled="false"
            type="button"
            class="el-button el-button--primary el-button--default"
          >
            <i class="el-icon"
              ><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill="currentColor"
                  d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                ></path></svg></i
            ><span class="">新建核稿</span>
          </button>

          <button
            @click="batchDelete"
            data-v-5317e0d1=""
            aria-disabled="false"
            type="button"
            class="el-button el-button--danger el-button--default"
          >
            <i class="el-icon"
              ><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill="currentColor"
                  d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                ></path></svg></i
            ><span class="">批量删除</span>
          </button>
        </div>
          <!-- <button
          
            data-v-819514c6=""
            aria-disabled="false"
            type="button"
            class="el-button el-button--primary el-button--default"
          >
                <span style="height:42px;margin-top:30px;">更新状态</span>
          </button> -->
          <div
            data-v-3685a8a8=""
            class="el-input el-input--default el-input-group el-input-group--append el-input--suffix w-50 m-2"
            style="
              margin-left: calc(100vw * 20 / 1920);
              width: calc(100vw * 350 / 1920);
            "
            >
            <!-- input --><!-- prepend slot --><!--v-if-->
            <div tabindex="-1">
              <!-- prefix slot --><!--v-if-->
              <input
                class="el-input__inner"
                type="text"
                autocomplete="off"
                tabindex="0"
                placeholder="请输入文件名"
                id="el-id-4263-16"
                v-model="inputvalyhm"
              />
              <!-- <span
                v-if="inputvalyhm != ''"
                class="el-icon-circle-close"
                @click="clearInput"
                style="
                  cursor: pointer;
                  position: absolute;
                  left: calc(100% - 80px);
                  top: 50%;
                  transform: translateY(-50%);
                "
              >
              </span> -->
            </div>
            <!-- <div  @click:sea(this.inputvalyhm)>查询</div> -->
            <!-- append slot -->
            <div class="el-input-group__append" @click="sea(inputvalyhm)">
              <div class="el-icon-search"></div>
            </div>
            <!-- <button
            @click="tomanage()"
            data-v-819514c6=""
            aria-disabled="false"
            type="button"
            class="el-button el-button--primary el-button--default"
           
          >
         
          <div style="margin-top:4px;">
           模板管理
            </div>
          </button> -->
            <el-button
              @click="tomanage()"
              slot="trigger"
              size="small"
              type="primary"
              style="
                border-radius: 20px;background-image: linear-gradient(107deg,#3a6bc6 0%,#488aff 100%);box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);margin-left: 10px;"
              >模板管理</el-button
            >
          </div>
          </div>
          <div
            style="
              margin-left: calc(100vw * 35 / 1920);
              margin-top: calc(100vh * 10 / 1080);
              height: calc(100vh * 850 / 1080);
              width: calc(100% - calc(100vw * 35 / 1920)); /* 减去左右边距 */
              overflow: hidden; /* 防止内容溢出 */
            "
          >
          <div style="height: calc(100vh * 850 / 1080)">
            <el-table
              stripe
              border
              @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'date', order: 'descending' }"
              :data="this.tableData1"              
              style="
                margin-top: calc(100vh * 10 / 1080);
                height: calc(100vh * 840 / 1080);
                overflow-y: scroll;
              "
              :header-cell-style="{
                background: '#ebf2fb',
              }"
            >
              <el-table-column type="selection"> </el-table-column>
              <el-table-column label="序号" sortable type="index" width="50">
              </el-table-column>
              <el-table-column
                prop="file_name"
                sortable
                label="文件名"
              >
              </el-table-column>
              <el-table-column
                prop="template_name"
                sortable
                label="模板名"
              >
              </el-table-column>
              <el-table-column
                prop="type_name"
                sortable
                label="文种名"
              >
              </el-table-column>
              <el-table-column
                prop="cjr_name"
                sortable
                label="创建人"
              >
              </el-table-column>
              <!-- <el-table-column
                prop="cyry"
                sortable
                label="可修订人"
                width="120"
              >
              </el-table-column> -->
               <!-- <el-table-column prop="cyry" sortable label="可修订人">
                <template v-slot="scope">
                  <div
                    style="
                      max-height: 50px;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    :title="scope.row.cyry"
                  >
                    {{ scope.row.cyry }}
                  </div>
                </template> -->
              <!-- </el-table-column> -->
              <el-table-column
                prop="cjsj"
                sortable
                label="创建时间"
              >
              </el-table-column>

              <el-table-column
                prop="percentage"
                sortable
                label="进度"
              >
                <template v-slot="scope">
                  <div style="display: flex; align-items: center">
                    <el-progress
                      :percentage="scope.row.percentage"
                      :status="
                        scope.row.status === '失败' ? 'exception' : 'success'
                      "
                      :format="() => ''"
                      :show-text="false"
                      style="flex: 1"
                    ></el-progress>
                    <span style="margin-left: 10px"
                      >{{ scope.row.percentage }}%</span
                    >
                    <!-- 显示自定义百分比 -->
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="status" sortable label="状态">
                <template v-slot="scope">
                  <span
                    :style="{
                      color: scope.row.status === '成功' ? 'green' : 'red',
                    }"
                  >
                    {{ scope.row.status }}
                  </span>
                  <!-- 在状态为失败时显示额外的按钮 -->
                  <el-button
                    v-if="scope.row.status === '失败'"
                    @click="show(scope.row)"
                    class="el-icon-question"
                    style="
                      border: none;
                      background: none;
                      cursor: pointer;
                      font-size: calc(100vw * 18 / 1920);
                      margin-left: -10px;
                    "
                  >
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="" label="操作" width="400" align="center">
                <template v-slot="aa">
                  <el-button
                  v-if="aa.row.flag==true"
                    style="margin-right: 0px"
                    type="text"
                    size="medium"
                    @click="down=(aa.row),showdia2(down)"
                    :style="{
                      color:
                        aa.row.percentage === 100 && aa.row.status === '成功'
                          ? '#409EFF'
                          : 'grey',
                    }"
                    >下载结果</el-button
                  >
                  <el-button
                  v-if="aa.row.flag==true"
                    style="margin-right: 0px"
                    type="text"
                    size="medium"
                    @click="showdia22(aa.row)"
                    :style="{
                      color: aa.row.percentage === 100 ? '#409EFF' : 'grey',
                    }"
                    >重新核稿</el-button
                  >
                  <!-- <el-button
                    style="margin-right: 0px"
                    type="text"
                    size="medium"
                    @click="get_showdia2(aa.row)"
                    :style="{
                      color:
                        aa.row.percentage === 100 && aa.row.status === '成功'
                          ? '#409EFF'
                          : 'grey',
                    }"
                    >反馈</el-button
                  > -->
                  <!-- <el-button
                    style="margin-right: 0px"
                    type="text"
                    size="medium"
                    @click="get_showdia3(aa.row)"
                    :style="{
                      color:
                        aa.row.percentage === 100 && aa.row.status === '成功'
                          ? '#409EFF'
                          : 'grey',
                    }"
                    >修订</el-button
                  > -->
                   <!-- <el-button
                    style="margin-right: 0px"
                    type="text"
                    size="medium"
                    @click="downxd(aa.row)"
                    :style="{
                      color:
                        aa.row.percentage === 100 && aa.row.status === '成功'
                          ? '#409EFF'
                          : 'grey',
                    }"
                    >修订</el-button
                  > -->
                  <el-button
                  v-if="aa.row.flag==true"
                    style="margin-right: 0px"
                    type="text"
                    size="medium"
                    @click="fbdia = true,fb_check_id=aa.row.check_id"
                    :style="{
                      color:
                        aa.row.percentage === 100 && aa.row.status === '成功'
                          ? '#409EFF'
                          : 'grey',
                    }"
                    >发布</el-button
                  >
                  <el-button
                  v-if="aa.row.flag==true"
                    style="color: red"
                    type="text"
                    size="medium"
                    @click="showdia3(aa.row)"
                    >删除</el-button
                  >
                  <!-- <el-button @click="show(aa.row)" class="el-icon-question" style="border: none; background: none;cursor: pointer;font-size: 18px;"></el-button> -->
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="this.total"
          >
          </el-pagination>
        </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog title="新建核稿" :visible.sync="adddia" @close="resetForm()">
      <el-form :model="form" :label-width="formLabelWidth">
        <el-form-item label="上传文件">
          <el-upload
            class="upload-demo"
            @change="handleFileChange"
            :before-upload="beforeAvatarUpload"
            ref="upload"
            action="#"
            accept=".docx"
            :show-file-list="false"
            :file-list="fileList"
          >
            <div
              class="el-icon-upload2"
            ></div>
            <div v-if="fileList.length > 0" >
              {{ fileList[0].name }} 已上传
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="选择文种" >
          <el-select
            v-model="form.region"
            placeholder="请选择文种类型"
            @change="onRegionChange($event)"
          >
            <el-option
              v-for="option in options"
              :key="option.type_id"
              :label="option.type_name"
              :value="option.type_id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择模板" >
          <el-select
            v-model="form.region1"
            placeholder="请选择模板"
            @change="onRegionChange1($event)"
          >
            <el-option
              v-for="option in options1"
              :key="option.template_id"
              :label="option.template_name"
              :value="option.template_id"
            >
            </el-option>
          </el-select>
          <el-button
            class="el-icon-view"
            type="text"
            @click="content = true"
          ></el-button>
        </el-form-item>
        <el-form-item>
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            >校验内容
          </el-checkbox>
          <el-checkbox-group
            v-model="checkedCities"
            @change="handleCheckedCitiesChange"
            style="
              display: flex;
              flex-direction: column;
              text-align: left;
              margin-left: 240px;
            "
          >
            <el-checkbox
              v-for="city in cities"
              :label="city"
              :key="city"
              >{{ city }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div
        slot="footer"
        class="dialog-footer"
        style="display: flex; justify-content: flex-end; align-items: center"
      >
        <el-button
          @click="resetForm(), (adddia = false)"
          style="margin-right: 10px"
          >取消</el-button
        >
        <el-button type="primary" @click="check_file()">核稿</el-button>
      </div>
    </el-dialog>
    
    <!-- <el-dialog title="新建核稿" :visible.sync="adddia" @close="resetForm()">
      <el-form :model="form">
        <el-form-item label="上传文件" :label-width="formLabelWidth">
          <el-upload class="upload-demo" @change="handleFileChange" :before-upload="beforeAvatarUpload" ref="upload"
            action="#" accept=".docx" :show-file-list="false" :file-list="fileList">
            <div class="el-icon-upload2" style="margin-left: auto; margin-top: 10px"></div>
            <div v-if="fileList.length > 0" style="margin-left: auto">
              {{ fileList[0].name }} 已上传
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="选择文种" :label-width="formLabelWidth">
          <el-select v-model="form.region" placeholder="请选择文种类型" @change="onRegionChange($event)" style="width: 100%">
            <el-option v-for="option in options" :key="option.type_id" :label="option.type_name"
              :value="option.type_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择模板" :label-width="formLabelWidth">
          <el-select v-model="form.region1" placeholder="请选择模板" @change="onRegionChange1($event)" style="width: 100%">
            <el-option v-for="option in options1" :key="option.template_id" :label="option.template_name"
              :value="option.template_id"></el-option>
          </el-select>
          <el-button class="el-icon-view" type="text" @click="content = true"
            style="font-size: 18px; margin-left: 10px; cursor: pointer"></el-button>
        </el-form-item>
        <el-form-item>
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">校验内容
          </el-checkbox>
          <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange"
            style="display: flex; flex-direction: column; text-align: left; margin-left: auto">
            <el-checkbox v-for="city in cities" :label="city" :key="city"
              style="margin-bottom: 10px; text-align: left">{{ city }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="display: flex; justify-content: flex-end; align-items: center">
        <el-button @click="resetForm(), (adddia = false)" style="margin-right: 10px">取消</el-button>
        <el-button type="primary" @click="check_file()">核稿</el-button>
      </div>
    </el-dialog> -->
    
    <el-dialog title="校验项详情" :visible.sync="content">
      <el-descriptions title="">
        <el-descriptions-item
          label="文字格式校验："
          style="margin-right: 10px"
          :label-style="{
            fontWeight: 'bold',
            fontSize: '15px',
            color: '#409EFF',
          }"
        >
          <div>
            <div
              v-for="item in characters"
              :key="item.text_color"
              style="
                margin-bottom: 10px;
                border: 1px solid #ccc;
                padding: 5px;
                border-radius: 4px;
              "
            >
              {{ item.description }}；文字颜色:
              <span :style="{ color: item.text_color }">颜色示例</span>
            </div>
          </div>
        </el-descriptions-item>

        <el-descriptions-item
          label="页面格式校验："
          style="margin-right: 10px"
          :label-style="{
            fontWeight: 'bold',
            fontSize: '15px',
            color: '#409EFF',
          }"
        >
          <!-- {{this.page_format}} -->
          <div style="margin-top: 5px">{{ this.page_format }}</div>
        </el-descriptions-item>
        <!-- <el-descriptions-item label="其他项目校验：">
      <div style="margin-top: 5px;">{{this.qtys}}</div>
    </el-descriptions-item> -->
        <el-descriptions-item
          label="其他项目校验："
          :label-style="{
            fontWeight: 'bold',
            fontSize: '15px',
            color: '#409EFF',
          }"
        >
          <div style="margin-top: 5px">
            <div v-for="item in qtys" :key="item.name">
              {{ item.name }}: {{ item.value }}
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
    <!-- <el-dialog title="提示" :visible.sync="this.$route.query.showdownload" @close="showdownload = false">
     <span>请选择您下载的文件类型：</span>
    </el-dialog> -->
    <el-dialog
      title="发布"
      :visible.sync="fbdia"
      @close="handleClosefb()"
      v-loading="loading"
      element-loading-text="请稍候..."
      element-loading-background="rgba(255, 255, 255, 0.7)"
    >
      <el-form>
        <el-form-item label="可修订人员"></el-form-item>
        <el-tree
          :data="treeData"
          style="margin-left: 150px; margin-top: -52px"
          :props="props"
          show-checkbox
          @node-click="handleNodeClick"
          @check="handleCheck"
        >
        </el-tree>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClosefb()">取 消</el-button>
        <el-button type="primary" @click="submitForm1()">确定发布</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="downdia"
      @close="downdia = false"
      v-loading="loading"
      element-loading-text="请稍候..."
      element-loading-background="rgba(255, 255, 255, 0.7)"
    >
    <span style="margin-bottom: 40px;font-size:16px;">请选择您下载的文件类型：</span>
      <el-radio v-model="radio" label="2">最终稿</el-radio>
      <el-radio v-model="radio" label="1">花脸稿</el-radio>
      <div slot="footer" class="dialog-footer">
        <el-button @click="downdia = false">取 消</el-button>
        <el-button type="primary" @click="showdia2(down)">确定下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
const cityOptions = ["文字格式校验", "页面格式校验", "其他的校验项"];
import App from "../App";
import Wordcloud from "../components/Wordcloud.vue";
import { VueEditor } from "vue2-editor";
import CustomSteps from "../components/Step.vue";
import HeadNavigation from "../components/HeadNavigation.vue"; // 引入头部导航组件

//   import store from '../store/index'
import { mapState } from "vuex";
// ****************************
import Quill from "quill";
let BlockEmbed = Quill.import("blots/block/embed");
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute(
      "style",
      "padding: 20px; border: 1px dashed #488aff; color:red; position: relative;"
    );
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement("button");
    insertButton.innerHTML = "应用";
    insertButton.className = "btn-insert";
    insertButton.style.position = "absolute";
    // 按钮颜色
    insertButton.style.backgroundColor = "#488aff";
    // 字体为白色
    insertButton.style.color = "white";
    // insertButton.style.top = '10px';
    insertButton.style.right = "70px";
    insertButton.addEventListener("click", () => {
      // console.log(value.editorClass, '编辑器')
      // console.log(value.newText, '续写内容！')
      // // 这里的位置，是替换的传position，是插入的传position加length
      // console.log(value.Position, '位置')
      // console.log(value.selectedTextlength, '长度？？？？？？？？')
      // console.log(value.editorClass.getSelection(), '光标位置？？？？')
      if (value.flag == 1) {
        value.editorClass.insertText(
          value.Position + value.selectedTextlength,
          value.newText
        );
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement("button");
    cancelButton1.innerHTML = "取消";
    cancelButton1.className = "btn-cancel";
    cancelButton1.style.position = "absolute";
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = "#e64721";
    // 字体为白色
    cancelButton1.style.color = "white";
    cancelButton1.style.right = "10px";
    cancelButton1.addEventListener("click", () => {
      node.remove();
      // alert('取消按钮点击');
      // // 这里可以添加取消按钮的逻辑
    });
    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = "customBlock";
CustomBlock.tagName = "div";

Quill.register({
  "formats/customBlock": CustomBlock,
});
import store from "../store/index";
import {
  recheck,
  select_checkById,
  insert_handle,
  update_Single_User,
  select_handle_all,
  add_Single_User,
  js,
  fwsc,
  getSecDtDatas,
  getSuccessInfo,
  getSuccessInfoItem,
  getLabel1,
  getLabel2,
  testcheck,
  bcwz,
  rewrite,
  xz,
  cyt,
  get_user,
  getwrite,
  get_login_user,
  initPassWord,
  disabled,
  remove_disabled,
  delete_User,
  getdocument_type,
  select_templates_BytypeId,
  getfont,
  getfont_size,
  getpaper_size,
  getpreset,
  select_template_BytemplateId,
  upload_check_file,
  insert_check,
  select_checkBypage,
  selectPolling,
  download_checkResult,
  delete_check,
  select_check_result,
  select_handles,
  select_userByhandle_id,
  release_check,
  selectFilePath,
  downloadReviseFile
} from "../api/home.js"; // 接口请求
import { set } from "vue";
export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id
    // ...mapState(["id"]), // 映射 username 和 id
    username() {
      return this.$store.state.username;
    },
    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 600}px`,
      };
    },
    id() {
      return this.$store.state.id;
    },
    imageSrc() {
      return `data:image/jpeg;base64,${this.base64Image}`; // 注意根据实际的MIME类型替换'image/jpeg'
    },
  },
  data() {
    return {
      fb_check_id: "",
      down:'',
       radio: "1",
      downdia: false,
      props: {
        children: "children",
        label: "handleName",
        isLeaf: "leaf",
       
      },
      treeData: [],
      select_handlespeo: [], // 打印选中的节点 ID 数组
      selectedNodes: [], // 存储选中节点的数组
      expandedKeys: [], // 默认展开的节点
      checkedKeys: [], // 默认勾选的节点
      fbdia: false,
      showdownload: false,
      fileList: [],
      pollingInterval: null,
      percentage: 0,
      characters: [
        {
          description: "请先选择文种和模板",
          text_color: "请先选择文种和模板",
        },
      ],
      content: false,
      temid: "",
      character: [],
      page_format: "请先选择文种和模板",
      qtys: "请先选择文种和模板",
      checkAll: true,
      // checkedCities: [],
      checkedCities: cityOptions.slice(), // 初始化为城市选项数组
      cities: cityOptions,
      // isIndeterminate: true,
      isIndeterminate: false,
      filepath: "",
      form: {
        region: null, // 用于存储选中的值
        region1: null, // 用于存储选中的值
      },
      options: [], // 用于存储后端返回的数据
      options1: [], // 用于存储后端返回的数据

      selectedUsers: [], // 批量删除时，用于存储选中的文件名字
      departments: [], // 用于存储从后端获取的处室数据
      adddia: false,
      adddiacs: false,

      tableData1: [],
      inputvalyhm: "",
      sure_passw: "",
      new_passw: "",
      old_passw: "",
      formLabelWidth: "calc(100vw * 100 / 1920)",
      dialogVisible: false,
      dialogVisibleedit: false,
      up_img: false,
      nan_img: false,
      nv_img: false,
      backgroundImage: "", // 默认背景图
      tags: [], // 存储从后端获取的“句式特点”标签
      tags1: [], // 存储从后端获取的“句式特点”标签
      tags2: [], // 存储从后端获取的“句式特点”标签
      base64Image: "",
      xb: "",
      xm: "",
      szdw: "",
      ssgw: "",
      sjgzly: "",
      ssqy: "",
      age: "",
      wcdata: [],
      // 定义变量存词云的返回值，然后用vuex传给词云组件
      fzqwnr: "",
      newText: "",
      newText1: "",
      newText2: "",
      newText3: "",
      xxdialog: false,
      xxcontent: "",
      kxdialog: false,
      sxdialog: false,
      rsdialog: false,
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      selectedText: "",
      con_loading: false,
      loading1: false,
      correctionList: [],
      jydis: true,
      navShow: true,
      textarea: "",
      textarea2: [],
      textarea21: [],
      textarea22: [],
      textarea3: "",
      textarea4: "",
      textarea4: "",
      dialogShow: false,
      dialog2Show: false,
      gzbj: [],
      stepActived: 1,
      loading: false,
      curIndex: "2",
      articles: "",
      artShow: false,
      mask: false,
      maskAll: false,
      /**
       * @description:  功能性按钮
       * @return {*}    传入组件->FunctionTable
       */
      steps: [
        { title: "录入角色" },
        { title: "录入写作风格" },
        { title: "生成词云" },
      ],

      currentStep: 2, // 初始化当前步骤为第二个步骤
      secRecommend: [], // 第二步推荐
      dynamicTags: [],
      inputVisible: true,
      inputValue: "",
      resDataItem: [],
      progressPercent: 0,
      fetchResPrams: [],
      buttonShow: true,
      drawer: false,
      direction: "ltr",
      activeNames: [],
      currentPage: 1, // 当前页
      pageSize: 10, // 每页显示条数
      total: 0,

      // 表单验证规则
      formRules: {
        userName: [
          { required: true, message: "模板名不能为空", trigger: "blur" },
        ],
        passWord: [
          { required: true, message: "密码不能为空", trigger: "blur" },
        ],
        handle_name: [
          { required: true, message: "所在处室不能为空", trigger: "change" },
        ],
        // gwmc: [
        //   { required: true, message: '最后修改时间不能为空', trigger: 'blur' }
        // ]
      },
      // };
    };
  },
  components: {
    App,
    // 注册组件
    CustomSteps,
    VueEditor,
    Wordcloud,
    HeadNavigation,

  },
  //   created() {
  //  this.showdownload = this.$route.query.showdownload === true;
  // },
  watch: {
    "$route.query.showdownload": {
      immediate: true,
      handler(newVal) {
        console.log("Query Parameter:", newVal); // 添加调试信息
        this.showdownload = newVal === "true"; // 这里将字符串转换为布尔值
        console.log("Converted to Boolean:", this.showdownload); // 添加调试信息
      },
    },
  },
  mounted() {
    this.get_handles();
    this.getuser(1, 10);
    // this.getszcs();
    this.getdocument_type();
    this.pollingInterval = setInterval(() => {
      // this.selectPolling();
      this.getuser(this.currentPage, this.pageSize);
    }, 10000);
  },
  beforeDestroy() {
    clearInterval(this.pollingInterval); // 清除定时器
  },
      methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
    //    async showdia2(c1) {
    //   let params = {
    //     check_id: c1.check_id,
    //     flag:this.radio,
    //   };
    //   if (c1.percentage == 100 && c1.status == "成功") {
    //     download_checkResult(params)
    //       .then((blob) => {
    //         if (blob && blob.size > 0) {
    //           // 创建一个 URL 对象
    //           const url = window.URL.createObjectURL(blob); // 创建一个 <a> 元素
    //           const a = document.createElement("a"); // 设置下载属性
    //           a.href = url;
    //           a.download = `${c1.file_name}`; // 设置下载文件名，使用会议 ID 作为文件名的一部分 // 将 <a> 元素添加到文档中
    //           document.body.appendChild(a); // 触发点击事件
    //           a.click(); // 移除 <a> 元素
    //           document.body.removeChild(a); // 释放 URL 对象
    //           window.URL.revokeObjectURL(url);
    //           this.downdia = false;
    //           // downdia
    //         } else {
    //           this.$message({
    //             message: "下载失败: 无效的文件",
    //             type: "error",
    //           });
    //         }
    //       })
    //       .catch((error) => {
    //         console.error("下载时出现错误:", error);
    //         this.$message({
    //           message: "下载失败，请稍后重试",
    //           type: "error",
    //         });
    //       });
    //   } else {
    //     this.$message({
    //       message: "暂无可下载文件",
    //       type: "error",
    //     });
    //   }
    // },
    downxd(c1){
   let params = {
        check_id: c1.check_id,
        // flag:this.radio,
      };
      if (c1.percentage == 100 && c1.status == "成功") {
        downloadReviseFile(params)
          .then((blob) => {
            if (blob && blob.size > 0) {
              // 创建一个 URL 对象
              const url = window.URL.createObjectURL(blob); // 创建一个 <a> 元素
              const a = document.createElement("a"); // 设置下载属性
              a.href = url;
              a.download = `${c1.check_id+'_'+c1.file_name}`; // 设置下载文件名，使用会议 ID 作为文件名的一部分 // 将 <a> 元素添加到文档中
              document.body.appendChild(a); // 触发点击事件
              a.click(); // 移除 <a> 元素
              document.body.removeChild(a); // 释放 URL 对象
              window.URL.revokeObjectURL(url);
              this.downdia = false;
            } else {
              this.$message({
                message: "下载失败: 无效的文件",
                type: "error",
              });
            }
          })
          .catch((error) => {
            console.error("下载时出现错误:", error);
            this.$message({
              message: "下载失败，请稍后重试",
              type: "error",
            });
          });
      } else {
        this.$message({
          message: "暂无可下载文件",
          type: "error",
        });
      }
    },

//  },
    async get_wj(c1){
      let params = {  
        check_id:c1.check_id
      };
      let res = await selectFilePath(params);
      console.log(res.data.data, "查看绝对路径是啥");
// /home/<USER>/app/save/民航局_20250218172927_1.docx 
    },
       handleClosefb() {
      // this.fileList = [];
      this.selectedNodes = [];
      this.select_handlespeo = [];
      this.fbdia = false;
      // 关闭对话框时重置表单
      // this.inputs = [""];
      // this.successpdfurl = "";
      // this.form = {
      //   meet_name: "",
      //   meet_remarks: "",
      //   meet_theme: [],
      //   // meet_time: "",
      //   meet_time: this.getEndOfDay(), // 初始化时间为当天的 23:59:59
      // };
      this.treeData = [];
      this.get_handles();
      // this.adddia = false; // 确保关闭对话框
    },

    async fetchChildren(handleId) {
      try {
        let res = await select_userByhandle_id({
          handle_id: handleId,
          userId: this.id,
        }); // 根据当前节点的 handleId 调取接口
        // console.log("获取下层节点成功:", res.data.data);
        return res.data.data.map((item) => ({
          handleId: item.id,
          handleName: item.name,
          children: [], // 初始化为空数组支持后续懒加载
        }));
      } catch (error) {
        console.error("获取下层节点失败:", error);
        return []; // 返回空数组以避免错误
      }
    },
    async submitForm1() {
      let params = {
        check_id: this.fb_check_id,
        cyry: this.select_handlespeo,
      };
     let res = await release_check(params); 
      // let formData = new FormData();
      //  let res = await 发布接口(formData);
      // cyry
      // formData.append("cyry", this.select_handlespeo);
      console.log(this.select_handlespeo, "选中的节点数组");
      // this.fbdia=false;
      if (res.data.status_code == 200) {
        this.$message({
          message: "发布成功",
          type: "success",
        });
        this.fbdia = false;
        this.getuser(this.currentPage, this.pageSize);
        this.handleClosefb;
      } else {
        this.$message({
          message: "发布失败",
          type: "error",
        });
      }
    },
 
    async get_handles() {
      let res = await select_handles();
      // 转换数据格式
      //  console.log(res.data.data);
      this.treeData = res.data.data.map((item) => {
        return {
          handleId: item.handleId,
          handleName: item.handleName,
          children: [],
        };
      });
      // console.log(this.treeData[0].handleName);
      // console.log(this.treeData[0].children);
    },

    async handleNodeClick(node) {
      if (node.handleId) {
        // 确保当前节点有 handleId
        const children = await this.fetchChildren(node.handleId); // 发起请求获取下层数据
        if (children.length > 0) {
          node.disabled = false;
          node.children = children; // 将返回的子节点数据添加到当前节点的 children 属性中
          this.$set(
            this.treeData,
            this.treeData.findIndex((item) => item.handleId === node.handleId),
            node
          );
        } else {
          node.disabled = true; // 节点没有展开时设置为不可选中
        }
      }
    },
    // handleClose() {
    //   // this.fileList = [];
    //   // this.selectedNodes = [];
    //   // this.select_handlespeo = [];
    //   // // 关闭对话框时重置表单
    //   // this.inputs = [""];
    //   // this.successpdfurl = "";
    //   // this.form = {
    //   //   meet_name: "",
    //   //   meet_remarks: "",
    //   //   meet_theme: [],
    //   //   // meet_time: "",
    //   //   meet_time: this.getEndOfDay(), // 初始化时间为当天的 23:59:59
    //   // };
    //   this.treeData = [];
    //   this.fbdia = false; // 确保关闭对话框
    // },
    get_showdia2(c1) {
      // let params = {
      //   check_id:c1.check_id,
      // };
      // let res = select_check_result(params);
      this.$router.push("/back");
      this.$store.dispatch("updatecheck_id", c1.check_id);
    },
    get_showdia3(c1) {
      // let params = {
      //   check_id:c1.check_id,
      // };
      // let res = select_check_result(params);
      this.$router.push("/apply");
      this.$store.dispatch("updatecheck_id", c1.check_id);
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    async selectPolling() {
      let params = {
        user_id: this.id,
      };
      let res = await selectPolling(params);
      // console.log(res.data, "轮询接口的res");
      if (res.data.data == 1) {
        this.getuser(this.currentPage, this.pageSize);
      }
    },
    handleFileChange(file, fileList) {
      // 处理文件变化的逻辑，添加清空之前文件列表的逻辑
      this.fileList = [file]; // 仅显示当前文件
    },
    async show(c1) {
      let params = {
        check_id: c1.check_id,
      };
      let res = await select_checkById(params);
      // console.log(res.data.data, "查看详情接口的res");
      this.$message({
        // message: res.data.data,
        message: res.data.data.replace(/\n/g, "<br>"), // 替换 \n 为 <br>
        type: "warning",
        dangerouslyUseHTMLString: true,
      });
    },

    toxg() {
      this.$router.push("/grzx4");
    },
    sfw111() {
      this.$router.push("/sfw");
    },
    clickTopLg() {
      this.$router.push("/Lgsc");
    },
    clickTopNav() {
      this.$router.push("/Lgsc");
    },
    clickTopAi() {
      this.$router.push("/index");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    showdia3(c1) {
      let params = {
        check_id: [c1.check_id],
      };
      this.$confirm("此操作将永久删除该核稿，是否继续？")
        .then(() => {
          delete_check(params)
            .then((res) => {
              if (res.data.status_code == 200) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                });
                this.getuser(this.currentPage, this.pageSize, this.inputvalyhm);
              } else {
                this.$message({
                  message: "删除失败",
                  type: "error",
                });
              }
            })
            .catch((error) => {
              console.error("删除时出现错误:", error);
              this.$message({
                message: "删除失败，请稍后重试",
                type: "error",
              });
            });
        })
        .catch(() => {
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async showdia2(c1) {
      let params = {
        check_id: c1.check_id,
        flag:"2",
      };
      if (c1.percentage == 100 && c1.status == "成功") {
        download_checkResult(params)
          .then((blob) => {
            if (blob && blob.size > 0) {
              // 创建一个 URL 对象
              const url = window.URL.createObjectURL(blob); // 创建一个 <a> 元素
              const a = document.createElement("a"); // 设置下载属性
              a.href = url;
              a.download = `${c1.file_name}`; // 设置下载文件名，使用会议 ID 作为文件名的一部分 // 将 <a> 元素添加到文档中
              document.body.appendChild(a); // 触发点击事件
              a.click(); // 移除 <a> 元素
              document.body.removeChild(a); // 释放 URL 对象
              window.URL.revokeObjectURL(url);
              this.downdia = false;
              // downdia
            } else {
              this.$message({
                message: "下载失败: 无效的文件",
                type: "error",
              });
            }
          })
          .catch((error) => {
            console.error("下载时出现错误:", error);
            this.$message({
              message: "下载失败，请稍后重试",
              type: "error",
            });
          });
      } else {
        this.$message({
          message: "暂无可下载文件",
          type: "error",
        });
      }
    },
    async showdia22(c1) {
      let params = {
        check_id: c1.check_id,
      };
      // if(c1.status == '成功'){
      if (c1.percentage == 100) {
        let res = await recheck(params);
        // console.log(res, "查看接口的res");
        if (res.data.status_code == 200) {
          this.$message({
            message: res.data.message,
            type: "success",
          });
          this.getuser(this.currentPage, this.pageSize);
        } else {
          this.$message({
            message: res.data.message,
            type: "error",
          });
        }
      } else {
        this.$message({
          message: "上次核稿暂未完成，请稍侯",
          type: "error",
        });
      }
    },
    resetForm() {
      this.fileList = [];
      this.qtys = "";
      this.options1 = [];
      this.temid = "";
      // this.options=[];
      this.form.region1 = null; // 重置表单
      this.form = {
        // 重新定义表单的初始状态
        region: "",
        region1: null,
        // 如果有其他字段，也在这里重置
      };
      this.checkAll = false; // 重置全选状态
      this.isIndeterminate = false; // 重置不确定状态
      this.characters = [];
      this.page_format = "";
      this.checkedCities = [];
      this.characters = []; // 清空字符描述
      this.page_format = ""; // 清空页面格式
      this.qtys = []; // 清空其它项目
    },

    async getuser(c1, c2, c3) {
      let params = {
        pageNo: c1,
        pageSize: c2,
        file_name: c3,
        user_id: this.id,
      };
      let res = await select_checkBypage(params);
      if (res.data.status_code == 200) {
        this.tableData1 = res.data.data;
        this.total = res.data.total_records;
      } else {
        this.$message({
          message: "获取列表失败",
          type: "error",
        });
      }
    },

    async check_file() {
      let params = {
        file_path: this.filepath,
        template_id: this.temid,
        check_item: this.checkedCities,
        // check_type: this.checkedCities.join(","),
        cjrid: this.id,
      };
      let res = await insert_check(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "新增成功",
          type: "success",
        });
        this.adddia = false;
        this.getuser(this.currentPage, this.pageSize);
        this.filepath = "";
        this.resetForm();
      } else {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    // console.log(res, "核稿接口的res");
    //   this.getuser(this.currentPage, this.pageSize);
    //  this.resetForm()
    // },
    handleCheckAllChange(val) {
      this.checkedCities = val ? this.cities : [];
      this.isIndeterminate = false; // 取消不确定状态
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.cities.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.cities.length;
    },
    async onRegionChange(value) {
      this.form.region1 = null;
      // console.log(value, "参数");
      let params = {
        type_id: value,
      };
      // console.log(params, "参数");
      let res = await select_templates_BytypeId(params); // 调用接口发送数据
      this.options1 = res.data.data;
    },
    async onRegionChange1(value) {
      // console.log(value, "参数");
      this.temid = value;
      let params = {
        template_id: value,
      };
      // console.log(params, "参数");
      let res = await select_template_BytemplateId(params); // 调用接口发送数据

      this.character = res.data.data.character;
      this.page_format = res.data.data.page_format;
      this.qtys = res.data.data.qtys;
      this.characters = res.data.data.character;
    },
    async getdocument_type() {
      let res = await getdocument_type();
      //  this.options = responseData;
      this.options = res.data.data;
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    handleCloseadd() {
      this.form = {
        userName: "",
        passWord: "",
        handle_name: "",
        gwmc: "",
        yhlx: "",
      };
      this.adddia = false; // 确保关闭对话框
    },
    handleClosecs() {
      // 关闭对话框时重置表单

      this.formcs = {
        csm: "",
      };
      console.log(this.formcs.csm, "什么玩应");
      this.formcs.csm = "";
      this.adddiacs = false; // 确保关闭对话框
    },
    addcs() {
      let params = {
        csm: this.formcs.csm,
      };
      let res = insert_handle(params);

      // console.log(res,'新增处室接口的res')
      let x = res.then((data) => {
        // console.log(data.data.status_code);
        if (data.data.status_code == 200) {
          this.$message({
            message: "新增处室成功",
            type: "success",
          });
          this.getszcs();

          this.adddiacs = false;
          this.getuser(this.currentPage, this.pageSize);
          this.formcs.csm = "";
          // this.getszcs();
          // this.getuser(this.currentPage, this.pageSize);
        } else {
          this.$message({
            message: "新增处室失败",
            type: "error",
          });
        }
      });
    },
    // },
    clearInput() {
      this.inputvalyhm = ""; // 清空输入框内容
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    handleSelectionChange(selected) {
      // 选择文件的id
      this.selectedUsers = selected.map((user) => user.check_id); // 更新 selectedUsers 数组
    },
    submitForm() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          this.adduser(); // 在验证通过后调用添加模板的函数
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    clickzhgl() {
      this.$router.push("/grzx2");
    },
    // 获取所在处室
    async getszcs() {
      let rescs = await select_handle_all();
      this.departments = rescs.data.data;
    },
    async sea(c4) {
      // let pam = {
      //   yhm: c4,
      //   pageSize: '',
      //   page: '',
      // };
      this.getuser("", "", c4);
      // this.inputvalyhm = "";
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.getuser(this.currentPage, val);
      // console.log(`每页 ${val} 条`);
      this.pageSize = val;
    },
    clickgrzx() {
      this.$router.push("/grzx1");
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.getuser(val, this.pageSize);
      this.currentPage = val;
    },
    async batchDelete() {
      // 检查是否有选中的模板
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择文件！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            check_id: this.selectedUsers, // 将选中的文件名数组发送给后端
          };

          try {
            let res = await delete_check(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.selectedUsers = []; // 清空选中的模板
              this.getuser(1, this.pageSize, this.inputvalyhm); // 刷新模板列表
              this.currentPage = 1; // 刷新当前页
            } else {
              this.$message({
                message: "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },

    tomanage() {
      this.$router.push("/mb_manage");
    },
    changePwd() {
      // alert('sss')
    },
    togrzx() {
      this.$router.push("/grzx1");
    },

    clickzskgl() {
      this.$router.push("/zsk");
    },
    async onSubmit() {
      let params = {
        name: this.username,
        xm: this.form1.xm,
        xb: this.form1.xb,
        age: this.form1.age,
        szdw: this.form1.szdw,
        ssgw: this.form1.ssgw,
        sjgzly: this.form1.sjgzly,
        ssqy: this.form1.ssqy,
      };
      let res = await js(params);
      this.dialogVisible = false;
      // this.getuser();
      this.getcyt();
      this.$message({
        message: "修改成功",
        type: "success",
      });
      // this.getuser();
    },
    async scfw() {
      if (!this.formData) {
        console.error("没有文件被上传");
        return;
      }
      try {
        let res = await upload_check_file(this.formData); // 直接传递 formData 进行上传
        if (res.data.status_code == 200) {
          this.$message({
            message: "上传成功",
            type: "success",
          });
          this.filepath = res.data.data;
        } else {
          this.$message({
            message: "上传失败",
            type: "error",
          });
        }
      } catch (error) {
        console.error("上传失败:", error);
      }
    },
    beforeAvatarUpload(files) {
      //  this.fileList.push(files); // 将文件添加到文件列表中
      this.fileList = [files]; // 只保留当前上传的文件
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "check_file",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          this.formData = formData; // 将 formData 存储在组件的属性中

          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },

    togrzx() {
      this.$router.push("/grzx1");
    },
    togrzx() {
      // alert(1)
      this.$router.push("/grzx1");
    },
    async get_write() {
      let params = {
        name: this.username,
      };
      let res2 = await getwrite(params);
      // console.log(res2.data.data.szqt, "受众群体");
      this.tags = res2.data.data.jstd
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
      this.tags1 = res2.data.data.wzfg
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
      this.tags2 = res2.data.data.szqt
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
    },

    showContextMenu(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener("click", this.hideContextMenu);
      }
    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener("click", this.hideContextMenu);
    },

    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "************************************");
      // this.textarea3 = html;
      // return html;
    },
    getPlainText(html) {
      const div = document.createElement("div");
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    },
    //     getHtmlContent(html) {
    //   const div = document.createElement('div');
    //   div.innerHTML = html;
    //   return div.innerHTML;
    // },

    onCopySuccess() {
      console.log("复制成功");
    },
    onCopyError() {
      console.log("复制失败");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    ignore() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
    },
    highlightChange(text1, text2) {
      console.log(this.textarea4);
      let changeData = text2;
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.textarea4 = this.removetext(this.textarea4);
      this.textarea4 = this.textarea4.replace(text1, changeData);
    },
    highlightError(error, word) {
      this.textarea4 = this.removetext(this.textarea4);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.textarea4 = this.textarea4.replace(error, a);
      console.log(this.textarea4);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(this.$refs.editor.$el, "editorIframe");
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    removetextp(text) {
      let str = text;
      let reg1 = new RegExp(`<p>`, "g");
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</p>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    onCopySuccess() {
      this.$message({
        message: "内容已复制到剪贴板！",
        type: "success",
      });
    },
    onCopyError() {
      this.$message({
        message: "复制失败，请手动复制!",
        type: "warning",
      });
    },

    async jiaoyan() {
      this.textarea4 = this.removetext(this.textarea4);
      this.con_loading = true;
      // let text = this.removetextp(this.textarea4)
      testcheck({
        test_all: this.textarea4,
      }).then(async (data) => {
        this.correctionList = data.data.data;
        this.con_loading = false;
        if (this.correctionList.length == 0) {
          this.jydis = true;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          console.log(data, "619");
          this.jydis = false;
        }
      });
    },
    handleClosedrawer() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    // async jiaoyan(){
    //   this.jydis = false
    // },
    save() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.jydis = true;
    },
    fanhui() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    handleStepClick(index) {
      this.lock++;
      this.activeIndex = index; // 更新activeIndex的值
    },
    upShow() {
      this.navShow = !this.navShow;
    },
    async textareaFocus() {
      let data = await getLabel1();
      this.gzbj = data.data;
      this.dialogShow = true;
    },
    closeDialog() {
      this.dialogShow = false;
    },
    textarea2Focus() {
      this.dialog2Show = true;
    },
    closeDialog2() {
      this.dialog2Show = false;
    },
    async getText(e) {
      console.log(e);
      let params = {
        label: e,
      };
      let data = await getLabel2(params);
      console.log(data, "583");
      this.secRecommend = data.data;
      this.textarea = e;
      this.textarea2 = [];
    },
    getText2(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea2.push(e);
        this.dynamicTags.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm(e);
      } else {
        console.log(111);
      }
    },
    getText21(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea21.push(e);
        this.dynamicTags1.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm1(e);
      } else {
        console.log(111);
      }
    },
    getText22(e) {
      if (!this.dynamicTags2.includes(e)) {
        this.textarea22.push(e);
        this.dynamicTags2.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm2(e);
      } else {
        console.log(111);
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickszbg() {
      this.$router.push("/xz");
    },

    //角色
    clickTopld() {
      this.$router.push("/grzx");
    },
    clickTopLd() {
      this.$router.push("/sfw");
    },
    //   提示词
    clickToptsc() {
      this.$router.push("/tsc");
    },
    firstNextStep() {
      if (this.textarea == "") {
        this.$notify({
          title: "提示",
          message: "请填写工作背景",
          type: "warning",
        });
      } else if (this.textarea2.length == 0) {
        this.$notify({
          title: "提示",
          message: "请填写工作要点关键词",
          type: "warning",
        });
      } else {
        this.mask = true;
        if (this.mask == true) {
          this.getInfo(this.textarea, this.dynamicTags);
        }
      }
    },
    async getInfo(c1, c2) {
      this.loading = true;
      let params = {
        test_1: c1,
        test_2: c2,
      };
      let res = await getSecDtDatas(params);
      // console.log(res.data.status_code,'我是状态码11111')
      // console.log(res.data.message,'我是提示33332222')
      if (res.data.status_code == 200) {
        // 弹出提示
        this.$message({
          message: res.data.message,
          type: "success",
        });
        this.textarea3 = "";

        // console.log(res.data, 55555555);
        this.textarea3 = res.data.data;
        // console.log(this.textarea3,66666);
        // 这里放置需要执行的逻辑或调用其他方法
        this.buttonShow = true;
        // this.stepActived = 2;
        this.currentStep = 2;
        this.loading = false;
        this.mask = false;
        this.curIndex = "2";
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10001) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10002) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    repeatStep() {
      this.loading = true;
      this.mask = true;
      if (this.mask == true) {
        this.getInfo(this.textarea, this.dynamicTags);
      }
    },
    success() {
      this.artShow = true;
      this.loading = true;
      // 这里放置需要执行的逻辑或调用其他方法
      if (this.textarea3 == "") {
        this.$notify({
          title: "提示",
          message: "大纲内容为空，无法生成",
          type: "warning",
        });
      } else {
        this.progressPercent = 0;
        this.getSuccessInfo(this.textarea3, this.textarea);
      }
    },
    // 依次请求接口
    async fetchResData(index, nextIndex) {
      if (index >= this.fetchResPrams.length) {
        this.fetchData(nextIndex);
        return;
      }
      console.log(this.fetchResPrams[index], "675");
      let pams = {
        string: this.fetchResPrams[index],
        id_name: this.textarea,
      };
      const response = await getSuccessInfoItem(pams);
      if (response.data.status_code == 200) {
        // this.$message({
        //   message: response.data.message,
        //   type: 'success'
        // });
        this.textarea4 += response.data.data;

        let totalLength = 0;
        let allSubArrays = this.resDataItem.filter(
          (item) => typeof item != "string"
        );
        allSubArrays.forEach((subArray) => {
          totalLength += subArray.length;
        });
        let kuai = 100 / totalLength;
        this.progressPercent += Math.floor(kuai);
        await this.fetchResData(index + 1, nextIndex);
      } else if (response.data.status_code == 500) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10001) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10002) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      }
    },
    // 一段式生成
    async fetchData(index) {
      if (index >= this.resDataItem.length) {
        this.progressPercent = 100;
        this.maskAll = false;
        this.buttonShow = false;
        return;
      }
      if (typeof this.resDataItem[index] == "string") {
        this.textarea4 += this.resDataItem[index];
        await this.fetchData(index + 1);
      } else {
        this.fetchResPrams = this.resDataItem[index];
        this.fetchResData(0, index + 1);
      }
      // this.stepActived = 3;
      this.currentStep = 3;
      this.loading = false;
    },
    async getSuccessInfo(c1, c2) {
      this.loading = true;
      this.maskAll = true;
      let params = {
        string: c1,
        work: c2,
      };
      let res = await getSuccessInfo(params);
      if (res.data.status_code == 200) {
        // this.$message({
        //   message: res.data.message,
        //   type: 'success'
        // })
        this.textarea4 = "";
        this.resDataItem = res.data.data;
        this.fetchData(0);
        this.currentStep = 3;
        // this.stepActived = 3;
        this.loading = false;
        // }
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    clickTab(i) {
      this.curIndex = i;
      // }
    },
    handleCheck(selected, checked) {
      this.selectedNodes = checked;
      const handleIds = this.selectedNodes.checkedNodes.map(
        (node) => node.handleId
      );
      this.select_handlespeo = handleIds;
    },
  },
  // watch: {
  //   xb(newVal) {
  //     if (newVal) {
  //       this.backgroundImage = "../img/bga.png"; // 男背景图
  //     } else {
  //       this.backgroundImage = "../img/hahu3.png"; // 女背景图
  //     }
  //   },
  // },
     watch: {
    '$route'(to, from) {
      // 路由变化时调用获取数据的方法
      // this.getwz1();
    this.getuser(1, 10);

  


    },

   },
};
</script>
<style lang="less" scoped>
.dChangePwd .footerInline .tsFont {
  width: 100%;
  line-height: 30px;
  margin-top: 0;
  color: #f60;
}

.ml {
  margin-left: 100px;
}
.ml1 {
  margin-left: 120px;
}
.ml2 {
  margin-left: 100px;
}
::v-deep .el-descriptions-item__label.has-colon::after {
  content: "";
  margin-top: 20px;
}

.el-tag {
  background: #f5f8fc;
  border-radius: 12px;
  padding: 4px;
  // padding-left: 2px;
  // padding-right: 5px;
  // height: 32px;
  // line-height: 32px;
}

.btnr {
  margin-top: 190px;
}
.btn-group {
  background: url(../img/bga.png) no-repeat center;
  background-size: 45%;
}
.btn-group1 {
  // margin-right: 500px;
  background: url(../img/hahu3.png) no-repeat center;
  background-size: 45%;
}
// .btn-group1{
//     background: url(../img/hahu3.png) no-repeat left;
//     background-size: 40%;
//     margin-left: -500px;
// }
// .btn-group {
//   // background: url(../img/bga.png) no-repeat center;
//   background: url(this.backgroundImage) no-repeat center;
//   background-size: 45%;
// }
.left1 {
  background: #ffffff;
  border-radius: 8px;
}
.next_but {
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  width: 130px;
  height: 40px;
  border-radius: 20px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 600;
  &:hover {
    color: #ffffff;
  }
  &:active {
    color: #ffffff;
  }
}
.right {
  background: url(../img/bga.png) no-repeat center;
  // width: 88%;
  // height: 100%;
  background-size: 70%;
  //  background: #FFFFFF;
  // border-radius: 8px;
}
.el-select {
  width: 100%;
}
// .el-input {
//   width: 600px;
//   margin-left: -120px;
// }
.fir-kuai2 {
  margin-top: px;
  margin-right: 10px;

  width: 6px;
  height: 16px;
  float: left;
  background: #4081ff;
  border-radius: 1.5px;
}

.el-upload__tip {
  margin-top: -20px;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}
// .avatar-uploader .el-upload {
//   border: 1px #d9d9d9 dashed !important;
//   border-radius: 6px;
//   cursor: pointer;
//   position: relative;
//   overflow: hidden;
// }
.parent-container .avatar-uploader .el-upload {
  border: 1px #d9d9d9 dashed !important;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: calc(100vw * 28 / 1920);
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.ai-left-bar-li {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  width: 1000px;
  // color: #000;

  line-height: 16px;
  white-space: nowrap;
  cursor: pointer;
  // z-index: 9999;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 19 / 1920);
  // color: #000000;
  letter-spacing: 0;
  text-align: center;
  // background: url(../assets/jstext.png) no-repeat center;
}

.actived {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  // color: #fff;
}
.context-menu {
  margin-left: -200px;
  position: absolute;
  // background: white;
  background: #fff;

  // border: 1px dashed #ccc;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
  // margin-left:-200px;
}

.context-menu li {
  padding: 8px 16px;
  cursor: pointer;
  // margin-left:-200px;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}

.fr {
  float: right;
}

.work-container {
  height: 100%;

  .result-container {
    padding: 30px;

    .elcol-title {
      display: flex;
      overflow: hidden;
      width: 100%;
      height: 48px;

      .elcol-title-text {
        // float: left;
        padding-left: 10px;
        text-align: left;
        width: 40%;
        height: 100%;
      }

      .elcol-title-text2 {
        font-size: calc(100vw * 14 / 1920);
        // color: #d1d7de;
        // float: left;
        width: 75px;
      }

      .elcol-input {
        float: left;
        width: calc(60% - 75px);
        border: none !important;
      }
    }
  }
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

.ejdhl {
  margin-top: 43px;
  background-color: #fff;
  height: 900px;
  //  background: url(../assets/img-left.png) no-repeat center;
  // background-size: cover;
  // background-position: center;
  // transition: ease-out 0.4s;
}

.ejdhlTitle {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(
    76deg,
    #07389c 0%,
    #3d86d1 0%,
    #3448b3 100%
  );

  img {
    width: 120px;
    height: 48px;
  }

  p {
    border: 1.54px dashed rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-main {
  background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100% - 80px);
  position: absolute;
  right: 0;
  top: 80px;
  padding: 20px 10px;
  // overflow: hidden;
}

::v-deep(.el-textarea__inner) {
  padding: 16px;
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: 10px;
  height: 10px;
}

.ai-nav {
  // width: 180px;
  // height: 100%;
  height: calc(100% - 80px);
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  transition: ease-out 0.4s;

  .title {
    width: 300px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    letter-spacing: 0px;
    height: 20px;
    line-height: 20px;
    padding-top: 20px;
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    width: 100%;
    padding-top: 45px;
    height: calc(100% - 80px);
  }
}

.user-menu-content {
  width: 246px;
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px dashed rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: 16px;
        font-size: calc(100vw * 15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw * 12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px dashed #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;
      cursor: pointer;

      :first-child {
        margin-top: 0px;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

// 导航项
.nav-item {
  height: 40px;
  background: transparent;
  line-height: 20px;
  white-space: pre-wrap;
  // background-image: linear-gradient(270deg,
  //     rgba(30, 75, 202, 0.39) 0%,
  //     rgba(59, 130, 234, 0.28) 100%);
  // border: 1px dashed rgba(255, 255, 255, 0.2);
  // border-radius: 4px;
  background: #f4f6f8;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
  cursor: pointer;
  // margin: 0 auto;
  margin-top: 20px;
  margin-left: 100px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #9094a5;
  border-radius: 20px;
  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    letter-spacing: 0;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.choose {
  border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  color: #ffffff;
}

.clbutton {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 40px;
}

.clbutton1 {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}

.clbutton2 {
  left: 180px;
}

.clbutton12 {
  // left: 200px;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 280px;
}

.showUp {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 60px;
  height: 40px;
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.showUp1 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 6px;
  height: 40px;
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 130px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}
::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-drawer__header) {
  display: none;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-notification__group) {
  height: auto !important;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: calc(100vw * 14 / 1920) !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.progressClass {
  width: 100%;
  height: 26px;
  position: fixed;
  bottom: 48px;
  right: 0;
  z-index: 999;

  .progress {
    position: absolute;
    right: 52px;
    transition: ease-out 0.4s;
  }
}

::v-deep(.el-progress-bar__innerText) {
  padding: 7px !important;
  color: #fff !important;
}

.menu_label {
  width: 100%;
  border-radius: 4px;
  margin-top: 10px;
  min-height: 158px;
  height: auto;
  padding: 15px 16px;
  // max-height: 158px;
  background: #f8f9fd;
  border: 1px dashed rgba(229, 232, 245, 1);
  border-radius: 4px;
  padding: 15px 0px;
}

.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: 383px;
  word-wrap: break-word;
  text-align: left;
  margin-left: 5px;
  margin-bottom: 5px;
  float: left;
}

.el-main .ai-body .tab-item-fir .menu_label:active {
  background: #fff;
  border: 1px dashed #4170f6;
}

// .el-main .ai-body .tab-item-fir .menu_label:focus {
//   background: #fff;
//   border: 1px dashed #4170f6;
// }

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px !important;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: 80px;
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vh * 40 / 960);
        height: calc(100vh * 40 / 960);
        border-radius: calc(100vh * 20 / 960);
        background: #f4f6f8;
        border: 1px solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 960);
            width: calc(100vh * 130 / 960);
            color: #000;
            font-size: calc(100vh * 14 / 960);
            line-height: calc(100vh * 16 / 960);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vh * 14 / 960);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vh * 16 / 960);
              height: calc(100vh * 16 / 960);
              margin-right: calc(100vh * 14 / 960);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 960);
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 960);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: 80px;
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}
.pass_input {
  // float: left;
  width: 100%;
  height: 40px;
}

::v-deep(.el-alert__title) {
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-drawer__open .el-drawer.ltr) {
  width: 100% !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0;
}

::v-deep(.el-alert) {
  padding: 8px 8px;
}

.fir-textarea1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: 1px dashed rgba(229, 232, 245, 1);
  border-radius: 4px;
  margin: 20px;
  margin-bottom: 0;
  height: 158px;

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: 13px 18px 33px 16px;
  }
}

::v-deep(.el-collapse) {
  border: 0px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: 1px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: 40px;
  border-radius: 4px;
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: 4px;
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: 4px;
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: -10px;
    top: 2px;
    width: 4px;
    height: 15px;
    background: #5585f0;
    border-radius: 2px;
  }
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: 6px;
  /* 滚动条的宽度 */
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
  /* 滚动条的背景色 */
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
  /* 滚动条的滑块颜色 */
}

.elcol-title-left {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 20px;
  margin-left: 10px;
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 48px;
}

.elcol-title-text {
  // float: left;
  padding-left: 10px;
  text-align: left;
  width: 40%;
  height: 100%;
}

.elcol-title-text2 {
  font-size: calc(100vw * 14 / 1920);
  color: #d1d7de;
  // float: left;
  width: 75px;
}

.elcol-input {
  float: left;
  width: calc(60% - 75px);
  border: none !important;
}

/* 整个滚动条 */
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 10px;
  /* 对应横向滚动条的宽度 */
  height: 10px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: gray;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #fff;
  border-radius: 32px;
}
.el-icon {
  --color: inherit;
  height: 1em;
  width: 1em;
  line-height: 1em;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  fill: currentColor;
  color: var(--color);
  font-size: inherit;
  margin-right: 10px;
}
.el-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  height: calc(100vh * 32 / 1080);
  white-space: nowrap;
  cursor: pointer;
  padding: calc(100vh * 15 / 1080) calc(100vw * 20 / 1920);
  font-size: calc(100vw * 14 / 1920);
  // color: var(--el-button-text-color);
  // text-align: center;
  // box-sizing: border-box;
  // outline: 0;
  // transition: .1s;
  // font-weight: var(--el-button-font-weight);
  // -webkit-user-select: none;
  // -moz-user-select: none;
  // -ms-user-select: none;
  // user-select: none;
  // vertical-align: middle;
  // -webkit-appearance: none;
  // background-color: var(--el-button-bg-color);
  // border: var(--el-border);
  // border-color: var(--el-button-border-color);
  // padding: 8px 15px;
  // font-size: var(--el-font-size-base);
  // border-radius: var(--el-border-radius-base);
}
.el-input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  height: 35px;
  line-height: 35px;
  outline: 0;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}

:deep(.el-dialog__header) {
  padding: 15px 20px 10px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  font-weight: 700;
  // border-bottom: 1px solid var(--vxe-modal-border-color);
  // background-color: var(--vxe-modal-header-background-color);
  border-radius: var(--vxe-border-radius) var(--vxe-border-radius) 0 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
:deep(.el-dialog__title) {
  font-size: calc(100vw * 15 / 1920);
  color: #606266;
}
:deep(.el-form .el-form-item__label) {
  font-weight: 700;
  // margin-right:-100px;
}
:deep(.el-form-item) {
  margin-bottom: 20px;
}
:deep(.el-form-item__label) {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: calc(100vw * 14 / 1920);
  color: #606266;
  line-height: 40px;
  box-sizing: border-box;
  margin-left: 60px;
}
.checkbox-group-container {
  display: flex;
  flex-direction: column; /* 设置为列方向，以实现竖向排列 */
}
.text {
  margin-left: 10px;
  color: #000000d9;
  font-weight: 700;
  font-size: calc(100vw * 14 / 1920);
  letter-spacing: 0;
  line-height: 22px;
  cursor: pointer;
}
.ai-left-bar-ul {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 100%;

  .ai-left-bar-li {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh * 42 / 960);
    width: calc(100vh * 130 / 960);
    color: #000;
    font-size: calc(100vh * 14 / 960);
    line-height: calc(100vh * 16 / 960);
    white-space: nowrap;
    cursor: pointer;
    z-index: 9999;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vh * 14 / 960);
    color: #000000;
    letter-spacing: 0;
    text-align: center;

    img {
      width: calc(100vh * 16 / 960);
      height: calc(100vh * 16 / 960);
      margin-right: calc(100vh * 14 / 960);
    }

    &:hover {
      background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
      box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
      border-radius: calc(100vh * 20 / 960);
      color: #fff;
    }
  }

  .actived {
    background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
    border-radius: calc(100vh * 20 / 960);
    color: #fff;
  }
}
:deep(.el-checkbox__label) {
  display: inline-block;
  padding-left: 10px;
  line-height: 19px;
  font-size: calc(100vw * 14 / 1920);
  margin-right: 655px;
}

::v-deep .el-table .el-table__cell {
  padding: calc(100vh * 12 / 1080) 0;
  font-size: calc(100vw * 14 / 1920);
}
::v-deep(.el-table .cell) {
  line-height: calc(100vh * 23 / 1080) !important;
  padding-left: calc(100vw * 10 / 1920) !important;
  padding-right: calc(100vw * 10 / 1920) !important;
}
</style>