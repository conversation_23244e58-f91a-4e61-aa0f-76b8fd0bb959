<!-- 本页为角色页，点击跳转到写作、提示词页 -->
<template>
  <div class="work-container" v-loading="con_loading">
    <el-container>
      <div :style="navShow ? 'width: -10px' : 'width: 10px'" class="ejdhl">
        <div class="ai-nav" :style="navShow ? 'width: 300px' : 'width: 64px'">
          <div class="nav-list">
            <div
              class="nav-item"
              :style="navShow ? 'width: 120px' : 'width: 50px'"
              @click="clickTopLd()"
            >
              <img src="../assets/icon201a.png" alt="" />
              <p v-if="navShow">角色</p>
            </div>
            <div
              class="nav-item choose"
              :style="navShow ? 'width: 120px' : 'width: 50px'"
            >
              <img src="../assets/icon202b.png" alt="" />
              <p v-if="navShow">写作</p>
            </div>
            <div
              v-if="currentStep == 3"
              class="nav-item"
              :style="navShow ? 'width: 120px' : 'width: 50px'"
              @click="clickTopwd()"
            >
              <img src="../assets/icon204a.png" alt="" />
              <p v-if="navShow">进入系统</p>
            </div>
          </div>
        </div>
      </div>
      <el-header class="el-header1">
        <div class="ai-header">
          <div class="flex">
            <div class="ai-left-bar-li actived"></div>
          </div>
        </div>
      </el-header>
      <el-main
        style="transition: ease-out 0.4s; display: flex"
        :style="
          navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
        "
      >
     <div class="left" style="width: 40%;overflow-y:scroll;margin-right: 100px">

                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">填写文章风格</p>
                  </div>
                  <div class="menu_label" @click="textarea2Focus">
                    <el-tag :key="tag" type="info" v-for="tag in dynamicTags" closable :disable-transitions="false"
                      @close="handleClose(tag)">
                      {{ tag }}
                    </el-tag>
                    <el-input class="input-new-tag pass_input" v-if="inputVisible" v-model="inputValue"
                      ref="saveTagInput" size="small" placeholder="参考：叙事风格、议论风格、说明风格等"
                      @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
                    </el-input>
                  </div>

                  <div class="ai-dialog" v-if="dialog2Show">
                    <div class="ai-d-title">
                      <p class="ai-d-title-p">
                        请填写您想要的文章风格，建议您直接选择或参考推荐词汇，生成效果更佳
                      </p>
                      <img src="../assets/close.png" @click="closeDialog2" alt="" />
                    </div>
                    <div class="ai-d-body">
                      <div class="hints-control">
                        <div class="hint-icon hot"></div>
                        <div class="hint-description">智能推荐</div>
                      </div>
                      <div class="ai-tj-body">
                        <p v-for="(item, index) in secRecommend" :key="index" @click="getText2(item)" class="ai-tj-item"
                          :title="item">
                          {{ item }}
                        </p>
                      </div>
                    </div>
                  </div>
                               <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">填写句式特点</p>
                  </div>
                  <div class="menu_label" @click="textareaFocus">
                    <el-tag :key="tag" type="info" v-for="tag in dynamicTags1" closable :disable-transitions="false"
                      @close="handleClose1(tag)">
                      {{ tag }}
                    </el-tag>
                    <el-input class="input-new-tag pass_input" v-if="inputVisible" v-model="inputValue1"
                      ref="saveTagInput" size="small" placeholder="参考：并列句、简单句、排比句等"
                      @keyup.enter.native="handleInputConfirm1" @blur="handleInputConfirm1">
                    </el-input>
                  </div>

                  <div class="ai-dialog" v-if="dialogShow">
                    <div class="ai-d-title">
                      <p class="ai-d-title-p">
                        请填写您想要的句式特点，建议您直接选择或参考推荐词汇，生成效果更佳
                      </p>
                      <img src="../assets/close.png" @click="closeDialog4" alt="" />
                    </div>
                    <div class="ai-d-body">
                      <div class="hints-control">
                        <div class="hint-icon hot"></div>
                        <div class="hint-description">智能推荐</div>
                      </div>
                     <div class="ai-tj-body">
                        <p v-for="(item, index) in secRecommend1" :key="index" @click="getText21(item)" class="ai-tj-item"
                          :title="item">
                          {{ item }}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">填写受众群体</p>
                  </div>
                  <div class="menu_label" @click="textarea3Focus">
                    <el-tag :key="tag" type="info" v-for="tag in dynamicTags2" closable :disable-transitions="false"
                      @close="handleClose2(tag)">
                      {{ tag }}
                    </el-tag>
                    <el-input class="input-new-tag pass_input" v-if="inputVisible" v-model="inputValue2"
                      ref="saveTagInput" size="small" placeholder="参考：个人、企事业单位、政府机构等"
                      @keyup.enter.native="handleInputConfirm2" @blur="handleInputConfirm2">
                    </el-input>
                  </div>

                  <div class="ai-dialog" v-if="dialog3Show">
                    <div class="ai-d-title">
                      <p class="ai-d-title-p">
                        请填写您想要的受众群体，建议您直接选择或参考推荐词汇，生成效果更佳
                      </p>
                      <img src="../assets/close.png" @click="closeDialog5" alt="" />
                    </div>
                    <div class="ai-d-body">
                      <div class="hints-control">
                        <div class="hint-icon hot"></div>
                        <div class="hint-description">智能推荐</div>
                      </div>
                 <div class="ai-tj-body">
                        <p v-for="(item, index) in secRecommend2" :key="index" @click="getText22(item)" class="ai-tj-item"
                          :title="item">
                          {{ item }}
                        </p>
                      </div>
                    </div>
                  </div>
                    <el-button
                type="primary"
                @click="ok(), getcyt(),textareaFocus666()"
                style="
                margin-right: 160px;
margin-top: 20px;
                  border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                "
                >下一步：生成词云</el-button
              >
 <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">上传范文</p>
                  </div>
          <div style=" width: 70%; display: flex; justify-content: space-between; margin-top: 20px">
            <div style="margin-left: 20px">
          
              <el-upload
                class="upload-demo"
                multiple
                :before-upload="beforeAvatarUpload"
                ref="upload"
                action="#"
                  :show-file-list="false"  
                     accept=".doc,.docx,.pdf,.ofd,.txt"
              >
                <el-button
                  slot="trigger"
                  size="small"
                  type="primary"
                  style="
                      border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                  "
                  >述职报告</el-button
                >
              </el-upload>
            </div>
            <div>
          
              <el-upload
                class="upload-demo"
                multiple
                :before-upload="beforeAvatarUpload1"
                ref="upload"
                action="#"
                  :show-file-list="false"  
                     accept=".doc,.docx,.pdf,.ofd,.txt"
              >
                <el-button
                  slot="trigger"
                  size="small"
                  type="primary"
                  style="
                   border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                  "
                  >心得体会</el-button
                >
              </el-upload>
            </div>
                     <div>
         
              <el-upload
                class="upload-demo"
                multiple
                :before-upload="beforeAvatarUpload2"
                ref="upload"
                action="#"
                  :show-file-list="false"  
                     accept=".doc,.docx,.pdf,.ofd,.txt"
              >
                <el-button
                  slot="trigger"
                  size="small"
                  type="primary"
                  style="
                    border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                  "
                  >领导讲话</el-button
                >
              </el-upload>
            </div>
            </div>
          <div style=" width: 70%; display: flex; justify-content: space-between; margin-top: 20px">           
            <div style="margin-left: 20px">
              <el-upload
                class="upload-demo"
                multiple
                :before-upload="beforeAvatarUpload3"
                ref="upload"
                action="#"
                  :show-file-list="false"  
                     accept=".doc,.docx,.pdf,.ofd,.txt"
              >
                <el-button
                  slot="trigger"
                  size="small"
                  type="primary"
                  style="
                    border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                  "
                  >工作方案</el-button
                >
              </el-upload>
            </div>
                     <div>
          
              <el-upload
                class="upload-demo"
                multiple
                :before-upload="beforeAvatarUpload4"
                ref="upload"
                action="#"
                  :show-file-list="false"  
                     accept=".doc,.docx,.pdf,.ofd,.txt"
              >
                <el-button
                  slot="trigger"
                  size="small"
                  type="primary"
                  style="
 border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                  "
                  >调研报告</el-button
                >
              </el-upload>
            </div>
                     <div>
           
              <el-upload
                class="upload-demo"
                multiple
                :before-upload="beforeAvatarUpload5"
                ref="upload"
                action="#"
                  :show-file-list="false"  
                     accept=".doc,.docx,.pdf,.ofd,.txt"
              >
                <el-button
                  slot="trigger"
                  size="small"
                  type="primary"
                  style="
                    border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
                  "
                  >宣传材料</el-button
                >
              </el-upload>
                     </div>
            </div>
          </div> 
          <!-- <div
          style="width: 50%;margin-top: 120px;"
          >
     <video controls autoplay muted width="100%">
      <source src="../assets/shi.mp4" type="video/mp4">
      您的浏览器不支持 video 标签。
    </video>
          </div> -->
        <div
     
          class="right1"
          style="width: 50%; display: flex; flex-direction: column;background-color: #fff;"
        >
          <div class="r1" style="flex: 1">
            <custom-steps
              :steps="steps"
              :current-step="currentStep"
            ></custom-steps>
          </div>
          <div class="xh" style="flex: 5" v-if="currentStep == 2&&this.xb=='男'"></div>
          <div class="xh_nv" style="flex: 5" v-if="currentStep == 2&&this.xb=='女'"></div>

          <div class="r2" style="flex: 5" v-if="currentStep == 3">
            <Wordcloud></Wordcloud>
          </div>
          <div
            class="r11"
            style="flex: 2; display: flex"
            v-if="currentStep == 3"
          >
            <div style="flex: 1" class="btnr"></div>
            <div v-if="this.xb=='男'" style="flex: 1.5" class="btn-group"></div>
            <div v-if="this.xb=='女'" style="flex: 1.5" class="btn-group_nv"></div>

          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import Wordcloud from "../components/Wordcloud.vue";
import { VueEditor } from "vue2-editor";
import CustomSteps from "../components/Step.vue";
//   import store from '../store/index'
import { mapState } from "vuex";
// ****************************
import Quill from "quill";
let BlockEmbed = Quill.import("blots/block/embed");
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute(
      "style",
      "padding: 20px; border: 1px solid #488aff; color:red; position: relative;"
    );
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement("button");
    insertButton.innerHTML = "应用";
    insertButton.className = "btn-insert";
    insertButton.style.position = "absolute";
    // 按钮颜色
    insertButton.style.backgroundColor = "#488aff";
    // 字体为白色
    insertButton.style.color = "white";
    // insertButton.style.top = '10px';
    insertButton.style.right = "70px";
    insertButton.addEventListener("click", () => {
      if (value.flag == 1) {
        value.editorClass.insertText(
          value.Position + value.selectedTextlength,
          value.newText
        );
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement("button");
    cancelButton1.innerHTML = "取消";
    cancelButton1.className = "btn-cancel";
    cancelButton1.style.position = "absolute";
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = "#e64721";
    // 字体为白色
    cancelButton1.style.color = "white";
    cancelButton1.style.right = "10px";
    cancelButton1.addEventListener("click", () => {
      node.remove();
      // alert('取消按钮点击');
      // // 这里可以添加取消按钮的逻辑
    });

    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = "customBlock";
CustomBlock.tagName = "div";

Quill.register({
  "formats/customBlock": CustomBlock,
});
import store from "../store/index";
import {
  
  getSecDtDatas,
  getSuccessInfo,
  getSuccessInfoItem,
  getLabel1,
  getLabel2,
  getLabel333,
  testcheck,
  bcwz,
  rewrite,
  xz,
  cyt,
  fwsc,
  getlable3,
  getlable4,
  getlable5,
} from "../api/home.js"; // 接口请求
 export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id
    datas() {
      return this.$store.state.datas;
    },
    username() {
      return this.$store.state.username;
    },
    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 600}px`,
      };
    },
    id() {
      return this.$store.state.id1;
    },
    xb(){
      return this.$store.state.xb;

    },
  },
  data() {
    return {
      
      files: [],
      wcdata: [],
      // 定义变量存词云的返回值，然后用vuex传给词云组件
      fzqwnr: "",
      newText: "",
      newText1: "",
      newText2: "",
      newText3: "",
      xxdialog: false,
      xxcontent: "",
      kxdialog: false,
      sxdialog: false,
      rsdialog: false,
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      selectedText: "",
      con_loading: false,
      loading1: false,
      correctionList: [],
      jydis: true,
      navShow: true,
      navShowwd: false,
      textarea: "",
      textarea2: [],
      textarea3: "",
      textarea4: "",
      textarea4: "",
      dialogShow: false,
      dialog2Show: false,
      dialog3Show: false,
      // gzbj: [],
      // gzbj1: [],
      // gzbj2: [],
      // stepActived: 1,
      loading: false,
      curIndex: "2",
      articles: "",
      artShow: false,
      mask: false,
      maskAll: false,
      /**
       * @description:  功能性按钮
       * @return {*}    传入组件->FunctionTable
       */
      steps: [
        { title: "录入角色" },
        { title: "录入写作风格" },
        { title: "生成词云" },
      ],
      currentStep: 2, // 初始化当前步骤为第二个步骤
      secRecommend: [],
      secRecommend1: [],
      secRecommend2: [],

      dynamicTags: [],
      dynamicTags1: [],
      dynamicTags2: [],

      inputVisible: true,
      inputValue: "",
      inputValue1: "",
      inputValue2: "",
      resDataItem: [],
      progressPercent: 0,
      fetchResPrams: [],
      buttonShow: true,
      drawer: false,
      direction: "ltr",
      activeNames: [],
      ruleForm: {
        name: "",
        region: "",
        date1: "",
        date2: "",
        delivery: false,
        type: [],
        type1: [],
        type2: [],
        resource: "",
        desc: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
        ],
        region: [
          { required: true, message: "请选择活动区域", trigger: "change" },
        ],
        date1: [
          {
            type: "date",
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        date2: [
          {
            type: "date",
            required: true,
            message: "请选择时间",
            trigger: "change",
          },
        ],
        type: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个活动性质",
            trigger: "change",
          },
        ],
        resource: [
          { required: true, message: "请选择活动资源", trigger: "change" },
        ],
        desc: [{ required: true, message: "请填写活动形式", trigger: "blur" }],
      },
    };
  },
  components: {
    // 注册组件
    CustomSteps,
    VueEditor,
    Wordcloud,
  },
  mounted() {

  },
          methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
     togrzx() {
      this.$router.push("/grzx1");
    },
     async textareaFocus666() {
      let params = {
        username: this.username,
        // username:'user'
      }
      let data = await getLabel333(params);
      // this.gzbj = data.data;
      // this.dialogShow = true;
    },
     togrzx() {
      this.$router.push("/grzx1");
    },  togrzx() {
      // alert(1)
      this.$router.push("/grzx1");
    },
       async textarea2Focus() {
         let data = await getlable3();
      this.gzbj = data.data;
      this.secRecommend = data.data;

      this.dialog2Show = true;
    },
     async textareaFocus() {
      let data = await getlable4();
      this.gzbj = data.data;
      this.secRecommend1 = data.data;

      this.dialogShow = true;
    },
      async textarea3Focus() {
      let data = await getlable5();
      this.gzbj = data.data;
      this.secRecommend2 = data.data;

      this.dialog3Show = true;
    },
    beforeAvatarUpload(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "述职报告"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload1(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "心得体会"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload2(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];
      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "领导讲话"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload3(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "工作方案"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload4(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "调研报告"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload5(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "宣传材料"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    async scfw() {
      console.log("FormData123456:", this.formData.getAll("files[]"));
      if (!this.formData) {
        console.error("没有文件被上传");
        return;
      }
      try {
        let res = await fwsc(this.formData); // 直接传递 formData 进行上传
        console.log("w看一下返回值结果:", res.data.status_code);
        if(res.data.status_code==200){
          this.$message({
            message: "上传成功",
            type: "success",
          });
        }
        else{
          this.$message({
            message: "上传失败",
            type: "error",
          });
        }
        // this.loading = false;
        //         // this.xxdialog=true;
        //         this.xxcontent=this.newText;
        //         // this.kxdialog=true;
        //         // this.sxdialog=true;
        //         // this.rsdialog=true;
        //         // this.textarea2=res.data.data;
        //         // this.textarea3=res.data.data;
        //         // this.textarea4=res.data.data;
        //         // this.textarea5=res.data.data;
        //         // this.textarea6=res.data.data;
        //         // this.textarea7=res.data.data;
        //         // this.textarea8=res.data.data;
        // console.log("上传结果:", res);
      } catch (error) {
        console.error("上传失败:", error);
      }
    },

 

    async ok() {
      console.log("我是id:", this.id);
      // 智能推荐标签那三个数组传过去
      let params1 = {
        username: this.username,
        wzfg: this.dynamicTags,
        jstd: this.dynamicTags1,
        szqt: this.dynamicTags2,
      };
      // console.log("智能推荐标签参数416:", params1);
      let res = await xz(params1);
    },
   async getcyt() {
      this.currentStep = 3;
      let params = {
        name: this.username,
      };
      let res = await cyt(params);
      this.wcdata = res.data.data;
      console.log("词云数据:", this.wcdata);
      this.$store.dispatch("updatecy", this.wcdata);
    },
    showContextMenu(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener("click", this.hideContextMenu);
      }
    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener("click", this.hideContextMenu);
    },
    // 续写接口
    async sendToBackend() {
      this.loading1 = true;
      this.mask = true;
      let params1 = {
        text: this.selectedText,
        flag: "1",
      };
      let res = await rewrite(params1);
      this.newText = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "续写成功",
          type: "success",
        });
        // 注册自定义样式
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();
        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);
        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText,
              newText: this.newText,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 1,
            },
            Quill.sources.USER
          );
          // 将后端返回的续写文字插入到该位置后面
          // editor.insertText(position + this.selectedText.length, this.newText);
          // editor.formatText(position + this.selectedText.length, this.newText.length, 'color', 'red');
          // // 将光标移动到新插入的文本后面
          // editor.setSelection(position + this.selectedText.length + this.newText.length, 0);
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText);
          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText.length
          );
          console.log("Formatting text length:", this.newText.length);
          editor.formatText(
            editor.getLength() - this.newText.length,
            this.newText.length
          );

          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "续写失败",
          type: "error",
        });
      }
    },

    // 2.扩写接口
    async sendToBackend2() {
      this.loading1 = true;
      this.mask = true;
      let params2 = {
        text: this.selectedText,
        flag: "2",
      };
      let res = await rewrite(params2);
      this.newText1 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "扩写成功",
          type: "success",
        });
        // this.loading = false;
        //         // this.xxdialog=true;
        //         this.xxcontent=this.newText;
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          const position = allText.indexOf(this.selectedText);

          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText1,
              newText: this.newText1,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 2,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText1);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText1.length
          );
          console.log("Formatting text length:", this.newText1.length);
          editor.formatText(
            editor.getLength() - this.newText1.length,
            this.newText1.length
          );
          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "扩写失败",
          type: "error",
        });
      }
    },
    // 3.缩写接口
    async sendToBackend3() {
      this.loading1 = true;
      this.mask = true;
      let params3 = {
        text: this.selectedText,
        flag: "3",
      };
      let res = await rewrite(params3);
      this.newText2 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "缩写成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText2,
              newText: this.newText2,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 3,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText2);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText2.length
          );
          console.log("Formatting text length:", this.newText2.length);
          editor.formatText(
            editor.getLength() - this.newText2.length,
            this.newText2.length
          );
          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "缩写失败",
          type: "error",
        });
      }
    },
    // 4.润色接口
    async sendToBackend4() {
      this.loading1 = true;
      this.mask = true;
      let params4 = {
        text: this.selectedText,
        flag: "4",
      };
      let res = await rewrite(params4);
      this.newText3 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "润色成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText3,
              newText: this.newText3,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 4,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText3);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText3.length
          );
          console.log("Formatting text length:", this.newText3.length);
          editor.formatText(
            editor.getLength() - this.newText3.length,
            this.newText3.length
          );

          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "润色失败",
          type: "error",
        });
      }
    },
    copyText() {
      // 复制逻辑
      console.log("复制");
    },
    pasteText() {
      // 粘贴逻辑
      console.log("粘贴");
    },
    cutText() {
      // 剪切逻辑
      console.log("剪切");
    },
    // }
    async baocun() {
      console.log(this.$route.query.userName);
      let params = {
        wznr: this.textarea4.replace('"', '/"'),
        wzlx: "述职报告",
        userName: this.username,
      };
      let resbcwz = await bcwz(params);
      //  console.log(resbcwz)
      resbcwz.data.status_code == 200
        ? this.$message({
            message: "保存成功,请前往我的文档查看",
            type: "success",
          })
        : this.$message({
            message: "保存失败",
            type: "error",
          });
    },
    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "************************************");
      // this.textarea3 = html;
      // return html;
    },
    getPlainText(html) {
      const div = document.createElement("div");   
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    },
    //     getHtmlContent(html) {
    //   const div = document.createElement('div');
    //   div.innerHTML = html;
    //   return div.innerHTML;
    // },

    onCopySuccess() {
      console.log("复制成功");
    },
    onCopyError() {
      console.log("复制失败");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    ignore() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
    },
    highlightChange(text1, text2) {
      console.log(this.textarea4);
      let changeData = text2;
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.textarea4 = this.removetext(this.textarea4);
      this.textarea4 = this.textarea4.replace(text1, changeData);
    },
    highlightError(error, word) {
      this.textarea4 = this.removetext(this.textarea4);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.textarea4 = this.textarea4.replace(error, a);
      console.log(this.textarea4);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(this.$refs.editor.$el, "editorIframe");
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    removetextp(text) {
      let str = text;
      let reg1 = new RegExp(`<p>`, "g");
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</p>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    onCopySuccess() {
      this.$message({
        message: "内容已复制到剪贴板！",
        type: "success",
      });
    },
    onCopyError() {
      this.$message({
        message: "复制失败，请手动复制!",
        type: "warning",
      });
    },
    // handleInputConfirm() {
    //   let inputValue = this.inputValue;
    //   if (inputValue) {
    //     this.dynamicTags.push(inputValue);
    //     console.log(this.dynamicTags,'tianshangle1');
    //     this.textarea2 = this.dynamicTags;
    //   }
    //   // this.inputVisible = false;
    //   this.inputValue = "";
    // },
    handleInputConfirm() {
  let inputValue = this.inputValue;
  if (inputValue && !this.dynamicTags.includes(inputValue)) {
    this.dynamicTags.push(inputValue);
    console.log(this.dynamicTags, '已添加标签');
    this.textarea2 = this.dynamicTags;
  }
  this.inputValue = "";
},
     handleInputConfirm1() {
      let inputValue = this.inputValue1;
      if (inputValue) {
        this.dynamicTags1.push(inputValue);
        console.log(this.dynamicTags1,'tianshangle2');
        
        this.textarea21 = this.dynamicTags1;
      }

      this.inputValue1 = "";
    },
     handleInputConfirm2() {
      let inputValue = this.inputValue2;
      if (inputValue) {
        this.dynamicTags2.push(inputValue);
        console.log(this.dynamicTags2,'tianshangle3');

        this.textarea22 = this.dynamicTags2;
      }
      this.inputValue2 = "";
    },
    async jiaoyan() {
      this.textarea4 = this.removetext(this.textarea4);
      this.con_loading = true;
      // let text = this.removetextp(this.textarea4)
      testcheck({
        test_all: this.textarea4,
      }).then(async (data) => {
        this.correctionList = data.data.data;
        this.con_loading = false;
        if (this.correctionList.length == 0) {
          this.jydis = true;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          console.log(data, "619");
          this.jydis = false;
        }
      });
    },
    handleClosedrawer() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    save() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.jydis = true;
    },
    fanhui() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
      handleClose1(tag) {
      this.dynamicTags1.splice(this.dynamicTags1.indexOf(tag), 1);
    },
      handleClose2(tag) {
      this.dynamicTags2.splice(this.dynamicTags2.indexOf(tag), 1);
    },
    handleStepClick(index) {
      this.lock++;
      this.activeIndex = index; // 更新activeIndex的值
    },
    upShow() {
      this.navShow = !this.navShow;
    },
    closeDialog2() {
      this.dialog2Show = false;
    },
       closeDialog4() {
      this.dialogShow = false;
    },
       closeDialog5() {
      this.dialog3Show = false;
    },

    getText2(tag) {
  if (!this.dynamicTags.includes(tag)) {
    this.dynamicTags.push(tag);
    console.log('添加推荐标签:', tag);
  }
  this.handleInputConfirm(); // 这行可能导致重复输入
}
,
     getText21(e) {
      if (!this.dynamicTags1.includes(e)) {
        this.textarea2.push(e);
        this.dynamicTags1.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm1(e);
      } else {
        console.log(111);
      }
    },
         getText22(e) {
      if (!this.dynamicTags2.includes(e)) {
        this.textarea2.push(e);
        this.dynamicTags2.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm2(e);
      } else {
        console.log(111);
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickszbg() {
      this.$router.push("/xz");
    },
clickTopwd() {
  // 创建视频元素
  const video = document.createElement('video');
  video.src = require('../assets/shi.mp4'); // 使用 require 确保路径正确
  video.style.position = 'fixed';
   video.style.position = 'fixed';
  video.style.top = '0';
  video.style.left = '0';
  video.style.width = '100%';
  video.style.height = '100%';
  video.style.zIndex = '9999'; // 确保视频在最上层
  video.style.objectFit = 'cover'; // 确保视频铺满并保持比例
  
  // 将视频添加到页面
  document.body.appendChild(video);

  // 全屏播放视频
  video.play().catch(error => {
    console.error("播放失败:", error);
  });

  // 当视频播放结束后，移除视频并跳转路由
  video.onended = () => {
    document.body.removeChild(video); // 移除视频
    this.$router.push("/sfw"); // 跳转路由
  };

  // 错误处理
  video.onerror = () => {
    console.error("视频加载失败");
    document.body.removeChild(video); // 移除视频
  };
},

    //角色
    clickTopLd() {
      this.$router.push("/grzx");
    },
    //   提示词
    clickToptsc() {
      this.$router.push("/tsc");
    },
    firstNextStep() {
      if (this.textarea == "") {
        this.$notify({
          title: "提示",
          message: "请填写工作背景",
          type: "warning",
        });
      } else if (this.textarea2.length == 0) {
        this.$notify({
          title: "提示",
          message: "请填写工作要点关键词",
          type: "warning",
        });
      } else {
        this.mask = true;
        if (this.mask == true) {
          this.getInfo(this.textarea, this.dynamicTags);
        }
      }
    },
    async getInfo(c1, c2) {
      this.loading = true;
      let params = {
        test_1: c1,
        test_2: c2,
      };
      let res = await getSecDtDatas(params);
      // console.log(res.data.status_code,'我是状态码11111')
      // console.log(res.data.message,'我是提示33332222')
      if (res.data.status_code == 200) {
        // 弹出提示
        this.$message({
          message: res.data.message,
          type: "success",
        });
        this.textarea3 = "";

        // console.log(res.data, 55555555);
        this.textarea3 = res.data.data;
        // console.log(this.textarea3,66666);
        // 这里放置需要执行的逻辑或调用其他方法
        this.buttonShow = true;
        // this.stepActived = 2;
        this.currentStep = 2;
        this.loading = false;
        this.mask = false;
        this.curIndex = "2";
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10001) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10002) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    repeatStep() {
      this.loading = true;
      this.mask = true;
      if (this.mask == true) {
        this.getInfo(this.textarea, this.dynamicTags);
      }
    },
    success() {
      this.artShow = true;
      this.loading = true;
      // 这里放置需要执行的逻辑或调用其他方法
      if (this.textarea3 == "") {
        this.$notify({
          title: "提示",
          message: "大纲内容为空，无法生成",
          type: "warning",
        });
      } else {
        this.progressPercent = 0;
        this.getSuccessInfo(this.textarea3, this.textarea);
      }
    },
    // 依次请求接口
    async fetchResData(index, nextIndex) {
      if (index >= this.fetchResPrams.length) {
        this.fetchData(nextIndex);
        return;
      }
      console.log(this.fetchResPrams[index], "675");
      let pams = {
        string: this.fetchResPrams[index],
        id_name: this.textarea,
      };
      const response = await getSuccessInfoItem(pams);
      if (response.data.status_code == 200) {
        // this.$message({
        //   message: response.data.message,
        //   type: 'success'
        // });
        this.textarea4 += response.data.data;

        let totalLength = 0;
        let allSubArrays = this.resDataItem.filter(
          (item) => typeof item != "string"
        );
        allSubArrays.forEach((subArray) => {
          totalLength += subArray.length;
        });
        let kuai = 100 / totalLength;
        this.progressPercent += Math.floor(kuai);
        await this.fetchResData(index + 1, nextIndex);
      } else if (response.data.status_code == 500) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10001) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10002) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      }
    },
    // 一段式生成
    async fetchData(index) {
      if (index >= this.resDataItem.length) {
        this.progressPercent = 100;
        this.maskAll = false;
        this.buttonShow = false;
        return;
      }
      if (typeof this.resDataItem[index] == "string") {
        this.textarea4 += this.resDataItem[index];
        await this.fetchData(index + 1);
      } else {
        this.fetchResPrams = this.resDataItem[index];
        this.fetchResData(0, index + 1);
      }
      // this.stepActived = 3;
      this.currentStep = 3;
      this.loading = false;
    },
    async getSuccessInfo(c1, c2) {
      this.loading = true;
      this.maskAll = true;
      let params = {
        string: c1,
        work: c2,
      };
      let res = await getSuccessInfo(params);
      if (res.data.status_code == 200) {
        // this.$message({
        //   message: res.data.message,
        //   type: 'success'
        // })
        this.textarea4 = "";
        this.resDataItem = res.data.data;
        this.fetchData(0);
        this.currentStep = 3;
        // this.stepActived = 3;
        this.loading = false;
        // }
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    clickTab(i) {
      // if (this.currentStep != 2) {
      //   this.$notify({
      //     title: "提示",
      //     message: "请先输入工作背景和工作要点生成大纲",
      //     type: "warning",
      //   });
      // } else {
      this.curIndex = i;
      // }
    },
  },
};
</script>
<style lang="less" scoped>
.ai-dialog {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        row-gap: 1px;
        width: 70%;
        height: -moz-fit-content;
        height: 240px;
        padding: 7px;
        box-shadow: 0 20px 40px 4px #e4e4e524;
        margin-top: 10px;
        transition: ease-out 0.4s;
        background: #ace9ff;
        border: 1px solid rgba(90, 206, 255, 1);
        border-radius: 4px;

        .ai-d-title {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          width: 100%;
          height: -moz-fit-content;
          height: fit-content;
          margin: 0;
          padding: 1px 3px 2px 2px;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw *  14 / 1920);
          color: #313733;
          letter-spacing: 0;
          font-weight: 400;

          .ai-d-title-p {
            flex-grow: 1;
            line-height: 16px;
            text-align: left;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #313733;
            letter-spacing: 0;
            font-weight: 400;
            margin-bottom: 10px;
          }

          img {
            width: 18px;
            height: 18px;
            cursor: pointer;
          }
        }

        .ai-d-body {
          width: 100%;
          height: calc(100% - 44px);
          overflow: hidden;
          background: #ffffff;
          border-radius: 4px;

          .hints-control {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
            padding: 14px 14px 0;
            height: 30px;


            .hint-icon {
              flex-grow: 0;
              flex-shrink: 0;
              width: 20px;
              height: 20px;
              margin-right: 6px;
              background-size: contain;
              background-image: url("../assets/icon_fire.png");
            }

            .hint-description {
              font-weight: 600;
              line-height: 14px;
              font-family: SourceHanSansSC-Bold;
              font-size: calc(100vw *  14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 700;
            }
          }

          .ai-tj-body {
            width: 100%;
            // height: 100%;
            // overflow: hidden;
            height: 200px;
            overflow-y: auto;

            /* 垂直滚动条 */
            .ai-tj-item {
              padding: 14px 14px 0;
              line-height: 12px;
              width: 50%;
              text-align: left;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              float: left;
              cursor: pointer;
              height: 30px;
              font-family: PingFangSC-Regular;
              font-size: calc(100vw *  14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 400;
              // &:hover {

              // }
            }
          }
        }
      }
.pass_input {
 

  // margin-left: 10px;
  // el-input__inner是el-input的input类名
  & /deep/ .el-input__inner {
    border: none;
    background-color: rgba(122, 151, 255, 0) !important;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  14 / 1920);
    color: #666666;
    letter-spacing: 0;
    font-weight: 400;
  }
}
.menu_label {
  width: 70%;
  border-radius: 4px;
  margin-top: 10px;
  min-height: 120px;
  height: auto;
  padding: 15px 16px;
  // max-height: 158px;
  background: #f8f9fd;
  border: 1px solid rgba(229, 232, 245, 1);
  border-radius: 4px;
  padding: 15px 0px;
}

.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: 383px;
  word-wrap: break-word;
  text-align: left;
  margin-left: 5px;
  margin-bottom: 5px;
  float: left;

}
.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw *  14 / 1920);
  letter-spacing: 0;
  line-height: 16px;
  overflow: hidden;
  margin-top: 20px;
  margin-bottom: 20px;
  height: 20px;

  .fir-kuai {
    width: 6px;
    height: 16px;
    margin-right: 8px;
    float: left;
    // margin-top: 2px;
    background: #4081ff;
    border-radius: 1.5px;
  }

  .fir-title-p {
    line-height: 16px;
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  14 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}
.btnr {
  margin-top: 19D0px;
}
.btn-group {
  background: url(../img/bga.png) no-repeat center;
  background-size: 40%;
}
.btn-group_nv{
  background: url(../img/nv.jpeg) no-repeat center;
  background-size: 40%;
}
.left {
  margin-left: 20px;
  padding-left: 30px;

  background: #ffffff;
  border-radius: 8px;
}
.next_but {
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  width: 130px;
  height: calc(100vh * 40/ 1080);
  border-radius: 20px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 600;
  &:hover {
    color: #ffffff;
  }
  &:active {
    color: #ffffff;
  }
}
.right {
  background: url(../img/bga.png) no-repeat center;
  // width: 88%;
  // height: 100%;
  background-size: 70%;
  //  background: #FFFFFF;
  // border-radius: 8px;
}
.el-select {
  width: 600px;
}
.el-input {
  width: 600px;
}
.fir-kuai1 {
  margin-top: 12px;
  width: 6px;
  height: 16px;
  float: left;
  background: #4081ff;
  border-radius: 1.5px;
}

.el-upload__tip {
  margin-top: -20px;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}
// .avatar-uploader .el-upload {
//   border: 1px #d9d9d9 solid !important;
//   border-radius: 6px;
//   cursor: pointer;
//   position: relative;
//   overflow: hidden;
// }
.parent-container .avatar-uploader .el-upload {
  border: 1px #d9d9d9 solid !important;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: calc(100vw *  28 / 1920);
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.ai-left-bar-li {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh *  50 / 1080);
  width: calc(100vw *  1000/ 1920);
  // color: #000;

  line-height: 16px;
  white-space: nowrap;
  cursor: pointer;
  // z-index: 9999;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  19 / 1920);
  // color: #000000;
  letter-spacing: 0;
  text-align: center;
  background: url(../assets/jstext.png) no-repeat center;
}

.actived {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  // color: #fff;
}
.context-menu {
  margin-left: -200px;
  position: absolute;
  // background: white;
  background: #fff;

  // border: 1px solid #ccc;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
  // margin-left:-200px;
}

.context-menu li {
  padding: 8px 16px;
  cursor: pointer;
  // margin-left:-200px;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}

.fr {
  float: right;
}

.work-container {
  height: 100%;

  .result-container {
    padding: 30px;

    .elcol-title {
      display: flex;
      overflow: hidden;
      width: 100%;
      height: 48px;

      .elcol-title-text {
        // float: left;
        padding-left: 10px;
        text-align: left;
        width: 40%;
        height: 100%;
      }

      .elcol-title-text2 {
        font-size: calc(100vw *  14 / 1920);
        // color: #d1d7de;
        // float: left;
        width: 75px;
      }

      .elcol-input {
        float: left;
        width: calc(60% - 75px);
        border: none !important;
      }
    }
  }
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: 20px;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

.ejdhl {
  margin-top: 43px;
  background-color: #fff;
  height: 900px;
  //  background: url(../assets/img-left.png) no-repeat center;
  // background-size: cover;
  // background-position: center;
  // transition: ease-out 0.4s;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(
    76deg,
    #07389c 0%,
    #3d86d1 0%,
    #3448b3 100%
  );

  img {
    width: 120px;
    height: 48px;
  }

  p {
    border: 1.54px solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header1 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  background: url(../assets/jsbg.png) no-repeat center;
  background-color: #fff;
  .ai-header {
    width: 100%;
    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: 40px;
        height: calc(100vh * 40/ 1080);
        border-radius: 20px;
        // background: #f4f6f8;
        border: 1px solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 42px;
            width: 130px;
            color: #000;
            font-size: calc(100vw *  14 / 1920);
            line-height: 16px;
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: 16px;
              height: 16px;
              margin-right: 14px;
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: 20px;
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw *  12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: calc(100% - 300px);
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 /1080);
  padding: 20px 10px;
  overflow: hidden;

  .ai-gai {
    position: fixed;
    /* top: 0; */
    left: 0;
    bottom: 0;
    height: 91%;
    // background: rgba(122, 151, 255, 0.6);
    z-index: 999;
  }

  .ai-body {
    transition: ease-out 0.4s;
    width: 100%;
    height: 100%;
    // background: red;
    float: left;
    overflow: hidden;

    .ai-body-left {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      width: 444px;
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-left: 10px;
      float: left;

      .ai-body-left-top {
        width: 100%;
        height: calc(100% - calc(100vh * 80 / 1080));
        overflow-y: scroll;
      }

      .ai-body-left-bottom {
        width: 100%;
        height: 90px;

        // padding-top: 30px;
        .ai-body-left-bottom-button {
          height: calc(100vh * 40/ 1080);
          // font-weight: 600;
          font-size: calc(100vw *  14 / 1920);
          letter-spacing: 0px;
          flex-grow: 1;
          color: #fff;
          margin: 0 25px;
          cursor: pointer;
          line-height: calc(100vh * 40/ 1080);
          margin-top: 30px;
          background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 20px;
          font-family: PingFangSC-Regular;
        }
      }

      .ai-body-left-bottom2 {
        width: 100%;
        height: calc(100vh * 80 / 1080);
        padding-top: 30px;

        .repeat {
          width: 188px;
          float: left;
          height: calc(100vh * 40/ 1080);
          font-size: calc(100vw *  14 / 1920);
          flex-grow: 1;
          margin-left: 25px;
          cursor: pointer;
          line-height: calc(100vh * 40/ 1080);
          background: #f4f6f8;
          border: 1px solid rgba(230, 230, 230, 1);
          border-radius: 20px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #666666;
          letter-spacing: 0;
          text-align: center;
          // font-weight: 600;
        }

        .ai-body-left-bottom-button {
          width: 188px;
          float: left;
          height: calc(100vh * 40/ 1080);
          flex-grow: 1;
          margin-left: 14px;
          cursor: pointer;
          line-height: calc(100vh * 40/ 1080);
          background-image: linear-gradient(102deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 20px;
          margin-top: 0px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          // font-weight: 600;
        }
      }

      .ai-step {
        margin-top: 20px;
        width: calc(100% - 40px);
        margin: 0 20px;
        margin-top: 5px;
        height: calc(100vh * 40/ 1080);
      }
    }

    .ai-body-right {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      width: calc(100% - 480px);
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-left: 20px;
      float: left;

      .ai-body-start {
        width: 100%;
        height: 100%;

        .pic_bkg1 {
          // width: calc(100vw * 670 / 1920);
          // height: calc(100vw * 590 / 1920);
          width: 670px;
          height: 590px;
          background: url(../assets/img-bg1.png) no-repeat center;
          // background: url(../assets/img-bg1.png) no-repeat center;

          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 70px;
          position: relative;
        }

        .pic_bkg {
          width: 528px;
          height: 518px;
          // z-index: 15;
          background: url(../assets/img-ai.png) no-repeat center;
          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 118px;
          position: relative;
        }

        .pic_font {
          position: absolute;
          margin-left: auto;
          margin-right: auto;
          left: 0;
          right: 0;
          bottom: 116px;
          width: 255px;
          height: calc(100vh * 40/ 1080);
          border: 1.54px solid rgba(0, 0, 0, 0);
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  28 / 1920);
          color: #000000;
          text-align: center;
          font-weight: 600;
        }

        .title_message {
          position: absolute;
          margin-left: auto;
          margin-right: auto;
          left: 0;
          right: 0;
          bottom: 82px;
          text-align: center;
          line-height: 16px;
          margin-top: 10px;
          height: 22px;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw *  14 / 1920);
          color: #999999;
          letter-spacing: 0;
          font-weight: 400;
        }

        .pic_step {
          width: 551px;
          height: 142px;
          z-index: 15;
          background: url("../assets/pic_step.png");
          background-size: contain;
          margin: auto;
        }
      }

      .ai-body-art {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;

        .over {
          overflow: auto;
        }

        .fir-textarea {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 96.5%;
          background: #f8f9fd;
          border: 1px solid rgba(229, 232, 245, 1);
          border-radius: 4px;
          margin: 20px;
          margin-bottom: 0;
          height: 158px;

          ::v-deep(.el-textarea__inner) {
            font-size: 14px !important;
            background-color: #f8f9fd !important;
            height: 100% !important;
            font-family: PingFangSC-Regular;
            padding: 13px 18px 33px 16px;
          }
        }

        .fir-textarea-max {
          height: 95% !important;
        }

        ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
          display: none !important;
        }

        ::v-deep(.ql-blank) {
          display: none !important;
        }

        ::v-deep(.ql-container.ql-snow) {
          border: 0;
        }
      }
    }

    .ai-tab {
      width: 230px;
      height: calc(100vh * 40/ 1080);
      margin: 0 auto;
      margin-top: 30px;
      background: #f4f6f8;
      border: 1px solid #eeeff0;
      border-radius: 20px;

      .tab-item {
        width: 50%;
        height: calc(100vh * 40/ 1080);
        line-height: 16px;
        float: left;
        line-height: calc(100vh * 40/ 1080);
        cursor: pointer;

        font-family: PingFangSC-Semibold;
        font-size: calc(100vw *  14 / 1920);
        color: #9094a5;
        letter-spacing: 0;
        text-align: center;
      }

      .activedTab {
        border-radius: 20px;

        background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
        box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
        color: #ffffff;
      }
    }

    .tab-item-fir {
      width: 100%;
      height: 536px;
      padding: 0 25px;

      .fir-title {
        color: #222;
        font-weight: 500;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 16px;
        overflow: hidden;
        margin-top: 20px;
        margin-bottom: 20px;
        height: 20px;

        .fir-kuai {
          width: 6px;
          height: 16px;
          margin-right: 8px;
          float: left;
          // margin-top: 2px;
          background: #4081ff;
          border-radius: 1.5px;
        }

        .fir-title-p {
          line-height: 16px;
          float: left;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw *  14 / 1920);
          color: #333333;
          letter-spacing: 0;
          font-weight: 400;
        }
      }

      .fir-alert {
        margin-top: 10px;
        height: 35px;
      }
      // ***滚动条样式
      .ai-tj-body::-webkit-scrollbar {
        width: 6px;
        /* 滚动条的宽度 */
      }

      .ai-tj-body::-webkit-scrollbar-track {
        background: #fff;
        /* 滚动条的背景色 */
      }

      .ai-tj-body::-webkit-scrollbar-thumb {
        background: #488aff;
        /* 滚动条的滑块颜色 */
      }

      .fir-textarea {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        margin-top: 14px;
        height: 158px;
        background: #f8f9fd;
        // border: 1px solid rgba(229, 232, 245, 1);
        border-radius: 4px;

        ::v-deep(.el-textarea__inner) {
          font-size: 14px !important;
          background-color: #f8f9fd !important;
          height: 100% !important;
          font-family: PingFangSC-Regular;
        }
      }

      .fir-textarea-height {
        height: 460px !important;
      }
    }
  }
}

::v-deep(.el-textarea__inner) {
  padding: 16px;
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw *  12 / 1920);
  right: 10px;
  height: 10px;
}

.ai-nav {
  // width: 180px;
  // height: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  transition: ease-out 0.4s;

  .title {
    width: 300px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    letter-spacing: 0px;
    height: 20px;
    line-height: 20px;
    padding-top: 20px;
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    width: 100%;
    padding-top: 45px;
    height: calc(100% - calc(100vh * 80 / 1080));
  }
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;
      cursor: pointer;

      :first-child {
        margin-top: 0px;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

// 导航项
.nav-item {
  height: calc(100vh * 40/ 1080);
  background: transparent;
  line-height: 20px;
  white-space: pre-wrap;
  // background-image: linear-gradient(270deg,
  //     rgba(30, 75, 202, 0.39) 0%,
  //     rgba(59, 130, 234, 0.28) 100%);
  // border: 1px solid rgba(255, 255, 255, 0.2);
  // border-radius: 4px;
  background: #f4f6f8;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
  cursor: pointer;
  // margin: 0 auto;
  margin-top: 20px;
  margin-left: 100px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #9094a5;
  border-radius: 20px;
  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    letter-spacing: 0;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.choose {
  border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  color: #ffffff;
}

.clbutton {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 40px;
}

.clbutton1 {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}

.clbutton2 {
  left: 180px;
}

.clbutton12 {
  // left: 200px;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 280px;
}

.showUp {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 60px;
  height: calc(100vh * 40/ 1080);
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.showUp1 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 6px;
  height: calc(100vh * 40/ 1080);
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 130px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-drawer__header) {
  display: none;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-notification__group) {
  height: auto !important;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.progressClass {
  width: 100%;
  height: 26px;
  position: fixed;
  bottom: 48px;
  right: 0;
  z-index: 999;

  .progress {
    position: absolute;
    right: 52px;
    transition: ease-out 0.4s;
  }
}

::v-deep(.el-progress-bar__innerText) {
  padding: 7px !important;
  color: #fff !important;
}
.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: 383px;
  word-wrap: break-word;
  text-align: left;
  margin-left: 5px;
  margin-bottom: 5px;
  float: left;
}

.el-main .ai-body .tab-item-fir .menu_label:active {
  background: #fff;
  border: 1px solid #4170f6;
}

// .el-main .ai-body .tab-item-fir .menu_label:focus {
//   background: #fff;
//   border: 1px solid #4170f6;
// }

.pass_input {
  // float: left;
  width: 100%;
  height: calc(100vh * 40/ 1080);
}

::v-deep(.el-alert__title) {
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-drawer__open .el-drawer.ltr) {
  width: 100% !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0;
}

::v-deep(.el-alert) {
  padding: 8px 8px;
}

.fir-textarea1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: 1px solid rgba(229, 232, 245, 1);
  border-radius: 4px;
  margin: 20px;
  margin-bottom: 0;
  height: 158px;

  ::v-deep(.el-textarea__inner) {
    font-size: 14px !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: 13px 18px 33px 16px;
  }
}

::v-deep(.el-collapse) {
  border: 0px solid #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: 1px solid #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40/ 1080);
  border-radius: 4px;
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: 4px;
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: 4px;
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: -10px;
    top: 2px;
    width: 4px;
    height: 15px;
    background: #5585f0;
    border-radius: 2px;
  }
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: 6px;
  /* 滚动条的宽度 */
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
  /* 滚动条的背景色 */
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
  /* 滚动条的滑块颜色 */
}

.elcol-title-left {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 20px;
  margin-left: 10px;
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 48px;
}

.elcol-title-text {
  // float: left;
  padding-left: 10px;
  text-align: left;
  width: 40%;
  height: 100%;
}

.elcol-title-text2 {
  font-size: calc(100vw *  14 / 1920);
  color: #d1d7de;
  // float: left;
  width: 75px;
}
.elcol-input {
  float: left;
  width: calc(60% - 75px);
  border: none !important;
}
.xh {
  background: url(../img/bga.png) no-repeat center;

  background-size: 70%;
}
.xh_nv {
  background: url(../img/nv.jpeg) no-repeat center;

  background-size: 70%;
}
</style>