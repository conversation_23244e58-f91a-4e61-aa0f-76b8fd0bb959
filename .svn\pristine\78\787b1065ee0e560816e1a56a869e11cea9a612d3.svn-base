<template>
  <div class="work-container">
    <div class="clearfix" id="login_wrap">
      <div id="login">
        <h2 class="title">欢迎使用思盒ai</h2>
        <div class="login--account">
          <span>账号：</span>
          <el-input
            type="text"
            placeholder="用户名"
            name="account"
            v-model="usern"
            style="width: 64%"
          ></el-input>
        </div>
        <div class="login--password">
          <span>密码：</span>
          <!-- <input type="password" placeholder="密码" name="password" v-model="passw" /> -->
          <el-input
            placeholder="请输入密码"
            v-model="passw"
            show-password
            style="width: 64%"
          ></el-input>
        </div>
        <p class="login--btn">
          <el-button id="loginBtn" type="primary" @click="login1()"
            >登录</el-button
          >
        </p>
      </div>
    </div>
    <el-dialog
      title=""
      :visible.sync="showdia"
      width="50%"
      class="custom-dialog"
    >
      <span style="font-size: 18px; line-height: 20px; margin-top: 300px"
        >您当前密码为初始密码，是否修改？</span
      >
      <span
        slot="footer"
        class="dialog-footer"
        style="padding: 35px 379px 20px"
      >
        <el-button
          style="
            font-size: 10px;
            line-height: 10px;
            padding: 10px 20px;
            background-color: #2975e6;
            color: white;
            margin-top: 35px !important;
          "
          @click="xg()"
          >确认</el-button
        >
        <el-button
          style="
            font-size: 10px;
            line-height: 10px;
            padding: 10px 20px;
            margin-top: 35px;
            background-color: #ffffff;
            border: 1px solid #cccccc;
            color: #cccccc !important;
          "
          @click="not_xg()"
          >暂不修改</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { login } from "../api/home.js"; // 接口请求
export default {
  data() {
    return {
      showdia: false,
      usern: "",
      passw: "",
      username: "",
      id: "",
      loginnum: 0,
      admin: "false",
      passwordVisible: false,
      disabled: "",
      initpassword: "",
      handle_name: "",
    };
  },
  computed: {
    //     count() {
    //   return this.$store.state.count;
    // },
  },
  components: {},
  methods: {
    clickpb() {
      this.$router.push("/pb_manage");
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    xg() {
      if (this.admin == "true") {
        this.$router.push({
          name: "grzx4",
        });
        this.showdia = false;
      } else {
        this.$router.push({
          name: "grzx3",
        });
        this.showdia = false;
      }
    },
    not_xg() {
      if (this.admin == "true") {
        if (this.loginnum > 1) {
          this.$router.push({
            name: "sfw",
          });
          this.showdia = false;
        } else {
          this.$router.push({
            name: "grzx",
            // query: {}
          });
          this.showdia = false;
        }
      } else {
        this.$router.push({
          name: "sfw_notadmin",
        });
        this.showdia = false;
      }
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible;
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    formatMessage(reason) {
      return reason.replace(/\n/g, "<br>"); // 将文本中的 \n 替换为 <br />
    },
    // 真登录
    async login1() {
      let params = {
        passWord: this.passw,
        userName: this.usern,
      };
      // 1234原来的
      let res = await login(params);
      // 1234新的
      // let resdata = await login(params);
      // let res = await resdata.json();
      // ￥￥￥￥￥￥￥￥￥￥￥￥￥￥
      if (res.data.status_code == 10000) {
        // this.count=0;
        this.username = res.data.data.userName;
        this.handle_name = res.data.data.handle_name;

        this.id = res.data.data.id;
        this.admin = res.data.data.admin;
        this.initpassword = res.data.data.initpassword;
        console.log(this.id, "用户id");
        this.$store.dispatch("updateUsername", this.username);
        this.$store.dispatch("updateId", this.id);
        this.$store.dispatch("updateadmin", this.admin);
        this.$store.dispatch("updatehandle_name", this.handle_name);

        this.loginnum = res.data.data.count;
        this.disabled = res.data.data.disabled;
        // token写法
        // store.commit('addNewToken', res.data)
        // let dataLogin = await getUserInfo()
        // const PubSub = require('pubsub-js')
        // PubSub.publish('data', dataLogin)
        if (this.disabled == "true") {
          this.$message({
            title: "提示",
            message: this.formatMessage(res.data.data.reason), // 将 \n 替换为 <br>
            type: "warning",
            dangerouslyUseHTMLString: true, // 允许使用 HTML 内容
            duration: 0, // 设置为 0 表示一直显示
            showClose: true,
          });
        } else {
          if (this.admin == "true") {
            if (this.initpassword == "true") {
              this.showdia = true;
            } else {
              if (this.loginnum > 1) {
                this.$router.push({
                  name: "sfw",
                });
              } else {
                this.$router.push({
                  name: "grzx",
                  // query: {}
                });
              }
            }
          } else {
            if (this.initpassword == "true") {
              this.showdia = true;
            } else {
              this.$router.push({
                name: "sfw_notadmin",
              });
            }
          }
        }
      } else if (res.data.status_code == 500) {
        this.$notify({
          title: "提示",
          message: res.data.message,
          type: "error",
        });
      } else {
        // alert(1)
        this.$notify({
          title: "提示",
          message: "登录失败请重试",
          type: "warning",
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.work-container {
  background: url("../assets/back.png") center no-repeat !important;
  background-size: 100% 100%;
  height: 100%;
}
.title {
  text-align: center;
  font-size: 22px;
}

#login_wrap {
  // background: url("../assets/back.png") center no-repeat;
  // background-size: 100% 100%;

  height: 100%;

  > div {
    background: rgba(255, 255, 255, 0.3);
    width: 520px;
    height: 400px;
    padding: 30px 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    > div {
      padding: 10px 0;
      border-bottom: 1px solid #ddd;

      &.login--account {
        margin: 25px 0 30px;
      }

      // span {
      //   color: #666;
      //   display: inline-block;
      //   width: 84px;
      //   font-size: 20px;
      // }

      input {
        background: none;
        font-size: 16px;
        border: none;
        height: 30px;
        width: 280px;
        padding-left: 12px;
        box-sizing: border-box;
        color: #666;

        &.error {
          border: 1px solid #f00;
        }

        &::-webkit-input-placeholder {
          color: #aaa;
        }
      }
    }

    p {
      text-align: right;

      &.login--btn {
        button {
          width: 100%;
          height: 50px;
          font-size: 18px;
          // border: none;
          margin-top: 30px;
        }
      }

      a {
        color: #fff;
        display: inline-block;
        padding: 0 15px;
        font-size: 14px;
      }
    }
  }
}

.info {
  color: #999;
  margin-top: 8px;
  text-align: center !important;
}
// <style lang="less" scoped>
::v-deep .custom-dialog .el-dialog__body {
  padding-top: 120px;
  /* 调整这个值以达到你想要的移动距离 */
}

::v-deep .custom-dialog .el-dialog {
  height: 400px;
  background: url("../assets/tc.png") no-repeat center !important;
  background-size: cover !important;
  /* 确保背景图片覆盖整个对话框 */
}

::v-deep .custom-dialog .dialog-footer {
  position: absolute; /* 添加绝对定位 */
  bottom: 20px; /* 调整这个值以达到你想要的按钮位置 */
  left: 50%; /* 使按钮水平居中 */
  transform: translateX(-50%); /* 使按钮水平居中 */
  padding-top: 0; /* 移除原有的padding-top */
  width: 100%; /* 使footer宽度占满dialog */
  display: flex; /* 使按钮水平排列 */
  justify-content: center; /* 使按钮水平居中 */
}

.el-button span {
  font-family: SourceHanSansSC-Medium;
}
//
</style>