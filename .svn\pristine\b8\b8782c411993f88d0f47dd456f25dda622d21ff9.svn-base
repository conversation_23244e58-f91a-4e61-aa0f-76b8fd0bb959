<!-- 会议记录 -->
<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle"></div>
        <div class="ai-header">
          <div class="ai-bar">
            <HeadNavigation />

          </div>
        </div>
      </el-header>
      <!-- <div style="margin-top:100px">
        <el-button>会议记录</el-button>
                <el-button>材料整理</el-button>
      </div> -->
      <el-main
        v-loading="loading1"
        element-loading-text="请稍等..."
        element-loading-background="rgba(255, 255, 255, 0.7)"
      >
        <div style="margin-top: 15px">
          <button
            @click="
              form = {
                attendees: '',
                meetingLocation: '',
                meetingTopic: '',
                meetingTime: '',
                moderator: '',
                remarks: '',
                meetingId: '',
              };
              adddia = true;
            "
            data-v-819514c6=""
            aria-disabled="false"
            type="button"
            class="el-button el-button--primary el-button--default"
          >
            <i class="el-icon"
              ><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill="currentColor"
                  d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"
                ></path></svg></i
            ><span class="">上传会议通知</span>
          </button>
          <!--  -->
          <button
            @click="daochu()"
            data-v-dbe6b5ce=""
            aria-disabled="false"
            type="button"
            class="el-button el-button--primary el-button--default"
          >
            <i class="el-icon"
              ><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill="currentColor"
                  d="M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64zm384-253.696 236.288-236.352 45.248 45.248L508.8 704 192 387.2l45.248-45.248L480 584.704V128h64v450.304z"
                ></path></svg></i
            ><span class="">批量导出</span>
          </button>
          <!-- @click="batchDelete" -->
          <button
            style="margin-right: 540px"
            @click="batchDelete"
            data-v-5317e0d1=""
            aria-disabled="false"
            type="button"
            class="el-button el-button--danger el-button--default"
          >
            <i class="el-icon"
              ><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill="currentColor"
                  d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
                ></path></svg></i
            ><span class="">批量删除</span>
          </button>

          <div
            data-v-3685a8a8=""
            class="el-input el-input--default el-input-group el-input-group--append el-input--suffix w-50 m-2"
            style="margin-left: 20px; width: 300px"
          >
            <div tabindex="-1">
              <input
                class="el-input__inner"
                type="text"
                autocomplete="off"
                tabindex="0"
                placeholder="请输入查询信息"
                id="el-id-4263-16"
                v-model="inputvalyhm"
              />
              <span
                v-if="inputvalyhm != ''"
                class="el-icon-circle-close"
                @click="clearInput"
                style="
                  cursor: pointer;
                  position: absolute;
                  left: calc(100% - 80px);
                  top: 50%;
                  transform: translateY(-50%);
                "
              >
              </span>
            </div>
            <div class="el-input-group__append" @click="sea(inputvalyhm)">
              <div class="el-icon-search"></div>
            </div>
          </div>
          <el-date-picker
            class="el-input124"
            v-model="v1"
            type="datetime"
            placeholder="选择开始的日期时间"
          >
          </el-date-picker>
          <!-- <div>至</div> -->
          <el-date-picker
            class="el-input123"
            v-model="v2"
            type="datetime"
            placeholder="选择结束的日期时间"
          >
          </el-date-picker>

          <el-button style="height: 40px" @click="sea1()">查询</el-button>
          <div style="margin-left: 10px; margin-top: 10px; height: 700px">
            <el-table
              stripe
              border
              @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'date', order: 'descending' }"
              style="margin-top: 10px; height: 700px; overflow-y: scroll"
              :data="this.tableData1"
              :header-cell-style="{
                background: '#ebf2fb',
              }"
            >
              <!-- <el-table-column prop="" label="序号" width="201">
                  </el-table-column> -->
              <el-table-column type="selection"> </el-table-column>
              <el-table-column label="序号" width="50" sortable type="index">
              </el-table-column>
              <el-table-column prop="meetingTime" sortable label="会议时间">
              </el-table-column>
              <!-- <el-table-column prop="yhm" sortable label="用户名">
              </el-table-column> -->
              <el-table-column prop="meetingLocation" sortable label="会议地点">
              </el-table-column>
              <el-table-column prop="meetingTopic" sortable label="会议主题">
              </el-table-column>
              <el-table-column prop="moderator" sortable label="主持人">
              </el-table-column>
              <el-table-column prop="attendees" label="参会人员" sortable>
                <template v-slot="scope">
                  <div
                    style="
                      max-height: calc(100vh *  50 / 1080);
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    :title="scope.row.attendees"
                  >
                    {{ scope.row.attendees }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="remarks" sortable label="备注">
                <template v-slot="scope">
                  <div
                    style="
                      max-height: calc(100vh *  50 / 1080);
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    :title="scope.row.remarks"
                  >
                    {{ scope.row.remarks }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="" label="操作" width="290" align="center">
                <template v-slot="aa">
                  <el-button
                    class="el-icon-view"
                    style="margin-right: 10px; font-size: 20px"
                    type="text"
                    size="medium"
                    @click="showdia(aa.row)"
                  ></el-button>
                  <el-button
                    class="el-icon-download"
                    style="margin-right: 10px; font-size: 20px"
                    type="text"
                    size="medium"
                    @click="showdia1(aa.row)"
                  ></el-button>
                  <!-- el-icon-delete -->
                  <el-button
                    class="el-icon-delete"
                    style="margin-right: 10px; font-size: 20px"
                    type="text"
                    size="medium"
                    @click="showdia3(aa.row)"
                  ></el-button>
                  <!-- el-icon-edit -->
                  <el-button
                    class="el-icon-edit"
                    style="margin-right: 0px; font-size: 20px"
                    type="text"
                    size="medium"
                    @click="(form = aa.row), (dialogVisiblechange = true)"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="this.total"
          >
          </el-pagination>
        </div>
      </el-main>
    </el-container>
    <el-dialog
      title="新增会议通知"
      :visible.sync="adddia"
      @close="handleClose"
      v-loading="loading"
      element-loading-text="请稍候..."
      element-loading-background="rgba(255, 255, 255, 0.7)"
    >
      <el-form ref="userForm" :model="form" :rules="formRules">
        <el-form-item label="会议文件">
          <el-upload
            class="upload-demo"
            :before-upload="beforeAvatarUpload"
            ref="upload"
            action="#"
            :show-file-list="false"
            accept=".pdf,.doc,.docx"
          >
            <div
              class="el-icon-upload2"
              style="margin-left: -710px; margin-top: 10px"
            ></div>
          </el-upload>
          <!-- </div> -->
        </el-form-item>
        <el-form-item label="参会人员" prop="attendees">
          <el-input
            v-model="form.attendees"
            placeholder="请输入参会人员"
            autocomplete="off"
            style="width: 438px"
          ></el-input>
        </el-form-item>
        <el-form-item label="会议地点" prop="meetingLocation">
          <el-input
            style="width: 438px"
            v-model="form.meetingLocation"
            placeholder="请输入会议地点"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="会议主题" prop="meetingTopic">
          <el-input
            v-model="form.meetingTopic"
            placeholder="请输入会议主题"
            autocomplete="off"
            style="width: 438px"
          ></el-input>
        </el-form-item>
        <el-form-item label="会议时间" prop="meetingTime">
          <!-- <el-input
            class="el-input1"
            v-model="form.meetingTime"
            placeholder="请输入会议时间"
            autocomplete="off"
          ></el-input> -->
          <el-date-picker
            v-model="form.meetingTime"
            type="datetime"
            placeholder="选择日期时间"
            style="width: 438px"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="会议主持" prop="moderator">
          <el-input
            v-model="form.moderator"
            placeholder="请输入主持人"
            autocomplete="off"
            style="width: 438px"
          ></el-input>
        </el-form-item>
        <el-form-item label="会议备注" prop="remarks">
          <el-input
            v-model="form.remarks"
            placeholder="请输入备注"
            autocomplete="off"
            style="width: 438px"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            adddia = false;
            this.successpdfurl = '';
            this.form = {
              attendees: '',
              meetingLocation: '',
              meetingTopic: '',
              meetingTime: '',
              moderator: '',
              remarks: '',
              meetingId: '',
            };
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="submitForm1()">保 存</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="修改会议记录"
      :visible.sync="dialogVisiblechange"
      @close="handleClose1"
    >
      <el-form ref="userForm" :model="form" :rules="formRules">
        <el-form-item label="参会人员" prop="attendees">
          <el-input
            style="width: 438px"
            v-model="form.attendees"
            placeholder="请输入参会人员"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="会议地点" prop="meetingLocation">
          <el-input
            style="width: 438px"
            v-model="form.meetingLocation"
            placeholder="请输入会议地点"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="会议主题" prop="meetingTopic">
          <el-input
            style="width: 438px"
            v-model="form.meetingTopic"
            placeholder="请输入会议主题"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="会议时间" prop="meetingTime">
          <!-- <el-input
            class="el-input1"
            v-model="form.meetingTime"
            placeholder="请输入会议时间"
            autocomplete="off"
          ></el-input> -->
          <el-date-picker
            v-model="form.meetingTime"
            type="datetime"
            placeholder="选择日期时间"
            style="width: 438px"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="会议主持" prop="moderator">
          <el-input
            style="width: 438px"
            v-model="form.moderator"
            placeholder="请输入主持人"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="会议备注" prop="remarks">
          <el-input
            v-model="form.remarks"
            placeholder="请输入备注"
            autocomplete="off"
            style="width: 438px"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            getuser(currentPage, pageSize);
            dialogVisiblechange = false;
            this.successpdfurl = '';
            this.form = {
              attendees: '',
              meetingLocation: '',
              meetingTopic: '',
              meetingTime: '',
              moderator: '',
              remarks: '',
              meetingId: '',
            };
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="预览文件" :visible.sync="dialogVisiblepdf" width="30%">
      <div style="width: 100%; height: 100%; overflow: scroll">
        <!-- pdf插件 -->
        <!-- <pdf
          v-for="i in numPages"
          :key="i"
          :src="pdf"
          :page="i"
          width="100%"
        ></pdf> -->
        <img :src="imurl" style="width: 100%; height: 100%" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisiblepdf = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import HeadNavigation from "../components/HeadNavigation.vue"; // 引入头部导航组件

import {
  getTitle,
  getTitle2,
  getBody,
  getwz,
  d_w,
  select_meeting,
  upload_meeting,
  insert_meeting,
  select_meetingById,
  download_pdf_meetingById,
  delete_meeting,
  update_meeting,
  export_meeting,
} from "../api/home.js"; // 接口请求
import { search } from "../api/home3.js"; // 接口请求
import store from "../store/index";
import { VueEditor } from "vue2-editor";
import { mapState, mapActions } from "vuex";

// import { marked } from 'marked';
import pdf from "vue-pdf";

export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id

    username() {
      return this.$store.state.username;
    },
  },
  data() {
    return {
      loading1: false,
      loading: false,
      imurl: "",
      v1: "",
      v2: "",
      value1: "",
      formRules: {
        meetingTime: [
          { required: true, message: "会议时间不能为空", trigger: "blur" },
        ],
        // 会议地点
        meetingLocation: [
          { required: true, message: "会议地点不能为空", trigger: "blur" },
        ],
        // handle_name: [
        //   { required: true, message: "所在处室不能为空", trigger: "change" },
        // ],
        // // gwmc: [
        // //   { required: true, message: '岗位名称不能为空', trigger: 'blur' }
        // // ]
      },
      selectedUsers: [], // 用于存储选中的用户名字

      numPages: 0,

      successpdfurl: "",
      form: {
        attendees: "",
        meetingLocation: "",
        meetingTopic: "",
        meetingTime: "",
        moderator: "",
        remarks: "",
        meetingId: "",
        // pdfurl:"",
        // wzlxpdfurl: "",
        // wznr: "",
        // wzsj: "",
        // wztp: "",
        // wztpmc: "",
        //   wztpnr: "",
      },
      // meetingId: "",
      inputvalyhm: "",
      adddia: false,
      currentWznr: "",
      dialogVisible: false,
      dialogVisiblepdf: false,
      dialogVisiblechange: false,

      pdf: "",

      tableData1: [],
      tableData2: [],
      tableData3: [],
      tableData4: [],
      tableData5: [],
      tableData6: [],
      activeName: "a",
      array2: "",
      inputval: "",
      navShow: true,
      flpd: "",
      ztpd: "",
      input: "",
      flArr: [],
      ztArr: [],
      nrArr: [],
      flmc: "",
      ztmc: "",
      currentPage: 1, // 当前页
      pageSize: 10, // 每页显示条数
      total: 0,
    };
  },
  components: {
    HeadNavigation,

    VueEditor,
    pdf,
  },
  mounted() {
    this.getuser(1, 10);
  },
      methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickzskgl() {
      this.$router.push("/zsk");
    },
    handleClose() {
      // 关闭对话框时重置表单
      this.successpdfurl = "";
      this.form = {
        attendees: "",
        meetingLocation: "",
        meetingTopic: "",
        meetingTime: "",
        moderator: "",
        remarks: "",
        meetingId: "",
      };
      this.adddia = false; // 确保关闭对话框
    },
    handleClose1() {
      // 关闭对话框时重置表单
      this.successpdfurl = "";
      this.form = {
        attendees: "",
        meetingLocation: "",
        meetingTopic: "",
        meetingTime: "",
        moderator: "",
        remarks: "",
        meetingId: "",
      };
      this.getuser(this.currentPage, this.pageSize); // 刷新用户列表
      this.dialogVisiblechange = false; // 确保关闭对话框
    },
    clearInput() {
      this.inputvalyhm = ""; // 清空输入框内容
    },
    handleSelectionChange(selected) {
      // selected 是当前选中的用户数组
      this.selectedUsers = selected.map((user) => user.meetingId); // 更新 selectedUsers 数组
    },
    async batchDelete() {
      // 检查是否有选中的用户
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择会议！",
          type: "error",
        });
        return;
      }

      // 弹出确认对话框
      this.$confirm("此操作将永久删除选择的用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 点击“确定”后的逻辑
          let params = {
            meetingId: this.selectedUsers, // 将选中的用户名数组发送给后端
          };
          try {
            let res = await delete_meeting(params);
            if (res.data.status_code == 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.getuser(1, this.pageSize); // 刷新用户列表
            } else {
              this.$message({
                message: res.data.data.message || "删除失败",
                type: "error",
              });
            }
          } catch (error) {
            this.$message({
              message: "删除请求失败",
              type: "error",
            });
            console.error(error);
          }
        })
        .catch(() => {
          // 点击“取消”后的逻辑
          this.$message({
            message: "已取消删除",
            type: "info",
          });
        });
    },
    async daochu() {
      // 检查是否有选中的用户
      if (this.selectedUsers.length === 0) {
        this.$message({
          message: "请先选择会议！",
          type: "error",
        });
        // return;
      } else {
        let params = {
          meetingId: this.selectedUsers, // 将选中的用户名数组发送给后端
        };
        //  let res = await export_meeting(params);
        export_meeting(params)
          .then((blob) => {
            console.log("我是导出的", blob);
            if (blob && blob.size > 0) {
              // 创建一个 URL 对象
              const url = window.URL.createObjectURL(blob);
              // 创建一个 <a> 元素
              const a = document.createElement("a");
              // 设置下载属性
              a.href = url;
              a.download = `会议记录.xlsx`; // 将扩展名改为 .xlsx
              // 将 <a> 元素添加到文档中
              document.body.appendChild(a);
              // 触发点击事件
              a.click();
              // 移除 <a> 元素
              document.body.removeChild(a);
              // 释放 URL 对象
              window.URL.revokeObjectURL(url);
              this.dialogVisible = false;
            } else {
              this.$message({
                message: "导出失败: 无效的EXCEL文件",
                type: "error",
              });
            }
          })
          .catch((error) => {
            console.error("导出EXCEL时出现错误:", error);
            this.$message({
              message: "导出失败，请稍候重试",
              type: "error",
            });
          });
      }
    },
    async showdia3(c1) {
      let params = {
        meetingId: c1.meetingId,
        // username: this.username,
      };
      let res = await delete_meeting(params);
      if (res.data.status_code == 200) {
        // this.tableData1 = res.data.data.records;
        // this.total = res.data.data.total;
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getuser(this.currentPage, this.pageSize);
      } else {
        this.$message({
          message: "删除失败,请重试",
          type: "error",
        });
      }
    },
    async showdia1(row) {
      let params = {
        meetingId: row.meetingId,
      };
      let res = await download_pdf_meetingById(params);
      console.log("res", res.data);
      if (res.data.status_code == 200) {
        const base64Data = res.data.data; // 假设这个是 PDF 的 base64 数据
        const blob = this.base64ToBlob(base64Data, "application/pdf"); // 转换为 Blob
        const url = window.URL.createObjectURL(blob); // 创建一个 URL 对象
        const a = document.createElement("a"); // 创建一个 <a> 元素
        a.href = url;
        a.download = res.data.file_name; // 设置下载文件名
        document.body.appendChild(a); // 将 <a> 元素添加到文档中
        a.click(); // 触发点击事件
        document.body.removeChild(a); // 移除 <a> 元素
        window.URL.revokeObjectURL(url); // 释放 URL 对象
      } else if (res.data.status_code == 205) {
        this.$message({
          message: "下载失败: 该会议没有上传文件",
          type: "warning",
        });
      } else {
        this.$message({
          message: "下载失败，请稍候重试",
          type: "error",
        });
      }
    },
    base64ToBlob(base64, type) {
      const byteCharacters = atob(base64); // 解码 base64
      const byteNumbers = new Uint8Array(byteCharacters.length); // 创建 Uint8Array
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i); // 将字符转换为其 UTF-16 码点
      }
      return new Blob([byteNumbers], { type }); // 返回 Blob 对象
    },
    submitForm1() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          this.submitFormadd(); // 在验证通过后调用添加用户的函数
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    async submitFormadd() {
      this.loading = true;
      let params = {
        attendees: this.form.attendees,
        meetingLocation: this.form.meetingLocation,
        meetingTopic: this.form.meetingTopic,
        meetingTime: this.form.meetingTime,
        moderator: this.form.moderator,
        remarks: this.form.remarks,
        meeting_pdf: this.successpdfurl,
        username: this.id,
      };
      let res = await insert_meeting(params);

      if (res.data.status_code == 200) {
        this.$message({
          message: "保存成功",
          type: "success",
        });
        this.adddia = false;
        this.successpdfurl = "";
        this.form = {
          attendees: "",
          meetingLocation: "",
          meetingTopic: "",
          meetingTime: "",
          moderator: "",
          remarks: "",
          meetingId: "",
        };
        // this.getuser(1, 10);
        this.getuser(this.currentPage, this.pageSize);
        this.loading = false;
      } else {
        this.$message({
          message: "保存失败,请重试",
          type: "error",
        });
      }
      // },
    },

    submitForm() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          this.showdia2(); // 在验证通过后调用添加用户的函数
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    sea1() {
      this.getuser(1, this.pageSize, this.inputvalyhm, this.v1, this.v2);
      this.currentPage = 1;
    },
    async showdia2() {
      let params = {
        meetingId: this.form.meetingId,
        meetingLocation: this.form.meetingLocation,
        meetingTopic: this.form.meetingTopic,
        meetingTime: this.form.meetingTime,
        moderator: this.form.moderator,
        attendees: this.form.attendees,
        remarks: this.form.remarks,
        username: this.id,
      };

      let res = await update_meeting(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "修改成功",
          type: "success",
        });
        this.dialogVisiblechange = false;
        this.form = {
          attendees: "",
          meetingLocation: "",
          meetingTopic: "",
          meetingTime: "",
          moderator: "",
          remarks: "",
          meetingId: "",
        };
        // this.getuser(1, 10);
        this.getuser(this.currentPage, this.pageSize);
      } else {
        this.$message({
          message: "修改失败",
          type: "error",
        });
        this.dialogVisiblechange = false;
        this.form = {
          attendees: "",
          meetingLocation: "",
          meetingTopic: "",
          meetingTime: "",
          moderator: "",
          remarks: "",
          meetingId: "",
        };
      }
    },
    beforeAvatarUpload(files) {
      this.loading = true;
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "meeting_pdf",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          // formData.append("file_type", "述职报告"); // 添加额外字段
          formData.append("username", this.username); // 添加额外字段
          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("meeting_pdf")); // 调试输出
          // this.scfw();
          this.upload_meeting();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
      // }
    },
    async upload_meeting() {
      console.log("FormData123456:", this.formData.getAll("meeting_pdf[]"));
      if (!this.formData) {
        console.error("没有文件被上传");
        return;
      }
      try {
        let res = await upload_meeting(this.formData); // 直接传递 formData 进行上传
        this.form = res.data.data;
        console.log("返回值结果:", this.form);

        if (res.data.status_code == 200) {
          this.loading = false;

          this.$message({
            message: "上传成功",
            type: "success",
          });
          this.form.attendees = res.data.data.attendees;
          this.form.meetingLocation = res.data.data.meetingLocation;
          this.form.meetingTopic = res.data.data.meetingTopic;
          this.form.meetingTime = res.data.data.meetingTime;
          this.form.moderator = res.data.data.moderator;
          this.form.remarks = res.data.data.remarks;
          this.form.meetingId = res.data.data.meetingId;
          // 获取刚才上传成功的文件流
          const uploadedFiles = this.formData.getAll("meeting_pdf"); // 获取上传的文件流
          if (uploadedFiles.length > 0) {
            this.successpdfurl = uploadedFiles[0]; // 假设只获取第一个文件流
            console.log("成功上传的文件流:", this.successpdfurl);
          }
        } else {
          this.$message({
            message: "上传失败",
            type: "error",
          });
        }
      } catch (error) {
        console.error("上传失败:", error);
      }
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    handleSizeChange(val) {
      this.getuser(1, val, this.inputvalyhm, this.v1, this.v2);
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.getuser(val, this.pageSize, this.inputvalyhm, this.v1, this.v2);
      this.currentPage = val;
    },
    handleClick() {
      alert(1);
    },
    sea(c3) {
      this.getuser(1, this.pageSize, c3, this.v1, this.v2);
      this.currentPage = 1;
    },
    async getuser(c1, c2, c3, c4, c5) {
      let params = {
        search_string: c3,
        kssj: c4,
        jssj: c5,
        pageNo: c1,
        pageSize: c2,
      };
      let res2 = await select_meeting(params);
      this.tableData1 = res2.data.data;
      this.total = res2.data.total_records;
    },
    toxg() {
      // alert(1)
      this.$router.push("/grzx4");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
       clickTopsfw() {
      this.$router.push("/sfw");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    sfw111() {
      this.$router.push("/sfw");
    },
    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "111111111111111111111111111111111111");
      this.currentWznr = html;
      // return html;
    },
    marked1(c1) {
      const lines = c1.wznr.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true;
      lines.forEach((line) => {
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${content}</h${level}>`;
            isFirstHeading = false;
          } else {
            html += `<h${level} style="text-align: left; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${content}</h${level}>`;
          }
        } else if (line.startsWith("-")) {
          if (!inList) {
            html +=
              '<ul style="text-align: left; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li style="max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${content}</li>`;
        } else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${line}</p>`;
        }
      });
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "222222222222");
      return html;
    },
    getBlobType(blob) {
      const type = blob.type;
      if (
        type ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ) {
        return "Word 文档";
      } else if (type === "application/pdf") {
        return "PDF 文档";
      } else {
        return "未知类型";
      }
    },
    async showdia(c1) {
      this.loading1 = true;
      let params = {
        meetingId: c1.meetingId,
      };
      let res = await select_meetingById(params);
      // console.log(res.data.status_code, "这是查询会议通知文件返回值select_meetingById");
      if (res.data.status_code == 205) {
        this.$message({
          message: "未上传会议通知文件",
          type: "error",
        });
        this.loading1 = false;
      } else if (res.data.status_code == 200) {
        this.dialogVisiblepdf = true;
        // this.imurl=res.data.data.pdf_base64;
        this.imurl = `data:image/png;base64,${res.data.data.pdf_base64}`;
        this.loading1 = false;
      } else {
        this.$message({
          message: "查询失败",
          type: "error",
        });
      }
    },
    getPdfNumPages(pdfData) {
      this.filePdfUrl = "data:application/pdf;base64," + pdfData;
      this.pdf = this.filePdfUrl;
      // console.log(this.filePdfUrl, "这是我回显的pdfurl11111")
      const loadingTask = pdf.createLoadingTask(this.filePdfUrl);
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf.numPages;
        })
        .catch((err) => {
          console.error("Error while loading the PDF", err);
        });
    },
    clickTopLg() {
      this.$router.push("/Lgsc");
    },
    clickTopNav() {
      this.$router.push("/Lgsc");
    },
    clickTopAi() {
      this.$router.push("/index");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    setParams() {
      this.params1.userName = this.username;
      this.params2.userName = this.username;
      this.params3.userName = this.username;
      this.params4.userName = this.username;
      this.params5.userName = this.username;
      this.params6.userName = this.username;
    },
    async getwz1() {
      let res1 = await getwz(this.params1);

      if (res1.data.status_code == 200) {
        this.tableData1 = res1.data.data;

        // 弹出提示
        this.$message({
          message: res1.data.message,
          type: "success",
        });
      } else if (res1.data.status_code == 500) {
        this.$message({
          message: res1.data.message,
          type: "error",
        });
      }
    },
    async getwz2() {
      let res2 = await getwz(this.params2);
      // if (res2.data.status_code == 200) {
      // 弹出提示
      this.tableData2 = res2.data.data;
      //   this.$message({
      //     message: res2.data.message,
      //     type: 'success'
      //   });

      // }
      // else if (res2.data.status_code == 500) {

      //   this.$message({
      //     message: res2.data.message,
      //     type: 'error'
      //   })
      // }
    },
    async getwz3() {
      let res3 = await getwz(this.params3);
      // if (res3.data.status_code == 200) {
      this.tableData3 = res3.data.data;
      //   this.$message({
      //     message: res3.data.message,
      //     type: 'success'
      //   });

      // }

      // }
    },
    async getwz4() {
      let res4 = await getwz(this.params4);
      this.tableData4 = res4.data.data;
    },
    async getwz5() {
      let res5 = await getwz(this.params5);
      this.tableData5 = res5.data.data;
    },
    async getwz6() {
      let res6 = await getwz(this.params6);
      this.tableData6 = res6.data.data;
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    // 路由跳转时强制刷新界面
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        // 强制刷新页面
        vm.$forceUpdate();
      });
    },
  },
};
</script>
<style lang="less" scoped>
.ellipsis-container {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-content {
  display: inline;
}

.ellipsis .cell {
  height: calc(100vh *  50 / 1080);
  /* 你可以根据需要调整高度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 移除悬停展示的样式 */
.ellipsis .cell:hover {
  /* 移除或覆盖悬停展示的样式 */
  text-overflow: ellipsis;
  /* 确保悬停时不展示完整内容 */
}

.custom-label {
  margin-left: 50px;
  font-weight: 600;
  font-size: calc(100vw *  18 / 1920);
  letter-spacing: 0px;
  line-height: 16px;
  //  active时的样式
  // :active{
  // color:#fff;
  // background-color:#3a6bc6;
  // border-radius:20px;
  // }
}

.tableCon {
  .ellipsis {
    height: 200px;
  }
}

.highlight {
  background-color: yellow;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: linear-gradient(76deg,
  //     #07389c 0%,
  //     #3d86d1 0%,
  //     #3448b3 100%);

  img {
    width: 120px;
    height: 48px;
  }

  p {
    border: 1.54px solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vh * 40 / 1080);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: 1px solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vh * 130 / 1080);
            color: #000;
            font-size: calc(100vh * 14 / 1080);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vh * 14 / 1080);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vh * 16 / 1080);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vh * 14 / 1080);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw *  12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 /1080);
  padding: 20px 10px;
  // overflow: hidden;
}

.LgscCon {
  width: 1002px;
  height: auto;
  margin: 0 auto;

  .button {
    width: 108px;
    height: 58px;
    background: #4c91ff;
    border-radius: 0px 100px 100px 0px;
    cursor: pointer;
  }

  img {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }

  p {
    line-height: 58px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flexcz {
  display: flex;
  align-items: center;
}

.flexNoCz {
  display: flex;
  justify-content: center;
}

.floatLeft {
  float: left;
}

.lgfl {
  width: 100%;
  height: calc(100vh * 40/ 1080);
  // background: #000;
  margin-top: 42px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  // font-weight: 600;
  display: flex;
  align-items: center;
}

.lgfl1 {
  margin-top: 10px;
}

.mgr1 {
  margin-right: 26px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  // font-weight: 600;
  // cursor: pointer;
}

.mgr {
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  // font-weight: 600;
  width: 96px;
  margin-right: 26px;
  cursor: pointer;

  &:hover {
    width: 96px;
    height: 100%;
    background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
    border-radius: 20px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    // font-weight: 600;
  }

  &:active {
    width: 96px;
    height: 100%;
    background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
    border-radius: 20px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.activeFl {
  width: 96px;
  height: 100%;
  background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
  border-radius: 20px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  // font-weight: 600;
}

.nrCon {
  margin-top: 30px;
  width: 100%;
  height: calc(100vh * 750 / 1080);
  overflow-y: scroll;

  .nrConLeft {
    width: calc(50% - 10px);
    height: auto;
    float: left;
    // display: flex;
    // flex-flow: row wrap;
    // // align-items: flex-start;
    // // flex-basis: auto;
    // align-items: flex-start;
    // margin-right: 20px;
  }

  .nrConRight {
    width: calc(50% - 10px);
    height: auto;
    float: right;
  }
}

.nrConLeftBk {
  width: auto;
  height: auto;
  background: #ffffff;
  box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.13);
  border-radius: 12px;
  padding: 28px 40px;
  position: relative;
  margin-bottom: 20px;

  .bkTitle {
    height: auto;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    text-align: left;
    // font-weight: 600;
  }

  .bkCon {
    height: auto;
    margin-top: 19px;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    text-align: left;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
  }

  .bkBtn {
    width: 100%;
    margin-top: 16px;
    height: auto;

    // background: #000;
    .bkBtnLeft {
      width: 50%;
      height: 24px;

      .xztg {
        width: auto;
        height: 24px;
        background: #f5f8fc;
        border-radius: 12px;
        padding: 0 10.5px;
        font-family: PingFangSC-Regular;
        font-size: calc(100vw *  14 / 1920);
        color: #7b7e9e;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        cursor: pointer;
      }

      .qt {
        width: auto;
        height: 24px;
        background: #f5f8fc;
        border-radius: 12px;
        padding: 0 10.5px;
        font-family: PingFangSC-Regular;
        font-size: calc(100vw *  14 / 1920);
        color: #7b7e9e;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        margin-left: 10px;
        cursor: pointer;
      }
    }

    .bkBtnright {
      // width: 131px; //复制加收藏
      width: 54px;
      height: 24px;

      .fz {
        width: 53px;
        height: 24px;
        cursor: pointer;

        img {
          width: 16px;
          height: 16px;
        }

        p {
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw *  14 / 1920);
          color: #666666;
          letter-spacing: 0;
          line-height: 24px;
          font-weight: 500;
        }
      }

      .sc {
        width: 53px;
        height: 24px;
        cursor: pointer;

        img {
          width: 16px;
          height: 16px;
        }

        p {
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw *  14 / 1920);
          color: #666666;
          letter-spacing: 0;
          line-height: 24px;
          font-weight: 500;
        }
      }
    }
  }
}

.flexLd {
  display: flex;
  justify-content: space-between;
}

::v-deep(.el-input__inner) {
  // height: 58px;
  border-radius: 0;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.dialog-content {
  height: 400px;
  overflow-y: scroll;
}
.dChangePwd .footerInline .tsFont {
  width: 100%;
  line-height: 30px;
  margin-top: 0;
  color: #f60;
}

.ml {
  margin-left: 100px;
}
.ml1 {
  margin-left: 120px;
}
.ml2 {
  margin-left: 100px;
}
::v-deep .el-descriptions-item__label.has-colon::after {
  content: "";
  margin-top: 20px;
}

.el-tag {
  background: #f5f8fc;
  border-radius: 12px;
  padding: 4px;
  // padding-left: 2px;
  // padding-right: 5px;
  // height: 32px;
  // line-height: 32px;
}

.btnr {
  margin-top: 190px;
}
.btn-group {
  background: url(../img/bga.png) no-repeat center;
  background-size: 45%;
}
.btn-group1 {
  // margin-right: 500px;
  // background: url(../img/hahu3.png) no-repeat center;
  background-size: 45%;
}
// .btn-group1{
//     background: url(../img/hahu3.png) no-repeat left;
//     background-size: 40%;
//     margin-left: -500px;
// }
// .btn-group {
//   // background: url(../img/bga.png) no-repeat center;
//   background: url(this.backgroundImage) no-repeat center;
//   background-size: 45%;
// }
.left1 {
  background: #ffffff;
  border-radius: 8px;
}
.next_but {
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  width: 130px;
  height: calc(100vh * 40/ 1080);
  border-radius: 20px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 600;
  &:hover {
    color: #ffffff;
  }
  &:active {
    color: #ffffff;
  }
}
.right {
  background: url(../img/bga.png) no-repeat center;
  // width: 88%;
  // height: 100%;
  background-size: 70%;
  //  background: #FFFFFF;
  // border-radius: 8px;
}
.el-select {
  width: 600px;
}
.el-input {
  width: 200px;
  // margin-left: -120px;
  margin-left: -145px;
  margin-right: 150px;
}
.el-input1 {
  width: 438px;
  margin-left: -149px;
}
.fir-kuai2 {
  // margin-top: px;
  margin-right: 10px;

  width: 6px;
  height: 16px;
  float: left;
  background: #4081ff;
  border-radius: 1.5px;
}

.el-upload__tip {
  margin-top: -20px;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}
// .avatar-uploader .el-upload {
//   border: 1px #d9d9d9 dashed !important;
//   border-radius: 6px;
//   cursor: pointer;
//   position: relative;
//   overflow: hidden;
// }
.parent-container .avatar-uploader .el-upload {
  border: 1px #d9d9d9 dashed !important;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: calc(100vw *  28 / 1920);
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.ai-left-bar-li {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh *  50 / 1080);
  width: calc(100vw *  1000/ 1920);
  // color: #000;

  line-height: 16px;
  white-space: nowrap;
  cursor: pointer;
  // z-index: 9999;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  19 / 1920);
  // color: #000000;
  letter-spacing: 0;
  text-align: center;
  // background: url(../assets/jstext.png) no-repeat center;
}

.actived {
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  // background-image: linear-gradient(91deg, rgba(173,235,251,0.80) 0%, rgba(164,210,251,0.30) 50%, rgba(157,189,251,0.60) 100%);
  // border-radius: 6px;
  // color: #fff;
}
.context-menu {
  margin-left: -200px;
  position: absolute;
  // background: white;
  background: #fff;

  // border: 1px dashed #ccc;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
  // margin-left:-200px;
}

.context-menu li {
  padding: 8px 16px;
  cursor: pointer;
  // margin-left:-200px;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}

.fr {
  float: right;
}

.work-container {
  height: 100%;

  .result-container {
    padding: 30px;

    .elcol-title {
      display: flex;
      overflow: hidden;
      width: 100%;
      height: 48px;

      .elcol-title-text {
        // float: left;
        padding-left: 10px;
        text-align: left;
        width: 40%;
        height: 100%;
      }

      .elcol-title-text2 {
        font-size: calc(100vw *  14 / 1920);
        // color: #d1d7de;
        // float: left;
        width: 75px;
      }

      .elcol-input {
        float: left;
        width: calc(60% - 75px);
        border: none !important;
      }
    }
  }
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

.ejdhl {
  margin-top: 43px;
  background-color: #fff;
  height: 900px;
  //  background: url(../assets/img-left.png) no-repeat center;
  // background-size: cover;
  // background-position: center;
  // transition: ease-out 0.4s;
}

// .ejdhlTitle {
//   height: calc(100vh * 80 / 1080);
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   transition: ease-out 0.4s;
//   background-image: linear-gradient(
//     76deg,
//     #07389c 0%,
//     #3d86d1 0%,
//     #3448b3 100%
//   );

//   img {
//     width: 120px;
//     height: 48px;
//   }

//   p {
//     border: 1.54px dashed rgba(0, 0, 0, 0);
//     font-family: PingFangSC-Semibold;
//     font-size: calc(100vw *  24 / 1920);
//     color: #000000;
//     text-align: left;
//     color: #fff;
//     display: flex;
//     align-items: center;
//     margin-left: 5px;
//   }
// }

.el-header1 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  // background: url(../assets/jsbg.png) no-repeat center;
  background-color: #fff;
  .ai-header {
    width: 100%;
    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: 40px;
        height: calc(100vh * 40/ 1080);
        border-radius: 20px;
        // background: #f4f6f8;
        border: 1px dashed #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 42px;
            width: 130px;
            color: #000;
            font-size: calc(100vw *  14 / 1920);
            line-height: 16px;
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: 16px;
              height: 16px;
              margin-right: 14px;
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: 20px;
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw *  12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  color: #333;
  text-align: center;
  // line-height: 160px;
  // width: calc(100% - 300px);
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 /1080);
  padding: 20px 10px;
  overflow: hidden;

  .ai-gai {
    position: fixed;
    /* top: 0; */
    left: 0;
    bottom: 0;
    height: 91%;
    // background: rgba(122, 151, 255, 0.6);
    z-index: 999;
  }

  .ai-body {
    transition: ease-out 0.4s;
    width: 100%;
    height: 100%;
    // background: red;
    float: left;
    overflow: hidden;

    .ai-body-left {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      width: 444px;
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-left: 10px;
      float: left;

      .ai-body-left-top {
        width: 100%;
        height: calc(100% - calc(100vh * 80 / 1080));
        overflow-y: scroll;
      }

      .ai-body-left-bottom {
        width: 100%;
        height: 90px;

        // padding-top: 30px;
        .ai-body-left-bottom-button {
          height: calc(100vh * 40/ 1080);
          // font-weight: 600;
          font-size: calc(100vw *  14 / 1920);
          letter-spacing: 0px;
          flex-grow: 1;
          color: #fff;
          margin: 0 25px;
          cursor: pointer;
          line-height: calc(100vh * 40/ 1080);
          margin-top: 30px;
          background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 20px;
          font-family: PingFangSC-Regular;
        }
      }

      .ai-body-left-bottom2 {
        width: 100%;
        height: calc(100vh * 80 / 1080);
        padding-top: 30px;

        .repeat {
          width: 188px;
          float: left;
          height: calc(100vh * 40/ 1080);
          font-size: calc(100vw *  14 / 1920);
          flex-grow: 1;
          margin-left: 25px;
          cursor: pointer;
          line-height: calc(100vh * 40/ 1080);
          background: #f4f6f8;
          border: 1px dashed rgba(230, 230, 230, 1);
          border-radius: 20px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #666666;
          letter-spacing: 0;
          text-align: center;
          // font-weight: 600;
        }

        .ai-body-left-bottom-button {
          width: 188px;
          float: left;
          height: calc(100vh * 40/ 1080);
          flex-grow: 1;
          margin-left: 14px;
          cursor: pointer;
          line-height: calc(100vh * 40/ 1080);
          background-image: linear-gradient(102deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 20px;
          margin-top: 0px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          // font-weight: 600;
        }
      }

      .ai-step {
        margin-top: 20px;
        width: calc(100% - 40px);
        margin: 0 20px;
        margin-top: 5px;
        height: calc(100vh * 40/ 1080);
      }
    }

    .ai-body-right {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      width: calc(100% - 480px);
      height: 100%;
      background: #ffffff;
      border-radius: 8px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-left: 20px;
      float: left;

      .ai-body-start {
        width: 100%;
        height: 100%;

        .pic_bkg1 {
          // width: calc(100vw * 670 / 1920);
          // height: calc(100vw * 590 / 1920);
          width: 670px;
          height: 590px;
          background: url(../assets/img-bg1.png) no-repeat center;
          // background: url(../assets/img-bg1.png) no-repeat center;
          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 70px;
          position: relative;
        }

        .pic_bkg {
          width: 528px;
          height: 518px;
          // z-index: 15;
          background: url(../assets/img-ai.png) no-repeat center;
          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 118px;
          position: relative;
        }

        .pic_font {
          position: absolute;
          margin-left: auto;
          margin-right: auto;
          left: 0;
          right: 0;
          bottom: 116px;
          width: 255px;
          height: calc(100vh * 40/ 1080);
          border: 1.54px dashed rgba(0, 0, 0, 0);
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  28 / 1920);
          color: #000000;
          text-align: center;
          font-weight: 600;
        }

        .title_message {
          position: absolute;
          margin-left: auto;
          margin-right: auto;
          left: 0;
          right: 0;
          bottom: 82px;
          text-align: center;
          line-height: 16px;
          margin-top: 10px;
          height: 22px;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw *  14 / 1920);
          color: #999999;
          letter-spacing: 0;
          font-weight: 400;
        }

        .pic_step {
          width: 551px;
          height: 142px;
          z-index: 15;
          background: url("../assets/pic_step.png");
          background-size: contain;
          margin: auto;
        }
      }

      .ai-body-art {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;

        .over {
          overflow: auto;
        }

        .fir-textarea {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 96.5%;
          background: #f8f9fd;
          border: 1px dashed rgba(229, 232, 245, 1);
          border-radius: 4px;
          margin: 20px;
          margin-bottom: 0;
          height: 158px;

          ::v-deep(.el-textarea__inner) {
            font-size: 14px !important;
            background-color: #f8f9fd !important;
            height: 100% !important;
            font-family: PingFangSC-Regular;
            padding: 13px 18px 33px 16px;
          }
        }

        .fir-textarea-max {
          height: 95% !important;
        }

        ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
          display: none !important;
        }

        ::v-deep(.ql-blank) {
          display: none !important;
        }

        ::v-deep(.ql-container.ql-snow) {
          border: 0;
        }
      }
    }

    .ai-tab {
      width: 230px;
      height: calc(100vh * 40/ 1080);
      margin: 0 auto;
      margin-top: 30px;
      background: #f4f6f8;
      border: 1px dashed #eeeff0;
      border-radius: 20px;

      .tab-item {
        width: 50%;
        height: calc(100vh * 40/ 1080);
        // line-height: 16px;
        float: left;
        line-height: calc(100vh * 40/ 1080);
        cursor: pointer;

        font-family: PingFangSC-Semibold;
        font-size: calc(100vw *  14 / 1920);
        color: #9094a5;
        letter-spacing: 0;
        text-align: center;
      }

      .activedTab {
        border-radius: 20px;

        background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
        box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
        color: #ffffff;
      }
    }

    .tab-item-fir {
      width: 100%;
      height: 536px;
      padding: 0 25px;

      .fir-title {
        color: #222;
        font-weight: 500;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 16px;
        overflow: hidden;
        margin-top: 20px;
        margin-bottom: 20px;
        height: 20px;

        .fir-kuai {
          width: 6px;
          height: 16px;
          margin-right: 8px;
          float: left;
          // margin-top: 2px;
          background: #4081ff;
          border-radius: 1.5px;
        }

        .fir-title-p {
          line-height: 16px;
          float: left;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw *  14 / 1920);
          color: #333333;
          letter-spacing: 0;
          font-weight: 400;
        }
      }

      .fir-alert {
        margin-top: 10px;
        height: 35px;
      }

      .ai-dialog {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        row-gap: 1px;
        width: 100%;
        height: -moz-fit-content;
        height: 290px;
        max-height: 294px;
        padding: 7px;
        box-shadow: 0 20px 40px 4px #e4e4e524;
        margin-top: 10px;
        transition: ease-out 0.4s;
        background: #ace9ff;
        border: 1px dashed rgba(90, 206, 255, 1);
        border-radius: 4px;

        .ai-d-title {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          width: 100%;
          height: -moz-fit-content;
          height: fit-content;
          margin: 0;
          padding: 1px 3px 2px 2px;
          font-family: PingFangSC-Regular;
          font-size: calc(100vw *  14 / 1920);
          color: #313733;
          letter-spacing: 0;
          font-weight: 400;

          .ai-d-title-p {
            flex-grow: 1;
            line-height: 16px;
            text-align: left;
            font-family: PingFangSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #313733;
            letter-spacing: 0;
            font-weight: 400;
            margin-bottom: 10px;
          }

          img {
            width: 18px;
            height: 18px;
            cursor: pointer;
          }
        }

        .ai-d-body {
          width: 100%;
          height: calc(100% - 44px);
          overflow: hidden;
          background: #ffffff;
          border-radius: 4px;

          .hints-control {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
            padding: 14px 14px 0;
            height: 30px;

            .hint-icon {
              flex-grow: 0;
              flex-shrink: 0;
              width: 20px;
              height: 20px;
              margin-right: 6px;
              background-size: contain;
              background-image: url("../assets/icon_fire.png");
            }

            .hint-description {
              font-weight: 600;
              line-height: 14px;
              font-family: SourceHanSansSC-Bold;
              font-size: calc(100vw *  14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 700;
            }
          }

          .ai-tj-body {
            width: 100%;
            // height: 100%;
            // overflow: hidden;
            height: 200px;
            overflow-y: auto;

            /* 垂直滚动条 */
            .ai-tj-item {
              padding: 14px 14px 0;
              line-height: 12px;
              width: 50%;
              text-align: left;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              float: left;
              cursor: pointer;
              height: 30px;
              font-family: PingFangSC-Regular;
              font-size: calc(100vw *  14 / 1920);
              color: #313733;
              letter-spacing: 0;
              font-weight: 400;
              // &:hover {

              // }
            }
          }
        }
      }

      // ***滚动条样式
      .ai-tj-body::-webkit-scrollbar {
        width: 6px;
        /* 滚动条的宽度 */
      }

      .ai-tj-body::-webkit-scrollbar-track {
        background: #fff;
        /* 滚动条的背景色 */
      }

      .ai-tj-body::-webkit-scrollbar-thumb {
        background: #488aff;
        /* 滚动条的滑块颜色 */
      }

      .fir-textarea {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        margin-top: 14px;
        height: 158px;
        background: #f8f9fd;
        // border: 1px dashed rgba(229, 232, 245, 1);
        border-radius: 4px;

        ::v-deep(.el-textarea__inner) {
          font-size: 14px !important;
          background-color: #f8f9fd !important;
          height: 100% !important;
          font-family: PingFangSC-Regular;
        }
      }

      .fir-textarea-height {
        height: 460px !important;
      }
    }
  }
}

::v-deep(.el-textarea__inner) {
  padding: 16px;
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw *  12 / 1920);
  right: 10px;
  height: 10px;
}

.ai-nav {
  // width: 180px;
  // height: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  transition: ease-out 0.4s;

  .title {
    width: 300px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    letter-spacing: 0px;
    height: 20px;
    line-height: 20px;
    padding-top: 20px;
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    width: 100%;
    padding-top: 45px;
    height: calc(100% - calc(100vh * 80 / 1080));
  }
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px dashed rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px dashed #f0f3fa;
  }

  .options {
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;
      cursor: pointer;

      :first-child {
        margin-top: 0px;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

// 导航项
.nav-item {
  height: calc(100vh * 40/ 1080);
  background: transparent;
  line-height: 20px;
  white-space: pre-wrap;
  // background-image: linear-gradient(270deg,
  //     rgba(30, 75, 202, 0.39) 0%,
  //     rgba(59, 130, 234, 0.28) 100%);
  // border: 1px dashed rgba(255, 255, 255, 0.2);
  // border-radius: 4px;
  background: #f4f6f8;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
  cursor: pointer;
  // margin: 0 auto;
  margin-top: 20px;
  margin-left: 100px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #9094a5;
  border-radius: 20px;
  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    letter-spacing: 0;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.choose {
  border-radius: 20px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  color: #ffffff;
}

.clbutton {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 40px;
}

.clbutton1 {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}

.clbutton2 {
  left: 180px;
}

.clbutton12 {
  // left: 200px;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 280px;
}

.showUp {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 60px;
  height: calc(100vh * 40/ 1080);
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.showUp1 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 20px;
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: 10px;
  left: 6px;
  height: calc(100vh * 40/ 1080);
  bottom: 20px;
  // z-index: 9999;
  cursor: pointer;

  &:hover {
    // background-color: rgba(46,98,245,.1)!important;
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: 18px;
    height: 18px;
    float: left;
  }

  p {
    float: left;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 130px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-drawer__header) {
  display: none;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-notification__group) {
  height: auto !important;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.progressClass {
  width: 100%;
  height: 26px;
  position: fixed;
  bottom: 48px;
  right: 0;
  z-index: 999;

  .progress {
    position: absolute;
    right: 52px;
    transition: ease-out 0.4s;
  }
}

::v-deep(.el-progress-bar__innerText) {
  padding: 7px !important;
  color: #fff !important;
}

.menu_label {
  width: 100%;
  border-radius: 4px;
  margin-top: 10px;
  min-height: 158px;
  height: auto;
  padding: 15px 16px;
  // max-height: 158px;
  background: #f8f9fd;
  border: 1px dashed rgba(229, 232, 245, 1);
  border-radius: 4px;
  padding: 15px 0px;
}

.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: 383px;
  word-wrap: break-word;
  text-align: left;
  margin-left: 5px;
  margin-bottom: 5px;
  float: left;
}

.el-main .ai-body .tab-item-fir .menu_label:active {
  background: #fff;
  border: 1px dashed #4170f6;
}

// .el-main .ai-body .tab-item-fir .menu_label:focus {
//   background: #fff;
//   border: 1px dashed #4170f6;
// }

.pass_input {
  // float: left;
  width: 100%;
  height: calc(100vh * 40/ 1080);
}

::v-deep(.el-alert__title) {
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-drawer__open .el-drawer.ltr) {
  width: 100% !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0;
}

::v-deep(.el-alert) {
  padding: 8px 8px;
}

.fir-textarea1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: 1px dashed rgba(229, 232, 245, 1);
  border-radius: 4px;
  margin: 20px;
  margin-bottom: 0;
  height: 158px;

  ::v-deep(.el-textarea__inner) {
    font-size: 14px !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: 13px 18px 33px 16px;
  }
}

::v-deep(.el-collapse) {
  border: 0px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: 1px dashed #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40/ 1080);
  border-radius: 4px;
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: 4px;
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: 4px;
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: -10px;
    top: 2px;
    width: 4px;
    height: 15px;
    background: #5585f0;
    border-radius: 2px;
  }
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: 6px;
  /* 滚动条的宽度 */
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
  /* 滚动条的背景色 */
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
  /* 滚动条的滑块颜色 */
}

.elcol-title-left {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 20px;
  margin-left: 10px;
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 48px;
}

.elcol-title-text {
  // float: left;
  padding-left: 10px;
  text-align: left;
  width: 40%;
  height: 100%;
}

.elcol-title-text2 {
  font-size: calc(100vw *  14 / 1920);
  color: #d1d7de;
  // float: left;
  width: 75px;
}

.elcol-input {
  float: left;
  width: calc(60% - 75px);
  border: none !important;
}

/* 整个滚动条 */
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 10px;
  /* 对应横向滚动条的宽度 */
  height: 10px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: gray;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #fff;
  border-radius: 32px;
}
.el-icon {
  --color: inherit;
  height: 1em;
  width: 1em;
  line-height: 1em;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  fill: currentColor;
  color: var(--color);
  font-size: inherit;
  margin-right: 10px;
}
.el-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  height: 32px;
  white-space: nowrap;
  cursor: pointer;
}
.el-input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  // height: 35px;
  line-height: 35px;
  outline: 0;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
// .el-dialog__header{
// display: flex;
//     flex-direction: row;
//     flex-shrink: 0;
//     font-size: 1em;
//     font-weight: 700;

//     background-color: #f5f7fa;
//     border-radius: var(--vxe-border-radius) var(--vxe-border-radius) 0 0;
//     -webkit-user-select: none;
//     -moz-user-select: none;
//     user-select: none;
// }
// .el-dialog__header {
//     padding: 20px 20px 10px;
//     background-color: red !important;
// }
:deep(.el-dialog__header) {
  padding: 15px 20px 10px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  font-weight: 700;
  // border-bottom: 1px solid var(--vxe-modal-border-color);
  // background-color: var(--vxe-modal-header-background-color);
  border-radius: var(--vxe-border-radius) var(--vxe-border-radius) 0 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
:deep(.el-dialog__title) {
  font-size: calc(100vw *  15 / 1920);
  color: #606266;
}
:deep(.el-form .el-form-item__label) {
  font-weight: 700;
  // margin-right:-100px;
}
:deep(.el-form-item) {
  margin-bottom: 20px;
}
:deep(.el-form-item__label) {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: calc(100vw *  14 / 1920);
  color: #606266;
  line-height: calc(100vh * 40/ 1080);
  box-sizing: border-box;
  margin-left: 60px;
}
.normalPage .btnIcon .el-icon {
  font-size: 20px !important;
  color: #006eff;
}

:deep(.el-form-item__error) {
  color: #f56c6c;
  font-size: calc(100vw *  12 / 1920);
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 32px;
}
.el-input123 {
  width: 200px;
  // margin-left: 20px;
  margin-right: 10px;
}
.el-input124 {
  width: 200px;
  // margin-left: 140px;
  // margin-right: 150px;
}
</style>