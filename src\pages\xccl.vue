<!-- 宣传材料 -->
<template>
  <div class="work-container" v-loading="con_loading">
    <el-drawer
      title=""
      :visible.sync="drawer"
      :direction="direction"
      style="width: 80%"
      :before-close="handleClosedrawer"
    >
      <div style="display: flex; height: 94%">
        <div style="width: 50%; height: calc(100% - 30px); margin-top: 20px">
          <!-- <el-input class="fir-textarea1 fir-textarea-max1" type="textarea" placeholder=""
              v-model="textarea4"></el-input> -->
          <div
            style="
              margin-left: 20px;
              height: 100%;
              overflow: overlay;
              background-color: rgb(255, 255, 255);
              padding: 0px 20px;
              font-size: calc(100vw *  14 / 1920);
            "
            v-html="textarea4"
            contenteditable="true"
            class="result-content"
          ></div>
        </div>
        <div
          class="result-container"
          style="
            width: calc(50% - 40px);
            height: calc(100% - 30px);
            background-color: rgb(225, 231, 243);
            margin: 20px;
            padding-top: 16px;
          "
        >
          <div
            class="result-title"
            style="
              text-align: left;
              font-size: calc(100vw *  16 / 1920);
              margin-bottom: 10px;
              font-weight: bold;
            "
          >
            全部校对结果
          </div>
          <div style="height: 96%; overflow: overlay" class="result-content">
            <div v-for="(item, index) in correctionList">
              <!-- <template slot="title"> -->
              <!-- <div class="elcol-title">
                <p class="elcol-title-text">{{ item.source }}</p>
                <p class="elcol-title-text2">建议替换</p>
                <p class="elcol-title-text">{{ item.target }}</p> -->
              <!-- <el-input class="elcol-input" v-model="input" placeholder="请输入内容"></el-input> -->
              <!-- </div> -->
              <!-- </template> -->
              <el-collapse
                v-model="activeNames"
                @change="highlightError(item.source, index)"
                style="margin-bottom: 10px"
                accordion
              >
                <el-collapse-item :name="index">
                  <template slot="title">
                    <div style="width: 96%">
                      <div class="elcol-title">
                        <div
                          class="elcol-title-left"
                          :style="{
                            backgroundColor: getBackgroundColor(index),
                          }"
                        ></div>
                        <!-- <p class="elcol-title-text">{{ item.source }}</p> -->
                        <el-input
                          class="elcol-input"
                          v-model="item.source"
                          placeholder="请输入内容"
                        ></el-input>
                        <p class="elcol-title-text2">建议替换</p>
                        <!-- <p class="elcol-title-text">{{ item.target }}</p> -->
                        <el-input
                          class="elcol-input"
                          v-model="item.target"
                          placeholder="请输入内容"
                        ></el-input>
                      </div>
                    </div>
                  </template>
                  <div
                    style="
                      height: calc(100vh * 40/ 1080);
                      text-align: left;
                      padding-left: 20px;
                      line-height: calc(100vh * 40/ 1080);
                      border-bottom: 1px solid #e1e7f3;
                    "
                  >
                    拼写：政治语素错误
                  </div>
                  <!-- <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                  <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div> -->
                  <div style="height: 8px; margin-top: 6px">
                    <span
                      @click="ignore()"
                      style="
                        float: right;
                        margin-right: 10px;
                        color: red;
                        cursor: pointer;
                      "
                      >忽略</span
                    >
                    <span
                      @click="highlightChange(item.source, item.target)"
                      style="
                        float: right;
                        margin-right: 10px;
                        color: #66b1ff;
                        cursor: pointer;
                      "
                      >替换</span
                    >
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div
          type="primary"
          class="fr clbutton1"
          style="margin-right: 20px; cursor: pointer"
          @click="save"
        >
          关 闭
        </div>
        <!-- <el-button type="danger" @click="fanhui"></el-button> -->
      </div>
    </el-drawer>
<SideNavigation :navShow="navShow" />

    <el-container>
      <el-header
        style="transition: ease-out 0.4s"
        :style="
          navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
        "
      >
        <div class="ai-header">
          <div class="ai-bar">
          <HeadNavigation />
          </div>
        </div>
      </el-header>
      <el-main
        style="transition: ease-out 0.4s"
        :style="
          navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
        "
      >
        <div
          class="ai-gai"
          v-loading="loading"
          element-loading-text="文章生成中，请稍候..."
          element-loading-background="rgba(255, 255, 255, 0.7)"
          :style="
            navShow
              ? 'width: 870px; margin-left:900px;height:500px;margin-bottom: 150px;'
              : 'width: 528px;'
          "
          style="transition: ease-out 0.4s"
          v-if="mask"
        ></div>
        <div
          class="ai-gai"
          v-loading="loading1"
          element-loading-text="生成中，请稍候..."
          element-loading-background="rgba(255, 255, 255, 0.7)"
          :style="
            navShow
              ? 'width: calc(100vw *  1000/ 1920); margin-left:850px;height:700px;margin-bottom:100px;'
              : 'width: 600px;height:400px;'
          "
          style="transition: ease-out 0.4s"
          v-if="mask"
        ></div>
        <!-- <div
            class="ai-gai"
            v-loading="loading"
            element-loading-text="大纲生成中，请稍后..."
            element-loading-background="rgba(255, 255, 255, 0.7)"
            :style="navShow ? 'width: 770px' : 'width: 528px'"
            style="transition: ease-out 0.4s"
          ></div> -->
        <!-- <div v-if="maskAll && progressPercent <100" class="progressClass">
            <el-progress class="progress" :style="navShow
              ? 'width: calc(100% - 870px)'
              : 'width: calc(100% - 637px)'
              " :text-inside="true" :stroke-width="26" :percentage="progressPercent"
              style="transition: ease-out 0.4s"
              ></el-progress>
          </div> -->
        <!-- <div class="progressClass">
            <el-progress
              class="progress"
              :style="
                navShow
                  ? 'width: calc(100% - 870px)'
                  : 'width: calc(100% - 637px)'
              "
              style="height: 20px"
              :text-inside="true"
              :stroke-width="26"
              :percentage="progressPercent"
            ></el-progress>
          </div> -->
        <div class="ai-body">
          <div class="ai-body-left">
            <div class="ai-body-left-top">
              <div v-if="jydis">
                <custom-steps
                  :steps="steps"
                  :current-step="currentStep"
                ></custom-steps>
                <!-- <el-steps
                  :active="stepActived"
                  finish-status="success"
                  simple
                  class="ai-step"
                >
                  <el-step title="基本信息"></el-step>
                  <el-step title="大纲"></el-step>
                  <el-step title="文章"></el-step>
                </el-steps> -->
                <!-- <div class="ai-tab">
                    <div @click="clickTab('1')" class="tab-item" :class="{ activedTab: curIndex == 1 }">
                      基本信息
                    </div>
                    <div @click="clickTab('2')" class="tab-item" :class="{ activedTab: curIndex == 2 }">
                      AI大纲
                    </div>
                  </div> -->
                <div class="ai-tab">
                  <div
                    @click="clickTab('1')"
                    class="tab-item"
                    :class="{ activedTab: curIndex == 1 }"
                  >
                    基本信息
                  </div>
                </div>
                <div class="tab-item-fir" v-if="curIndex === '1'">
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">填写写作类型</p>
                  </div>
                  <!-- <el-input class="fir-textarea" type="textarea" placeholder="参考：市政府办公厅" v-model="textarea"
                      maxlength="30" @focus="textareaFocus" show-word-limit>
                    </el-input> -->
                  <el-input
                    class="fir-textarea"
                    type="textarea"
                    placeholder="参考：采访类、工作动态类"
                    v-model="textarea111"
                    show-word-limit
                  >
                  </el-input>
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">填写标题</p>
                  </div>
                  <!-- <el-input class="fir-textarea" type="textarea" placeholder="参考：市政府办公厅" v-model="textarea"
                      maxlength="30" @focus="textareaFocus" show-word-limit>
                    </el-input> -->
                  <el-input
                    class="fir-textarea"
                    type="textarea"
                    placeholder="参考：组织部部长下基层宣传"
                    v-model="textarea"
                    show-word-limit
                  >
                  </el-input>

                  <div class="ai-dialog" v-if="dialogShow">
                    <div class="ai-d-title">
                      <p class="ai-d-title-p">
                        请在工作背景部分填写部门及岗位信息，建议您参考直接选择推荐词汇，生成效果更佳
                      </p>
                      <img
                        src="../assets/close.png"
                        @click="closeDialog"
                        alt=""
                      />
                    </div>
                    <div class="ai-d-body">
                      <div class="hints-control">
                        <div class="hint-icon hot"></div>
                        <div class="hint-description">热门推荐</div>
                      </div>
                      <div class="ai-tj-body">
                        <!-- {{ item }} -->
                        <!-- <p @click="getText" class="ai-tj-item">
                          市政府办公厅主任
                        </p>
                        <p @click="getText" class="ai-tj-item">市委宣传部部长</p>
                        <p @click="getText" class="ai-tj-item">市委政法委书记</p>
                        <p @click="getText" class="ai-tj-item">市网信办主任</p> -->
                        <p
                          @click="getText(item)"
                          class="ai-tj-item"
                          v-for="(item, index) in gzbj"
                          :key="index"
                        >
                          {{ item }}
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- <div class="fir-title">
                      <div class="fir-kuai"></div>
                      <p class="fir-title-p">填写宣传内容摘要</p>
                    </div> -->
                  <!-- <div class="menu_label"> -->
                  <!-- <el-tag :key="tag" type="info" v-for="tag in dynamicTags" closable :disable-transitions="false"
                        @close="handleClose(tag)">
                        {{ tag }}
                      </el-tag> -->
                  <!-- <el-input class="input-new-tag pass_input" v-if="inputVisible" v-model="inputValue" ref="saveTagInput"
                        size="small" placeholder="参考:10月17日，市委组织部部长同志到基层调研" @keyup.enter.native="handleInputConfirm"
                        @blur="handleInputConfirm">
                      </el-input> -->
                  <!-- </div> -->
                  <!--  -->
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">填写宣传内容摘要</p>
                  </div>
                  <!-- <el-input class="fir-textarea" type="textarea" placeholder="参考：市政府办公厅" v-model="textarea"
                      maxlength="30" @focus="textareaFocus" show-word-limit>
                    </el-input> -->
                  <el-input
                    class="fir-textarea"
                    type="textarea"
                    placeholder="参考:10月17日，市委组织部部长同志到基层宣传干部心得"
                    v-model="textarea222"
                    show-word-limit
                  >
                  </el-input>
                  <!--  -->

                  <div class="ai-dialog" v-if="dialog2Show">
                    <div class="ai-d-title">
                      <p class="ai-d-title-p">
                        请在关键词部分填写工作要点，建议您直接选择或参考推荐词汇，生成效果更佳
                      </p>
                      <img
                        src="../assets/close.png"
                        @click="closeDialog2"
                        alt=""
                      />
                    </div>
                    <div class="ai-d-body">
                      <div class="hints-control">
                        <div class="hint-icon hot"></div>
                        <div class="hint-description">热门推荐</div>
                      </div>
                      <div class="ai-tj-body">
                        <p
                          v-for="(item, index) in secRecommend"
                          :key="index"
                          @click="getText2(item)"
                          class="ai-tj-item"
                          :title="item"
                        >
                          {{ item }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="tab-item-fir" v-if="curIndex === '2'">
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">大纲</p>
                  </div>
                  <el-alert
                    class="fir-alert"
                    title="根据自己的需要修改大纲，这样生成的报告更加精准哦~"
                    type="success"
                  >
                  </el-alert>
                  <el-input
                    class="fir-textarea fir-textarea-height"
                    type="textarea"
                    placeholder="参考：市政府办公厅"
                    v-model="textarea3"
                  >
                  </el-input>
                </div>
              </div>
              <div class="tab-item-fir" v-else>
                <div
                  style="height: 100%; overflow: overlay; margin-top: 20px"
                  class="result-content"
                >
                  <div v-for="(item, index) in correctionList">
                    <!-- <template slot="title"> -->
                    <!-- <div class="elcol-title">
                <p class="elcol-title-text">{{ item.source }}</p>
                <p class="elcol-title-text2">建议替换</p>
                <p class="elcol-title-text">{{ item.target }}</p> -->
                    <!-- <el-input class="elcol-input" v-model="input" placeholder="请输入内容"></el-input> -->
                    <!-- </div> -->
                    <!-- </template> -->
                    <el-collapse
                      v-model="activeNames"
                      @change="highlightError(item.source, item.wordNo)"
                      style="margin-bottom: 10px"
                      accordion
                    >
                      <el-collapse-item :name="index">
                        <template slot="title">
                          <div style="width: 96%">
                            <div class="elcol-title" style="display: flex">
                              <div
                                class="elcol-title-left"
                                :style="{
                                  backgroundColor: getBackgroundColor(index),
                                }"
                              ></div>
                              <p class="elcol-title-text elcol-input">
                                {{ item.wordNo }}
                              </p>
                              <!-- <el-input :title="item.wordNo" class="elcol-input" v-model="item.wordNo"
                                  placeholder="请输入内容" style="width: 40%;"></el-input> -->
                              <p class="elcol-title-text2">建议替换</p>
                              <!-- <p class="elcol-title-text">{{ item.target }}</p> -->
                              <el-input
                                :title="item.wordYes"
                                class="elcol-input"
                                v-model="item.wordYes"
                                placeholder="请输入内容"
                                style="width: 40%"
                              ></el-input>
                            </div>
                          </div>
                        </template>
                        <div
                          style="
                            height: calc(100vh * 40/ 1080);
                            text-align: left;
                            padding-left: 20px;
                            line-height: calc(100vh * 40/ 1080);
                            border-bottom: 1px solid #e1e7f3;
                          "
                        >
                          {{ item.eq }}
                        </div>
                        <!-- <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                  <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div> -->
                        <div style="height: 8px; margin-top: 6px">
                          <span
                            @click="ignore()"
                            style="
                              float: right;
                              margin-right: 10px;
                              color: red;
                              cursor: pointer;
                            "
                            >忽略</span
                          >
                          <span
                            @click="highlightChange(item.source, item.target)"
                            style="
                              float: right;
                              margin-right: 10px;
                              color: #66b1ff;
                              cursor: pointer;
                            "
                            >替换</span
                          >
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </div>
                <div
                  type="primary"
                  class="clbutton2 clbutton1"
                  style="margin-right: 20px; cursor: pointer"
                  @click="save"
                >
                  关 闭
                </div>
              </div>
            </div>
            <div class="ai-body-left-bottom" v-if="jydis == true">
              <div
                v-if="curIndex == '1'"
                @click="firstNextStep"
                class="ai-body-left-bottom-button"
              >
                下一步：生成文章
              </div>
              <div v-if="curIndex == '2'" class="ai-body-left-bottom2">
                <div class="repeat" @click="repeatStep">重新生成大纲</div>
                <div @click="success" class="ai-body-left-bottom-button">
                  <span v-if="buttonShow">下一步：生成文章</span>
                  <span v-else>重新生成文章</span>
                </div>
              </div>
            </div>
          </div>
          <div class="ai-body-right">
            <div class="ai-body-start" v-if="!artShow">
              <!-- <div class="ai-body-start" v-if="false"> -->
              <!-- <div class="pic_bkg">
                  <div class="pic_font">欢迎使用AI写作</div>
                  <div class="title_message">
                    采用AI大模型智能生成文章，仅需3步即可一键成文，快去试试吧~
                  </div>
                </div>
  
                <div class="pic_step"></div> -->
              <div class="pic_bkg1"></div>
            </div>

            <div class="ai-body-art" v-else>
              <vue-editor
                class="fir-textarea fir-textarea-max"
                v-model="textarea4"
                ref="editor"
                @contextmenu.native="showContextMenu"
              ></vue-editor>
              <div
                v-if="contextMenuVisible"
                class="context-menu"
                :style="contextMenuStyle"
              >
                <ul>
                  <!-- el-icon-edit -->
                  <li @click="sendToBackend()">
                    <i class="el-icon-edit"></i>

                    续写
                  </li>
                  <!-- el-icon-circle-plus-outline -->
                  <li @click="sendToBackend2()">
                    <i class="el-icon-circle-plus-outline"></i>

                    扩写
                  </li>
                  <!-- el-icon-remove-outline -->
                  <li @click="sendToBackend3()">
                    <i class="el-icon-remove-outline"></i>
                    缩写
                  </li>
                  <li @click="sendToBackend4()">
                    <i class="el-icon-magic-stick"></i>
                    润色
                  </li>
                </ul>
              </div>
              <div
                style="cursor: pointer"
                v-if="progressPercent == 100"
                class="clbutton"
                v-clipboard:copy="getPlainText(textarea4)"
                v-clipboard:success="onCopySuccess"
                v-clipboard:error="onCopyError"
              >
                复制全文
              </div>
              <div
                style="cursor: pointer"
                v-if="progressPercent == 100"
                class="clbutton1"
                @click="jiaoyan"
              >
                校验
              </div>
              <div
                style="cursor: pointer"
                v-if="progressPercent == 100"
                class="clbutton12"
                @click="baocun()"
              >
                保存
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import SideNavigation from "../components/SideNavigation.vue"; // 引入侧边导航组件
import HeadNavigation from "../components/HeadNavigation.vue"; // 引入头部导航组件

import { VueEditor } from "vue2-editor";
import CustomSteps from "../components/Step.vue";
import store from "../store/index";
import Quill from "quill";
let BlockEmbed = Quill.import("blots/block/embed");
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute(
      "style",
      "padding: 20px; border: 1px solid #488aff; color:red; position: relative;"
    );
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement("button");
    insertButton.innerHTML = "应用";
    insertButton.className = "btn-insert";
    insertButton.style.position = "absolute";
    // 按钮颜色
    insertButton.style.backgroundColor = "#488aff";
    // 字体为白色
    insertButton.style.color = "white";
    // insertButton.style.top = '10px';
    insertButton.style.right = "70px";
    insertButton.addEventListener("click", () => {
      // console.log(value.editorClass, '编辑器')
      // console.log(value.newText, '续写内容！')
      // // 这里的位置，是替换的传position，是插入的传position加length
      // console.log(value.Position, '位置')
      // console.log(value.selectedTextlength, '长度？？？？？？？？')
      // console.log(value.editorClass.getSelection(), '光标位置？？？？')
      if (value.flag == 1) {
        value.editorClass.insertText(
          value.Position + value.selectedTextlength,
          value.newText
        );
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement("button");
    cancelButton1.innerHTML = "取消";
    cancelButton1.className = "btn-cancel";
    cancelButton1.style.position = "absolute";
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = "#e64721";
    // 字体为白色
    cancelButton1.style.color = "white";
    cancelButton1.style.right = "10px";
    cancelButton1.addEventListener("click", () => {
      node.remove();
      // alert('取消按钮点击');
      // // 这里可以添加取消按钮的逻辑
    });

    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = "customBlock";
CustomBlock.tagName = "div";

Quill.register({
  "formats/customBlock": CustomBlock,
});
import {
  getSecDtDatas,
  getSuccessInfo,
  getSuccessInfoItem,
  getLabel1,
  getLabel2,
  testcheck,
  reflections,
  // 宣传材料接口
  xccl,
  bcwz,
  rewrite,
} from "../api/home.js"; // 接口请求
import { mapState } from "vuex";

 export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id
    username() {
      return this.$store.state.username;
    },
    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 600}px`,
      };
    },
  },
  data() {
    return {
      newText: "",
      newText1: "",
      newText2: "",
      newText3: "",
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      selectedText: "",
      loading1: false,
      con_loading: false,
      correctionList: [],
      jydis: true,
      navShow: true,
      textarea: "",
      textarea111: "",
      textarea222: "",

      textarea2: [],
      textarea3: "",
      textarea4: "",
      dialogShow: false,
      dialog2Show: false,
      gzbj: [],
      stepActived: 1,
      loading: false,
      // loading: true,
      curIndex: "1",
      articles: "",
      artShow: false,
      mask: false,
      maskAll: false,
      /**
       * @description:  功能性按钮
       * @return {*}    传入组件->FunctionTable
       */
      // steps: [{ title: "基本信息" }, { title: "大纲" }, { title: "文章" }],
      steps: [{ title: "基本信息" }, { title: "文章" }],
      currentStep: 1, // 初始化当前步骤为第二个步骤
      secRecommend: [], // 第二步推荐
      dynamicTags: [],
      inputVisible: true,
      inputValue: "",
      resDataItem: [],
      progressPercent: 0,
      fetchResPrams: [],
      buttonShow: true,
      drawer: false,
      direction: "ltr",
      activeNames: [],
      plainTextContent: "",
      // asyncStatus: true
    };
  },
  components: {
    // 注册组件
    CustomSteps,
    VueEditor,
    SideNavigation,
    HeadNavigation,

  },
  mounted() {
    // this.getInfo()
  },
      methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickTopbatter() {
      this.$router.push("/batter");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    toxg() {
      // alert(1)
      this.$router.push("/grzx4");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    showContextMenu(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener("click", this.hideContextMenu);
      }
    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener("click", this.hideContextMenu);
    },
    // 续写接口
    async sendToBackend() {
      this.loading1 = true;
      this.mask = true;
      let params1 = {
        text: this.selectedText,
        flag: "1",
      };
      let res = await rewrite(params1);
      this.newText = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "续写成功",
          type: "success",
        });
        // 注册自定义样式
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();
        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);
        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText,
              newText: this.newText,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 1,
            },
            Quill.sources.USER
          );
          // 将后端返回的续写文字插入到该位置后面
          // editor.insertText(position + this.selectedText.length, this.newText);
          // editor.formatText(position + this.selectedText.length, this.newText.length, 'color', 'red');
          // // 将光标移动到新插入的文本后面
          // editor.setSelection(position + this.selectedText.length + this.newText.length, 0);
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText);
          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText.length
          );
          console.log("Formatting text length:", this.newText.length);
          editor.formatText(
            editor.getLength() - this.newText.length,
            this.newText.length
          );

          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: "续写失败",
          type: "error",
        });
      }
    },
    clickTopsfw() {
      this.$router.push("/sfw");
    },

    // 2.扩写接口
    async sendToBackend2() {
      this.loading1 = true;
      this.mask = true;
      let params2 = {
        text: this.selectedText,
        flag: "2",
      };
      let res = await rewrite(params2);
      this.newText1 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "扩写成功",
          type: "success",
        });
        // this.loading = false;
        //         // this.xxdialog=true;
        //         this.xxcontent=this.newText;
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          const position = allText.indexOf(this.selectedText);

          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText1,
              newText: this.newText1,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 2,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText1);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText1.length
          );
          console.log("Formatting text length:", this.newText1.length);
          editor.formatText(
            editor.getLength() - this.newText1.length,
            this.newText1.length
          );
          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: "扩写失败",
          type: "error",
        });
      }
    },
    // 3.缩写接口
    async sendToBackend3() {
      this.loading1 = true;
      this.mask = true;
      let params3 = {
        text: this.selectedText,
        flag: "3",
      };
      let res = await rewrite(params3);
      this.newText2 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "缩写成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText2,
              newText: this.newText2,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 3,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText2);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText2.length
          );
          console.log("Formatting text length:", this.newText2.length);
          editor.formatText(
            editor.getLength() - this.newText2.length,
            this.newText2.length
          );
          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: "缩写失败",
          type: "error",
        });
      }
    },
    // 4.润色接口
    async sendToBackend4() {
      this.loading1 = true;
      this.mask = true;
      let params4 = {
        text: this.selectedText,
        flag: "4",
      };
      let res = await rewrite(params4);
      this.newText3 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "润色成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText3,
              newText: this.newText3,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 4,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText3);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText3.length
          );
          console.log("Formatting text length:", this.newText3.length);
          editor.formatText(
            editor.getLength() - this.newText3.length,
            this.newText3.length
          );

          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: "润色失败",
          type: "error",
        });
      }
    },
    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "111111111111111111111111111111111111");
      this.textarea4 = html;
      // return html;
    },
    async baocun() {
      console.log(this.$route.query.userName);
      let params = {
        wznr: this.textarea4.replace('"', '/"'),
        wzlx: "宣传材料",
        userName: this.username,
        cjrid: this.id,
      };
      let resbcwz = await bcwz(params);
      console.log(resbcwz.data.status_code);
      resbcwz.data.status_code == 200
        ? this.$message({
            message: "保存成功,请前往我的文档查看",
            type: "success",
          })
        : this.$message({
            message: "保存失败",
            type: "error",
          });
    },

    getPlainText(html) {
      const div = document.createElement("div");
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    ignore() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
    },
    highlightChange(text1, text2) {
      console.log(this.textarea4);
      let changeData = text2;
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.textarea4 = this.removetext(this.textarea4);
      this.textarea4 = this.textarea4.replace(text1, changeData);
    },
    highlightError(error, word) {
      this.textarea4 = this.removetext(this.textarea4);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.textarea4 = this.textarea4.replace(error, a);
      console.log(this.textarea4);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(this.$refs.editor.$el, "editorIframe");
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    removetextp(text) {
      let str = text;
      let reg1 = new RegExp(`<p>`, "g");
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</p>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    onCopySuccess() {
      this.$message({
        message: "内容已复制到剪贴板！",
        type: "success",
      });
    },
    onCopyError() {
      this.$message({
        message: "复制失败，请手动复制!",
        type: "warning",
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push(inputValue);
        this.textarea2 = this.dynamicTags;
      }
      // this.inputVisible = false;
      this.inputValue = "";
    },
    async jiaoyan() {
      this.textarea4 = this.removetext(this.textarea4);
      this.con_loading = true;
      // let text = this.removetextp(this.textarea4)
      testcheck({
        test_all: this.textarea4,
      }).then(async (data) => {
        this.correctionList = data.data.data;
        this.con_loading = false;
        if (this.correctionList.length == 0) {
          this.jydis = true;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          console.log(data, "619");
          this.jydis = false;
        }
      });
    },
    handleClosedrawer() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    // async jiaoyan(){
    //   this.jydis = false
    // },
    save() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.jydis = true;
    },
    fanhui() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    handleStepClick(index) {
      this.lock++;
      this.activeIndex = index; // 更新activeIndex的值
    },
    upShow() {
      this.navShow = !this.navShow;
    },
    async textareaFocus() {
      let data = await getLabel1();
      this.gzbj = data.data;
      this.dialogShow = true;
    },
    closeDialog() {
      this.dialogShow = false;
    },
    textarea2Focus() {
      // if (this.textarea == "市政府办公厅主任") {
      //   this.secRecommend = [
      //     { id: 1, title: "负责市政府办公厅日常管理" },
      //     { id: 2, title: "组织协调市政府办公厅工作" },
      //     { id: 3, title: "研究决定办公厅重大事项" },
      //     { id: 4, title: "协助市长处理办公厅相关事务" },
      //   ];
      // } else if (this.textarea == "市委宣传部部长") {
      //   this.secRecommend = [
      //     { id: 1, title: "制定宣传工作总体规划" },
      //     { id: 2, title: "指导媒体宣传工作" },
      //     { id: 3, title: "组织重大新闻发布" },
      //     { id: 4, title: "策划重大宣传活动" },
      //   ];
      // } else if (this.textarea == "市委政法委书记") {
      //   this.secRecommend = [
      //     { id: 1, title: "研究制定政法工作规划" },
      //     { id: 2, title: "领导政法工作会议" },
      //     { id: 3, title: "指导政法系统改革" },
      //     { id: 4, title: "协调处理重大政法问题" },
      //   ];
      // } else if (this.textarea == "市网信办主任") {
      //   this.secRecommend = [
      //     { id: 1, title: "落实上级部署" },
      //     { id: 2, title: "制定工作计划" },
      //     { id: 3, title: "监督网络安全" },
      //     { id: 4, title: "推动信息化建设" },
      //   ];
      // } else {
      //   this.secRecommend = [];
      // }
      this.dialog2Show = true;
    },
    closeDialog2() {
      this.dialog2Show = false;
    },
    async getText(e) {
      console.log(e);
      let params = {
        label: e,
      };
      let data = await getLabel2(params);
      console.log(data, "583");
      this.secRecommend = data.data;
      this.textarea = e;
      this.textarea2 = [];
    },
    getText2(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea2.push(e);
        this.dynamicTags.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm(e);
      } else {
        console.log(111);
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickTopInd() {
      this.$router.push("/index");
    },
    clickTopLg() {
      this.$router.push("/lgsc");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    //   心得体会
    clickTopXdth() {
      this.$router.push("/xdth");
    },
    // 工作总结
    clickTopGz() {
      this.$router.push("/gzzj");
    },
    // 领导讲话
    clickTopLd() {
      this.$router.push("/ldjh");
    },
    // 工作方案
    clickTopFa() {
      this.$router.push("/gzfa");
    },
    // 调研报告
    clickTopDy() {
      this.$router.push("/dybg");
    },
    //   领导讲话
    clickTopLd() {
      this.$router.push("/ldjh");
    },
    // 宣传材料
    clickTopXc() {
      this.$router.push("/xccl");
    },
    firstNextStep() {
      if (this.textarea111 == "") {
        this.$notify({
          title: "提示",
          message: "请填写工作类型",
          type: "warning",
        });
      } else if (this.textarea == "") {
        this.$notify({
          title: "提示",
          message: "请填写标题",
          type: "warning",
        });
      } else if (this.textarea222 == "") {
        this.$notify({
          title: "提示",
          message: "请填写宣传内容摘要",
          type: "warning",
        });
      } else {
        this.mask = true;
        // if (this.mask == true) {
        this.artShow = true;
        this.loading = true;
        this.progressPercent = 0;
        this.getSuccessInfo(this.textarea111, this.textarea, this.textarea222);
        // this.getInfo(this.textarea, this.dynamicTags);
        // }
      }
    },
    async getInfo(c1, c2, c3) {
      this.loading = true;
      let params = {
        text_1: c1,
        test_2: c2,
        test_3: c3,
      };
      let res = await getSecDtDatas(params);
      if (res.status == 200) {
        this.textarea3 = res.data;
        // 这里放置需要执行的逻辑或调用其他方法
        this.buttonShow = true;
        // this.stepActived = 2;
        this.currentStep = 2;
        this.loading = false;
        this.mask = false;
        this.curIndex = "2";
      }
    },
    repeatStep() {
      this.loading = true;
      this.mask = true;
      if (this.mask == true) {
        this.getInfo(this.textarea111, this.textarea, this.textarea222);
      }
    },
    success() {
      this.artShow = true;
      this.loading = true;
      // 这里放置需要执行的逻辑或调用其他方法
      if (this.textarea3 == "") {
        this.$notify({
          title: "提示",
          message: "大纲内容为空，无法生成",
          type: "warning",
        });
      } else {
        this.progressPercent = 0;
        this.getSuccessInfo(this.textarea111, this.textarea, this.textarea222);
      }
    },
    // 依次请求接口
    async fetchResData(index, nextIndex) {
      if (index >= this.fetchResPrams.length) {
        this.fetchData(nextIndex);
        return;
      }
      console.log(this.fetchResPrams[index], "675");
      let pams = {
        string: this.fetchResPrams[index],
        id_name: this.textarea,
      };
      const response = await getSuccessInfoItem(pams);
      this.textarea4 += response.data;
      let totalLength = 0;
      let allSubArrays = this.resDataItem.filter(
        (item) => typeof item != "string"
      );
      allSubArrays.forEach((subArray) => {
        totalLength += subArray.length;
      });
      let kuai = 100 / totalLength;
      this.progressPercent += Math.floor(kuai);
      await this.fetchResData(index + 1, nextIndex);
    },
    // 一段式生成
    async fetchData(index) {
      if (index >= this.resDataItem.length) {
        this.progressPercent = 100;
        // this.maskAll = false;
        this.maskAll = true;
        this.buttonShow = false;
        return;
      }
      if (typeof this.resDataItem[index] == "string") {
        this.textarea4 += this.resDataItem[index];
        await this.fetchData(index + 1);
      } else {
        this.fetchResPrams = this.resDataItem[index];
        this.fetchResData(0, index + 1);
      }
      // this.stepActived = 3;
      this.currentStep = 3;
      this.loading = false;
    },
    async getSuccessInfo(c1, c2, c3) {
      this.loading = true;
      this.maskAll = true;
      let params = {
        test_1: c1,
        test_2: c2,
        test_3: c3,
        username: this.username,
      };
      let res = await xccl(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: res.data.message,
          type: "success",
        });
        this.textarea4 = "";
        this.marked(res.data.data);

        this.fetchData(0);
        this.currentStep = 3;
        // this.stepActived = 3;
        this.loading = false;
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10001) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10002) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    clickTab(i) {
      if (this.currentStep != 2) {
        this.$notify({
          title: "提示",
          message: "请先输入工作背景和工作要点生成大纲",
          type: "warning",
        });
      } else {
        this.curIndex = i;
      }
    },
  },
};
</script>
<style lang="less" scoped>
// font-size: calc(100vw * 14 / 1920);
.custom-steps {
  font-size: calc(100vw * 14 / 1920);
}
.context-menu {
  margin-left: calc(-100vw * 200 / 1920);
  position: absolute;
  background: #fff;
  box-shadow: 0 calc(100vh * 2 / 1080) calc(100vh * 10 / 1080) rgba(0, 0, 0, 0.1);
  z-index: 1080;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.context-menu li {
  padding: calc(100vh * 8 / 1080) calc(100vw * 16 / 1920);
  cursor: pointer;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}

.fr {
  float: right;
}

.work-container {
  height: 100%;

  .result-container {
    padding: calc(100vh * 30 / 1080);

    .elcol-title {
      display: flex;
      overflow: hidden;
      width: 100%;
      height: calc(100vh * 48 / 1080);

      .elcol-title-text {
        padding-left: calc(100vw * 10 / 1920);
        text-align: left;
        width: 40%;
        height: 100%;
      }

      .elcol-title-text2 {
        font-size: calc(100vw * 14 / 1920);
        width: calc(100vw * 75 / 1920);
      }

      .elcol-input {
        float: left;
        width: calc(60% - calc(100vw * 75 / 1920));
        border: none !important;
      }
    }
  }
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  // background-color: #f9fafe;
  height: calc(100vh * 20 / 1080);
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

.ejdhl {
  height: 100%;
  background: url(../assets/img-left.png) no-repeat center;
  background-size: cover;
  background-position: center;
  transition: ease-out 0.4s;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  // background-color: #fff;
  background-image: linear-gradient(
    76deg,
    #07389c 0%,
    #3d86d1 0%,
    #3448b3 100%
  );

  img {
    width: calc(100vw * 120 / 1920);
    height: calc(100vh * 48 / 1080);
  }

  p {
    border: calc(100vw * 1.54 / 1920) solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: calc(100vw * 5 / 1920);
  }
}

.el-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0 calc(100vh * 2 / 1080) calc(100vh * 4 / 1080) 0
    rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  width: calc(100% - calc(100vw * 300 / 1920));

  .ai-header {
    width: 100%;

    .ai-bar {
      width: calc(100% - calc(100vw * 300 / 1920));
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vw * 40 / 1920);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: calc(100vw * 1 / 1920) solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vw * 130 / 1920);
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vw * 16 / 1920);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vw * 14 / 1920);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
                rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }
          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
              rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: calc(100vw * 93 / 1920);
        height: calc(100vh * 80 / 1080);

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 16 / 1920);
            height: calc(100vh * 16 / 1080);
          }
        }

        .btn {
          margin-left: calc(100vw * 30 / 1920);
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 28 / 1920);
            height: calc(100vh * 28 / 1080);
            float: left;
          }

          p {
            float: left;
            margin-left: calc(100vw * 22 / 1920);
            margin-top: calc(-100vh * 2 / 1080);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  // background-color: #f8f9fd;
  color: #333;
  text-align: center;
  width: calc(100% - calc(100vw * 300 / 1920));
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: calc(100vh * 20 / 1080) calc(100vw * 10 / 1920);
}
.ai-gai {
  position: fixed;
  left: 0;
  bottom: 0;
  height: 91%;
  // z-index: 999;
}

.ai-body {
  transition: ease-out 0.4s;
  width: 100%;
  height: 100%;
  float: left;
  overflow: hidden;
}
.ai-body-left {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: calc(100vw * 444 / 1920);
  height: 100%;
  background: #ffffff;
  border-radius: calc(100vh * 8 / 1080);
  overflow-x: hidden;
  overflow-y: auto;
  margin-left: calc(100vw * 10 / 1920);
  float: left;
}

.ai-body-left-top {
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  overflow-y: scroll;
}

.ai-body-left-bottom {
  width: 100%;
  height: calc(100vh * 90 / 1080);

  .ai-body-left-bottom-button {
    height: calc(100vh * 40 / 1080);
    font-size: calc(100vw * 14 / 1920);
    flex-grow: 1;
    color: #fff;
    margin: 0 calc(100vw * 25 / 1920);
    cursor: pointer;
    line-height: calc(100vh * 40 / 1080);
    margin-top: calc(100vh * 30 / 1080);
    background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
      rgba(100, 120, 212, 1);
    border-radius: calc(100vh * 20 / 1080);
    font-family: PingFangSC-Regular;
  }
}

.ai-body-left-bottom2 {
  width: 100%;
  height: calc(100vh * 80 / 1080);
  padding-top: calc(100vh * 30 / 1080);

  .repeat {
    width: calc(100vw * 188 / 1920);
    float: left;
    height: calc(100vh * 40 / 1080);
    font-size: calc(100vw * 14 / 1920);
    flex-grow: 1;
    margin-left: calc(100vw * 25 / 1920);
    cursor: pointer;
    line-height: calc(100vh * 40 / 1080);
    // background: #f4f6f8;
    border: calc(100vw * 1 / 1920) solid rgba(230, 230, 230, 1);
    border-radius: calc(100vh * 20 / 1080);
    font-family: PingFangSC-Semibold;
    color: #666666;
    text-align: center;
  }

  .ai-body-left-bottom-button {
    width: calc(100vw * 188 / 1920);
    float: left;
    height: calc(100vh * 40 / 1080);
    flex-grow: 1;
    margin-left: calc(100vw * 14 / 1920);
    cursor: pointer;
    line-height: calc(100vh * 40 / 1080);
    background-image: linear-gradient(102deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
      rgba(100, 120, 212, 1);
    border-radius: calc(100vh * 20 / 1080);
    margin-top: 0px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }

  .ai-step {
    margin-top: calc(100vh * 20 / 1080);
    width: calc(100% - calc(100vw * 40 / 1920));
    margin: 0 calc(100vw * 20 / 1920);
    margin-top: calc(100vh * 5 / 1080);
    height: calc(100vh * 40 / 1080);
  }
}
.ai-body-right {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: calc(100% - calc(100vw * 480 / 1920));
  height: 100%;
  background: #ffffff;
  border-radius: calc(100vh * 8 / 1080);
  overflow-x: hidden;
  overflow-y: auto;
  margin-left: calc(100vw * 20 / 1920);
  float: left;

  .ai-body-start {
    width: 100%;
    height: 100%;

    .pic_bkg1 {
      width: calc(100vw * 670 / 1920);
      height: calc(100vh * 590 / 1080);
      background: url(../assets/img-lct.png) no-repeat center;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: calc(100vh * 70 / 1080);
      position: relative;
    }

    .pic_bkg {
      width: calc(100vw * 528 / 1920);
      height: calc(100vh * 518 / 1080);
      background: url(../assets/img-ai.png) no-repeat center;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: calc(100vh * 118 / 1080);
      position: relative;
    }

    .pic_font {
      position: absolute;
      margin-left: auto;
      margin-right: auto;
      left: 0;
      right: 0;
      bottom: calc(100vh * 116 / 1080);
      width: calc(100vw * 255 / 1920);
      height: calc(100vh * 40 / 1080);
      border: calc(100vw * 1.54 / 1920) solid rgba(0, 0, 0, 0);
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw * 28 / 1920);
      color: #000000;
      text-align: center;
      font-weight: 600;
    }

    .title_message {
      position: absolute;
      margin-left: auto;
      margin-right: auto;
      left: 0;
      right: 0;
      bottom: calc(100vh * 82 / 1080);
      text-align: center;
      line-height: calc(100vh * 16 / 1080);
      margin-top: calc(100vh * 10 / 1080);
      height: calc(100vh * 22 / 1080);
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 14 / 1920);
      color: #999999;
      letter-spacing: 0;
      font-weight: 400;
    }

    .pic_step {
      width: calc(100vw * 551 / 1920);
      height: calc(100vh * 142 / 1080);
      background: url("../assets/pic_step.png");
      background-size: contain;
      margin: auto;
    }
  }

  .ai-body-art {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;

    .over {
      overflow: auto;
    }

    .fir-textarea {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 96.5%;
      // background: #f8f9fd;
      border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
      border-radius: calc(100vh * 4 / 1080);
      margin: calc(100vh * 20 / 1080);
      margin-bottom: 0;
      height: calc(100vh * 158 / 1080);

      ::v-deep(.el-textarea__inner) {
        font-size: calc(100vw * 14 / 1920) !important;
        // background-color: #f8f9fd !important;
        height: 100% !important;
        font-family: PingFangSC-Regular;
        padding: calc(100vh * 13 / 1080) calc(100vw * 18 / 1920)
          calc(100vh * 33 / 1080) calc(100vw * 16 / 1920);
      }
    }

    .fir-textarea-max {
      height: 95% !important;
    }

    ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
      display: none !important;
    }

    ::v-deep(.ql-blank) {
      display: none !important;
    }

    ::v-deep(.ql-container.ql-snow) {
      border: 0;
    }
  }
}

.ai-tab {
  width: calc(100vh * 230 / 1080);
  height: calc(100vh * 40 / 1080);
  margin: 0 auto;
  margin-top: calc(100vh * 30 / 1080);
  background: #f4f6f8;
  border: 1px solid #eeeff0;
  border-radius: calc(100vh * 20 / 1080);

  .tab-item {
    width: 100%;
    height: calc(100vh * 40 / 1080);
    line-height: calc(100vh * 16 / 1080);
    float: left;
    line-height: calc(100vh * 40 / 1080);
    cursor: pointer;

    font-family: PingFangSC-Semibold;
    font-size: calc(100vh * 14 / 1080);
    color: #9094a5;
    letter-spacing: 0;
    text-align: center;
  }

  .activedTab {
    border-radius: calc(100vh * 20 / 1080);

    background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
      rgba(100, 120, 212, 1);
    color: #ffffff;
  }
}

.tab-item-fir {
  width: 100%;
  height: calc(100vh * 536 / 1080);
  padding: 0 calc(100vw * 25 / 1920);
}

.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw * 14 / 1920);
  letter-spacing: 0;
  line-height: calc(100vh * 16 / 1080);
  overflow: hidden;
  margin-top: calc(100vh * 20 / 1080);
  margin-bottom: calc(100vh * 20 / 1080);
  height: calc(100vh * 20 / 1080);

  .fir-kuai {
    width: calc(100vw * 6 / 1920);
    height: calc(100vh * 16 / 1080);
    margin-right: calc(100vw * 8 / 1920);
    float: left;
    background: #4081ff;
    border-radius: calc(100vh * 1.5 / 1080);
  }

  .fir-title-p {
    line-height: calc(100vh * 16 / 1080);
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}

.fir-alert {
  margin-top: calc(100vh * 10 / 1080);
  height: calc(100vh * 35 / 1080);
}

.ai-dialog {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  row-gap: calc(100vh * 1 / 1080);
  width: 100%;
  height: calc(100vh * 290 / 1080);
  max-height: calc(100vh * 294 / 1080);
  padding: calc(100vh * 7 / 1080);
  box-shadow: 0 calc(100vh * 20 / 1080) calc(100vh * 40 / 1080)
    calc(100vh * 4 / 1080) #e4e4e524;
  margin-top: calc(100vh * 10 / 1080);
  transition: ease-out 0.4s;
  background: #ace9ff;
  border: calc(100vw * 1 / 1920) solid rgba(90, 206, 255, 1);
  border-radius: calc(100vh * 4 / 1080);

  .ai-d-title {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    height: fit-content;
    margin: 0;
    padding: calc(100vh * 1 / 1080) calc(100vw * 3 / 1920) calc(100vh * 2 / 1080)
      calc(100vw * 2 / 1920);
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #313733;
    letter-spacing: 0;
    font-weight: 400;

    .ai-d-title-p {
      flex-grow: 1;
      line-height: calc(100vh * 16 / 1080);
      text-align: left;
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 14 / 1920);
      color: #313733;
      letter-spacing: 0;
      font-weight: 400;
      margin-bottom: calc(100vh * 10 / 1080);
    }

    img {
      width: calc(100vw * 18 / 1920);
      height: calc(100vh * 18 / 1080);
      cursor: pointer;
    }
  }

  .ai-d-body {
    width: 100%;
    height: calc(100% - calc(100vh * 44 / 1080));
    overflow: hidden;
    background: #ffffff;
    border-radius: calc(100vh * 4 / 1080);

    .hints-control {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      padding: calc(100vh * 14 / 1080) calc(100vw * 14 / 1920) 0;
      height: calc(100vh * 30 / 1080);

      .hint-icon {
        flex-grow: 0;
        flex-shrink: 0;
        width: calc(100vw * 20 / 1920);
        height: calc(100vh * 20 / 1080);
        margin-right: calc(100vw * 6 / 1920);
        background-size: contain;
        background-image: url("../assets/icon_fire.png");
      }

      .hint-description {
        font-weight: 600;
        line-height: calc(100vh * 14 / 1080);
        font-family: SourceHanSansSC-Bold;
        font-size: calc(100vw * 14 / 1920);
        color: #313733;
        letter-spacing: 0;
        font-weight: 700;
      }
    }
  }
}
.ai-tj-body {
  width: 100%;
  height: calc(100vh * 200 / 1080);
  overflow-y: auto;

  .ai-tj-item {
    padding: calc(100vh * 14 / 1080) calc(100vw * 14 / 1920) 0;
    line-height: calc(100vh * 12 / 1080);
    width: 50%;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: left;
    cursor: pointer;
    height: calc(100vh * 30 / 1080);
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #313733;
    letter-spacing: 0;
    font-weight: 400;
  }
}

// ***滚动条样式
.ai-tj-body::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
}

.ai-tj-body::-webkit-scrollbar-track {
  background: #fff;
}

.ai-tj-body::-webkit-scrollbar-thumb {
  background: #488aff;
}

.fir-textarea {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: calc(100vh * 14 / 1080);
  height: calc(100vh * 158 / 1080);
  // background: #f8f9fd;
  border-radius: calc(100vh * 4 / 1080);

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
  }
}

.fir-textarea-height {
  height: calc(100vh * 460 / 1080) !important;
}

::v-deep(.el-textarea__inner) {
  padding: calc(100vh * 16 / 1080);
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: calc(100vw * 10 / 1920);
  height: calc(100vh * 10 / 1080);
}

.ai-nav {
  height: calc(100% - calc(100vh * 80 / 1080));
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  transition: ease-out 0.4s;

  .title {
    width: calc(100vw * 300 / 1920);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    height: calc(100vh * 20 / 1080);
    line-height: calc(100vh * 20 / 1080);
    padding-top: calc(100vh * 20 / 1080);
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    row-gap: calc(100vh * 12 / 1080);
    width: 100%;
    padding-top: calc(100vh * 45 / 1080);
    height: calc(100% - calc(100vh * 80 / 1080));
  }
}

.user-menu-content {
  width: calc(100vw * 246 / 1920);
  height: calc(100vh * 24 / 1080);
  padding: 0 calc(100vw * 20 / 1920);
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: calc(100vh * 17 / 1080);

    .avatar-wrapper {
      position: relative;
      width: calc(100vw * 52 / 1920);
      height: calc(100vh * 52 / 1080);
      border: calc(100vw * 1 / 1920) solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: calc(100vw * 52 / 1920);
        height: calc(100vh * 52 / 1080);
      }
    }

    .name-wrapper {
      width: calc(100vw * 300 / 1920);
      display: flex;
      flex-direction: column;
      margin-left: calc(100vw * 12 / 1920);

      .name {
        width: calc(100vw * 300 / 1920);
        color: #222;
        font-weight: 600;
        font-size: calc(100vw * 15 / 1920);
        letter-spacing: 0px;
        line-height: calc(100vh * 16 / 1080);
      }

      .id {
        margin-top: calc(100vh * 7 / 1080);
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw * 12 / 1920);
        letter-spacing: 0px;
        line-height: calc(100vh * 12 / 1080);
      }
    }
  }

  .divider {
    width: 100%;
    margin: calc(100vh * 20 / 1080) 0 calc(100vh * 18 / 1080);
    border-bottom: calc(100vw * 1 / 1920) solid #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: calc(100vh * 20 / 1080);
      cursor: pointer;

      :first-child {
        margin-top: 0px;
      }

      .icon {
        width: calc(100vw * 24 / 1920);
        height: calc(100vh * 24 / 1080);
        background-size: contain;
      }

      .text {
        margin-left: calc(100vw * 6 / 1920);
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: calc(100vh * 22 / 1080);
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

// 导航项
.nav-item {
  height: calc(100vh * 40 / 1080);
  background: transparent;
  line-height: calc(100vh * 20 / 1080);
  white-space: pre-wrap;
  background-image: linear-gradient(
    270deg,
    rgba(30, 75, 202, 0.39) 0%,
    rgba(59, 130, 234, 0.28) 100%
  );
  border: calc(100vw * 1 / 1920) solid rgba(255, 255, 255, 0.2);
  border-radius: calc(100vh * 4 / 1080);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
  cursor: pointer;
  margin: 0 auto;

  &:hover {
    background: #59bce1;
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
  }
}

.choose {
  border: calc(100vw * 1 / 1920) solid rgba(255, 255, 255, 0.2);
  border-radius: calc(100vh * 4 / 1080);
  background: #59bce1;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
}

.clbutton {
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: calc(100vh * 40 / 1080);
  left: calc(100vw * 40 / 1920);
}

.clbutton1 {
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: calc(100vh * 40 / 1080);
  left: calc(100vw * 160 / 1920);
}

.clbutton2 {
  left: calc(100vw * 180 / 1920);
}

.clbutton12 {
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: calc(100vh * 40 / 1080);
  left: calc(100vw * 280 / 1920);
}
.showUp {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 20 / 1080);
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: calc(100vh * 10 / 1080);
  left: calc(100vw * 60 / 1920);
  height: calc(100vh * 40 / 1080);
  bottom: calc(100vh * 20 / 1080);
  cursor: pointer;

  &:hover {
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.showUp1 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 20 / 1080);
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: calc(100vh * 10 / 1080);
  left: calc(100vw * 6 / 1920);
  height: calc(100vh * 40 / 1080);
  bottom: calc(100vh * 20 / 1080);
  cursor: pointer;

  &:hover {
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

::v-deep(.el-loading-spinner) {
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: calc(100vw * 150 / 1920) calc(100vh * 130 / 1080);
  height: calc(100vh * 100 / 1080);
  width: 100%;
  background-position: center;
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  margin: calc(100vh * 85 / 1080) 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(calc(-100vh * 20 / 1080))
    translateX(calc(-100vw * 16 / 1920));
  transform-origin: 0 0;
}

::v-deep(.el-drawer__header) {
  display: none;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(0);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0;
  width: 0;
  background: #c0c4cc;
}

::v-deep(.el-notification__group) {
  height: auto !important;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 1 / 1920);
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: calc(100vw * 14 / 1920) !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.progressClass {
  width: 100%;
  height: calc(100vh * 26 / 1080);
  position: fixed;
  bottom: calc(100vh * 48 / 1080);
  right: 0;
  z-index: 999;

  .progress {
    position: absolute;
    right: calc(100vw * 52 / 1920);
    transition: ease-out 0.4s;
  }
}

::v-deep(.el-progress-bar__innerText) {
  padding: calc(100vh * 7 / 1080) !important;
  color: #fff !important;
}

.menu_label {
  width: 100%;
  border-radius: calc(100vh * 4 / 1080);
  margin-top: calc(100vh * 10 / 1080);
  min-height: calc(100vh * 158 / 1080);
  height: auto;
  padding: calc(100vh * 15 / 1080) calc(100vw * 16 / 1920);
  background: #f8f9fd;
  border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
  border-radius: calc(100vh * 4 / 1080);
  padding: calc(100vh * 15 / 1080) 0;
}

.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: calc(100vw * 383 / 1920);
  word-wrap: break-word;
  text-align: left;
  margin-left: calc(100vw * 5 / 1920);
  margin-bottom: calc(100vh * 5 / 1080);
  float: left;
}

.el-main .ai-body .tab-item-fir .menu_label:active {
  background: #fff;
  border: calc(100vw * 1 / 1920) solid #4170f6;
}

.pass_input {
  width: 100%;
  height: calc(100vh * 40 / 1080);

  & /deep/ .el-input__inner {
    border: none;
    background-color: rgba(122, 151, 255, 0) !important;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #666666;
    letter-spacing: 0;
    font-weight: 400;
  }
}

::v-deep(.el-alert__title) {
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-drawer__open .el-drawer.ltr) {
  width: 100% !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0;
}

::v-deep(.el-alert) {
  padding: calc(100vh * 8 / 1080) calc(100vw * 8 / 1920);
}

.fir-textarea1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
  border-radius: calc(100vh * 4 / 1080);
  margin: calc(100vh * 20 / 1080);
  margin-bottom: 0;
  height: calc(100vh * 158 / 1080);

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: calc(100vh * 13 / 1080) calc(100vw * 18 / 1920)
      calc(100vh * 33 / 1080) calc(100vw * 16 / 1920);
  }
}

::v-deep(.el-collapse) {
  border: 0 solid #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: calc(100vw * 1 / 1920) solid #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40 / 1080);
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: calc(100vh * 4 / 1080);
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: calc(-100vw * 10 / 1920);
    top: calc(100vh * 2 / 1080);
    width: calc(100vw * 4 / 1920);
    height: calc(100vh * 15 / 1080);
    background: #5585f0;
    border-radius: calc(100vh * 2 / 1080);
  }
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
}

.elcol-title-left {
  width: calc(100vw * 8 / 1920);
  height: calc(100vh * 8 / 1080);
  border-radius: 50%;
  margin-top: calc(100vh * 20 / 1080);
  margin-left: calc(100vw * 10 / 1920);
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: calc(100vh * 48 / 1080);
}

.elcol-title-text {
  padding-left: calc(100vw * 10 / 1920);
  text-align: left;
  width: 40%;
  height: 100%;
}

.elcol-title-text2 {
  font-size: calc(100vw * 14 / 1920);
  color: #d1d7de;
  width: calc(100vw * 75 / 1920);
}

.elcol-input {
  float: left;
  width: calc(60% - calc(100vw * 75 / 1920));
  border: none !important;
}
</style>

