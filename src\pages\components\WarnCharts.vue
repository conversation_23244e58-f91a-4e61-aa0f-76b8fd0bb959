<template>
  <div class="alert-container">
  <div 
    v-for="(alert, index) in paginatedAlerts" 
    :key="currentPage * itemsPerPage + index"
    class="alert-item"
    :style="{
        background: getBkgColor(index),
      'background-image': getBgColor(index),
      color: getTextColor(index)
    }"
  >
    <div class="alert-content">
      {{ alert.result || alert }}
    </div>
  </div>
  </div>
</template>

<script>
export default {
  name: 'AlertList',
  props: {
    alerts: {
      type: Array,
      default: () => [],
      validator: function (value) {
        // 支持字符串数组或对象数组
        return value.every(item => 
          typeof item === 'string' || 
          (typeof item === 'object' && item.result)
        )
      }
    }
  },
  data() {
    return {
      currentPage: 0,
      itemsPerPage: 4,
      autoPlayInterval: null,
      autoPlayDelay: 3000,
      bgColors: [
        'linear-gradient(257deg, rgba(212,0,0,0.77) 0%, rgba(255,13,13,0.00) 95%)',
        'linear-gradient(260deg, rgba(237,0,0,0.44) 0%, rgba(255,0,0,0.00) 95%)',
        'linear-gradient(257deg, rgba(212,0,0,0.45) 0%, rgba(255,13,13,0.00) 95%)',
        'linear-gradient(257deg, rgba(106,0,212,0.77) 0%, rgba(138,13,255,0.00) 95%)',
        'linear-gradient(257deg, rgba(212,106,0,0.77) 0%, rgba(255,138,13,0.00) 95%)',
        'linear-gradient(257deg, rgba(212,0,106,0.77) 0%, rgba(255,13,138,0.00) 95%)',
        'linear-gradient(257deg, rgba(0,212,212,0.77) 0%, rgba(13,255,255,0.00) 95%)',
        'linear-gradient(257deg, rgba(212,0,212,0.77) 0%, rgba(255,13,255,0.00) 95%)',
        'linear-gradient(257deg, rgba(0,106,106,0.77) 0%, rgba(13,138,138,0.00) 95%)',
        'linear-gradient(257deg, rgba(106,0,0,0.77) 0%, rgba(138,13,13,0.00) 95%)'
      ],
      bkgColors: [
        '#0B4782',
        'rgba(19,76,128,0.29)',
        '#0B4782',
        '#0B4782',
        '#0B4782',
        '#0B4782',
        '#0B4782',
        '#0B4782',
        '#0B4782',
        '#0B4782',
      ],
      textColors: [
        '#B0D2FF',
        '#ffffff',
        '#B0D2FF',
        '#ffffff',
        '#B0D2FF',
        '#ffffff',
        '#B0D2FF',
        '#ffffff',
        '#B0D2FF',
        '#ffffff'
      ]
    }
  },
  computed: {
    hasAlerts() {
      return this.alerts && this.alerts.length > 0
    },
    totalPages() {
      return Math.ceil(this.alerts.length / this.itemsPerPage)
    },
    paginatedAlerts() {
      const start = this.currentPage * this.itemsPerPage
      const end = start + this.itemsPerPage
      return this.alerts.slice(start, end)
    },
    shouldAutoPlay() {
      return this.alerts.length > this.itemsPerPage
    }
  },
  watch: {
    alerts() {
      this.resetAutoPlay()
    }
  },
  mounted() {
    this.resetAutoPlay()
  },
  beforeDestroy() {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval)
    }
  },
  methods: {
    resetAutoPlay() {
      if (this.autoPlayInterval) {
        clearInterval(this.autoPlayInterval)
      }
      if (this.shouldAutoPlay) {
        this.autoPlayInterval = setInterval(() => {
          this.nextPage()
        }, this.autoPlayDelay)
      }
    },
    nextPage() {
      this.currentPage = (this.currentPage + 1) % this.totalPages
    },
    prevPage() {
      this.currentPage = (this.currentPage - 1 + this.totalPages) % this.totalPages
    },
    getBgColor(index) {
      return this.bgColors[index % this.bgColors.length]
    },
    getTextColor(index) {
      return this.textColors[index % this.textColors.length]
    },
    getBkgColor(index) {
      return this.bkgColors[index % this.bkgColors.length]
    },
    handleAlertClick(alert, index) {
      // 可以添加点击事件处理
      this.$emit('alert-click', { alert, index })
    }
  }
}
</script>

<style scoped>
.alert-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 16px;
  background: #1a1a2e;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.alert-container:hover .alert-item {
  animation-play-state: paused;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.alert-item {
  animation: slideIn 0.5s ease forwards;
}

.alert-item {
  margin-bottom: 12px;
  border-radius: 3px;
  /* min-height: 60px; */
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: calc(100vh * 45 / 1080);
  background: #0B4782;
}

.alert-item:last-child {
  margin-bottom: 0;
}

.alert-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.alert-content {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  width: 100%;
  padding: 16px 24px;
  letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-content {
    font-size: 16px;
    padding: 12px 16px;
  }
  
  .alert-item {
    min-height: 50px;
  }
}
</style>