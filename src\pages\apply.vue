<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle"></div>
        <div class="ai-header">
          <div class="ai-bar">
            <div class="ai-left-bar">
              <ul class="ai-left-bar-ul">
                <li @click="clickTopsfw" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />文
                </li>
                <!-- <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
                <!-- <li @click="clickTopsfw" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />收文
                </li>
                <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
                <li class="ai-left-bar-li" @click="clickToptywzsb">
                  <img src="../assets/icon15-h.png" alt="" />Ai读
                </li>
                <li class="ai-left-bar-li" @click="clickTopAi">
                  <img src="../assets/icon101.png" alt="" />AI写
                </li>
                <!-- <li @click="clickTopNav" class="ai-left-bar-li">
                  <img src="../assets/icon102-h.png" alt="" />
                  AI校对润色
                </li>
                <li @click="clickTopNav" class="ai-left-bar-li">
                  <img src="../assets/icon12-h.png" alt="" />笔墨文库
                </li> -->
                <li @click="clickTopLg" class="ai-left-bar-li">
                  <img src="../assets/icon13-h.png" alt="" />Ai想
                </li>
                <li @click="clickTopJq" class="ai-left-bar-li">
                  <img src="../assets/icon14-h.png" alt="" />Ai听
                </li>
               <!-- <li @click="clickrw" class="ai-left-bar-li">
                  <img src="../assets/check_blue.png" alt="" />核稿
                </li>  -->
                <li @click="clickTophy" class="ai-left-bar-li">
                  <img src="../assets/icon202a.png" alt="" />会议记录
                </li>
                <!-- <li @click="clickTopFz" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />辅助定密
                </li> -->
                <li @click="clickTopbmai" class="ai-left-bar-li">
                  <img src="../assets/icon204a.png" alt="" />助手
                </li>
                <!-- <li @click="clickTopsfw" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />收文
                </li>
               <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
              </ul>
            </div>
            <div class="ai-right-bar">
              <el-dropdown :hide-on-click="false" trigger="click">
                <div class="ai-avant btn">
                  <img src="../assets/icon18.png" alt="" />
                  <p>{{ username }}</p>
                </div>
                <el-dropdown-menu
                  slot="dropdown"
                  style="height: 50%; width: 20%; margin-right: -50px"
                >
                  <div class="user-menu-content">
                    <div class="user-info">
                      <div class="avatar-wrapper">
                        <img src="../assets/icon_tx.png" alt="" />
                      </div>
                      <div class="name-wrapper">
                        <div class="name">{{ username }}</div>
                        <div class="id">ID：001</div>
                      </div>
                    </div>
                    <div class="divider"></div>
                    <div class="options">
                      <div
                        rel="opener"
                        class="option"
                        href=""
                        target="_blank"
                        @click="togrzx()"
                      >
                        <div class="icon personal-center"></div>
                        <div class="text">个人中心</div>
                      </div>
                      <div
                        rel="opener"
                        class="option1"
                        href=""
                        target="_blank"
                        @click="toxg()"
                      >
                        <div class="el-icon-edit"></div>
                        <div class="text">修改密码</div>
                      </div>
                      <div
                        rel="opener"
                        class="option"
                        href=""
                        target="_blank"
                        @click="wdwd()"
                      >
                        <div class="icon my-document"></div>
                        <div class="text">我的文档</div>
                      </div>

                      <!-- <div
                        rel="opener"
                        class="option"
                        href=""
                        target="_blank"
                        @click="wdsc()"
                      >
                        <div class="icon my-document"></div>
                        <div class="text">我的收藏</div>
                      </div> -->
                      <a class="option" @click="logout()">
                        <div class="icon logout"></div>
                        <div class="text">退出登录</div>
                      </a>
                    </div>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </el-header>
      <el-main
        v-loading="loading"
        element-loading-text="请稍候..."
        element-loading-background="rgba(255, 255, 255, 0.7)"
      >
        <div style="margin-left: 1000px">
          <el-button type="text" @click="back_check()"> 【返回】 </el-button>
        </div>
        <!-- <div class="flexLd"></div> -->
        <div
          class="con flexLd"
          v-loading="con_loading"
          style="position: relative"
        >
          <div
            style="
              position: absolute;
              top: 0;
              left: 0%;
              width: 20%;
              height: 100%;
              z-index: 1;
              display: flex;
            "
            v-if="jiaoyanjc"
          >
            <div
              class=""
              style="
                width: 0%;
                height: 100%;
                margin-right: 5%;
                overflow: scroll;
              "
            >
              <div v-if="type && type == 'word'" v-html="wordHtmlContent"></div>
            </div>
          </div>
          <!-- <div class="leftTp">
            <div class="tpBG flexLd">
              <div class="dxTp flex" v-if="this.imgListRes.length > 0">
                <div class="tpbj"></div>
              </div>
              <div class="zwTp" v-if="this.imgListRes.length == 0"></div>
            </div>
          </div> -->
          <!--  -->
          <!-- <div></div> -->
          <div style="width: 360px; height: 700px; overflow-y: auto">
            <span style="color: #3a8ee6; font-weight: 600">——段落格式——</span>
            <div
              style="
                margin-bottom: 20px;
                border: 2px solid #3a8ee6;
                padding: 10px;
              "
              v-for="(item, index) in correctionList1"
              :key="item.comment_id"
            >
              <div style="display: flex; align-items: center">
                <h3
                  @click="
                    highlightError_new(
                      
                      item.previous_text,
                      item.comment_sentence,
                      item.next_text,
                    )
                  "
                >
                  第{{ index + 1 }}段
                </h3>
                <!-- 使用 Flexbox 布局 -->
                <!-- <h3 style="margin: 0">{{ item.comment_sentence }}</h3> -->
                <el-button
                  class="el-icon-check"
                  v-if="item.wrong == true"
                  type="text"
                  style="margin-left: 10px"
                ></el-button>
                <el-button
                  class="el-icon-close"
                  v-if="item.wrong == false"
                  type="text"
                  style="margin-left: 10px"
                ></el-button>
              </div>
              <el-collapse
                @change="
                  highlightError_new(
                   
                    item.previous_text,
                    item.comment_sentence,
                    item.next_text,
                  )
                "
              >
                <el-collapse-item
                  v-for="(comment, commentIndex) in item.comments"
                  :key="commentIndex"
                >
                  <template #title>
                    <div>
                      类型:{{ comment.name }}
                      <el-button
                        icon="el-icon-check"
                        v-if="comment.wrong == true"
                        type="text"
                        style="margin-left: 10px"
                      ></el-button>
                      <el-button
                        icon="el-icon-close"
                        v-else-if="comment.wrong == false"
                        type="text"
                        style="margin-left: 10px"
                      ></el-button>
                      <el-button
                        v-else
                        type="text"
                        style="margin-left: 10px"
                      ></el-button>
                      <!-- </div> -->
                    </div>
                  </template>
                  <div style="display: flex; align-items: center">
                    <p style="margin-right: 10px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">原来: {{ comment.value }}</p>

                    <p class="elcol-title-text2">建议</p>
                    <el-input
                      :title="comment.correct"
                      class="elcol-input"
                      v-model="comment.correct"
                      placeholder="请输入内容"
                      style="width: 40%"
                    ></el-input>
                  </div>
                </el-collapse-item>
                <!-- @click="get_history(item.comment_id)" -->
                <div style="height: 8px; margin-top: 6px">
                  <span
                    @click="get_his(item.comment_id), (histroyVisible = true)"
                    style="
                      float: right;
                      margin-right: 10px;
                      color: #66b1ff;
                      cursor: pointer;
                    "
                    >历史痕迹</span
                  >
                  <span
                    @click="saveChange(item.comments, item.comment_id)"
                    style="
                      float: right;
                      margin-right: 10px;
                      color: #66b1ff;
                      cursor: pointer;
                    "
                    >保存修改</span
                  >
                </div>
              </el-collapse>
            </div>
          </div>
          <!--  -->
          <div class="rightJson">
            <div
              style="
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
              "
            >
              <div class="fir-title">
                <div class="fir-kuai"></div>
                <p class="fir-title-p">原文</p>
              </div>
              <div></div>
            </div>
            <!-- <el-alert
                    class="fir-alert"
                    title="在下面可以右键选中您想修正的字词，不能超过4个字哦~"
                    type="success"
                  >
                  </el-alert> -->
            <vue-editor
              v-model="text"
              :editor-toolbar="customToolbar"
              class="fir-textarea fir-textarea-max"
              type="textarea"
              placeholder=""
              ref="editor"
              @contextmenu.native="showContextMenu"
            >
            </vue-editor>
            <div
              v-if="contextMenuVisible"
              class="context-menu"
              :style="contextMenuStyle"
            >
              <ul>
                <!-- <li @click="add">删除文字</li> -->
                <!-- <li @click="adddia = true">修改格式</li> -->
                <li @click="addDialogVisible = true">修改文字</li>
              </ul>
            </div>
            <el-button @click="save_edit()">保存修订</el-button>
            <el-button @click="back_all()">全部退回</el-button>
          </div>
          <div style="height: 700px; overflow-y: auto; width: 360px">
            <!--  -->
            <div class="r1">
              <div style="color: #3a8ee6; font-weight: 600">——文字错误——</div>
              <div
                style="height: 85%; overflow: overlay; margin-top: 20px"
                class="result-content"
              >
                <div v-for="(item, index) in correctionList3">
                  <el-collapse
                    v-model="activeNames"
                    @change="
                      highlightError(
                        item.comments[0].source,
                        item.comments[0].check,
                        item
                      )
                    "
                    style="margin-bottom: 10px"
                    accordion
                  >
                    <el-collapse-item :name="index">
                      <template slot="title">
                        <div style="width: 96%">
                          <div class="elcol-title" style="display: flex">
                            <div
                              class="elcol-title-left"
                              :style="{
                                backgroundColor: getBackgroundColor(index),
                              }"
                 
                            ></div>
                            <!-- <p class="elcol-title-text" style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                              {{ item.comments[0].value }}
                            </p> -->
                            <el-input
                              :title="item.comments[0].value"
                              class="elcol-input"
                              v-model="item.comments[0].value"
                              style="width: 40%"
                            ></el-input>
                            <p class="elcol-title-text2">建议</p>
                            <el-input
                              :title="item.comments[0].correct"
                              class="elcol-input"
                              v-model="item.comments[0].correct"
                              placeholder="请输入内容"
                              style="width: 40%"
                            ></el-input>
                          </div>
                        </div>
                      </template>
                      <div
                        style="
                          height: calc(100vh * 40/ 1080);
                          text-align: left;
                          padding-left: 20px;
                          line-height: calc(100vh * 40/ 1080);
                          border-bottom: 1px solid #e1e7f3;
                        "
                      >
                        错误类型：{{ item.comments[0].name }}
                      </div>

                      <div style="height: 8px; margin-top: 6px">
                        <span
                          @click="
                            get_his(item.comment_id), (histroyVisible = true)
                          "
                          style="
                            float: right;
                            margin-right: 10px;
                            color: #66b1ff;
                            cursor: pointer;
                          "
                          >历史痕迹</span
                        >
                        <span
                          @click="deletes(item.comment_id)"
                          style="
                            float: right;
                            margin-right: 10px;
                            color: red;
                            cursor: pointer;
                          "
                          >删除</span
                        >
                        <span
                          @click="
                            saveChange_new(
                              item.comments[0].correct,
                              item.comment_id,
                              item.comments[0].source,
                              item
                            )
                          "
                          style="
                            float: right;
                            margin-right: 10px;
                            color: #66b1ff;
                            cursor: pointer;
                          "
                          >保存修改</span
                        >
                       
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
            </div>
            <!--  -->
            <div class="r1">
              <div style="color: #3a8ee6; font-weight: 600">——新增修订——</div>
              <div
                style="height: 85%; overflow: overlay; margin-top: 20px"
                class="result-content"
              >
                <div v-for="(item, index) in correctionList3_add">
                  <el-collapse
                    v-model="activeNames1"
                    @change="
                      highlightError1(
                        item.source,
                        item.value
                      )
                    "
                    style="margin-bottom: 10px"
                    accordion
                  >
                    <el-collapse-item :name="index">
                      <template slot="title">
                        <div style="width: 96%">
                          <div class="elcol-title" style="display: flex">
                            <div
                              class="elcol-title-left"
                              :style="{
                                backgroundColor: getBackgroundColor(index),
                              }"
                            ></div>
                            <p class="elcol-title-text">
                              {{ item.value }}
                            </p>
                            <p class="elcol-title-text2">建议</p>
                            <input
                              :title="item.correct1"
                              style="height: calc(100vh * 20 / 1080);width: 40%;border: none !important; background-color: #f9fafe;margin-top: 14px;"
                              v-model="item.correct1"
                              placeholder="请输入内容"
                            >
                            </input>
                          </div>
                        </div>
                      </template>
                      <!-- <div
                        style="
                          height: calc(100vh * 40/ 1080);
                          text-align: left;
                          padding-left: 20px;
                          line-height: calc(100vh * 40/ 1080);
                          border-bottom: 1px solid #e1e7f3;
                        "
                      >
                        错误类型：{{ item.comments[0].name }}
                      </div> -->
                      <div style="height: 8px; margin-top: 6px">
                        <!-- <span
                          @click="
                            get_his(item.comment_id), (histroyVisible = true)
                          "
                          style="
                            float: right;
                            margin-right: 10px;
                            color: #66b1ff;
                            cursor: pointer;
                          "
                          >历史痕迹</span
                        > -->
                        <span
                          @click="deletes_add(item.comment_id)"
                          style="
                            float: right;
                            margin-right: 10px;
                            color: red;
                            cursor: pointer;
                          "
                          >删除</span
                        >
                        <!-- <span
                          style="
                            float: right;
                            margin-right: 10px;
                            color: #66b1ff;
                            cursor: pointer;
                          "
                          >保存修改</span
                        > -->
                        <!-- <span
                          @click="
                            highlightChange(
                              item.wordYes,
                              item.wordId,
                              item.wordNo
                            )
                          "
                          style="
                            float: right;
                            margin-right: 10px;
                            color: #66b1ff;
                            cursor: pointer;
                          "
                          >应用</span
                        > -->
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog
      title="新增文字错误"
      :visible.sync="addDialogVisible"
      width="45%"
      @close="add_word = ''"
    >
      <div
        style="height: 85%; overflow: overlay; margin-top: 20px"
        class="result-content"
      >
        <div>
          <el-collapse>
            <template>
              <div style="width: 96%">
                <div class="elcol-title" style="display: flex">
                  <!-- <p class="elcol-title-text">{{ selectedText }}</p> -->
                  <div style="width: 400px">{{ selectedText }}</div>

                  <p class="elcol-title-text2">建议修改为</p>
                  <el-input
                    :title="add_word"
                    class="elcol-input"
                    v-model="add_word"
                    placeholder="请输入内容"
                    style="width: 40%"
                  ></el-input>
                </div>
              </div>
            </template>
          </el-collapse>
          <!-- </el-collapse> -->
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addcheckword()" size="mini" type="primary"
          >确定新增</el-button
        >
        <el-button
          @click="(addDialogVisible = false), (add_word = '')"
          style="margin-right: 10px"
          size="mini"
          type="danger"
          >关闭</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="" :visible.sync="suredia" width="30%">
      <span>开始训练后，其他与模型相关的功能将暂停使用，是否开始训练？</span>
      <div slot="footer" class="dialog-footer">
        <el-button @click="starttrain()" size="mini" type="primary"
          >开始训练</el-button
        >
        <el-button
          @click="suredia = false"
          style="margin-right: 10px"
          size="mini"
          type="danger"
          >取消</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="历史痕迹" :visible.sync="histroyVisible" width="width: 45%">
      <div style="margin-left: 10px; margin-top: 10px; height: 500px">
        <el-table
          height="500"
          stripe
          border
          :default-sort="{ prop: 'date', order: 'descending' }"
          style="margin-top: 10px; overflow-y: scroll"
          :data="this.tableData1"
          :header-cell-style="{
            background: '#ebf2fb',
          }"
        >
          <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
          <el-table-column label="序号" sortable type="index" width="80">
          </el-table-column>
          <el-table-column prop="username" sortable label="操作人" width="100">
          </el-table-column>
          <el-table-column prop="czsj" sortable label="操作时间" width="200">
          </el-table-column>
          <el-table-column prop="cznr" sortable label="操作内容" width="530">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <el-dialog title="修改格式" :visible.sync="adddia" width="55%">
      <el-form ref="userForm" :model="form">
        <el-form-item>
          <div>
            <el-table :data="tableData" style="width: 100%">
              <el-table-column label="字体设置">
                <template slot-scope="scope">
                  <div style="position: relative; width: 100%">
                    <!-- 字体选择 -->
                    <el-select
                      v-model="scope.row.font"
                      placeholder="选择字体"
                      clearable
                      filterable
                      style="width: 75%; margin-bottom: 5px"
                    >
                      <el-option
                        v-for="option in options3"
                        :key="option.font_id"
                        :label="option.font_name"
                        :value="option.font_id"
                      >
                      </el-option>
                    </el-select>

                    <!-- 字号选择和输入 -->
                    <div style="position: relative">
                      <el-select
                        v-model="scope.row.fontSize"
                        placeholder="选择字号"
                        filterable
                        style="width: 75%"
                      >
                        <el-option
                          v-for="option in optionssi"
                          :key="option.font_size_name"
                          :label="option.font_size_name"
                          :value="option.font_size_name"
                        >
                        </el-option>
                      </el-select>
                      <el-input
                        v-model="scope.row.fontSize"
                        placeholder="手动输入字号"
                        style="
                          position: absolute;
                          top: 0;
                          left: 0%;
                          width: 65%;
                          z-index: 1;
                        "
                        clearable
                        @input="handleInputChange(scope.row)"
                      />
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="checkList"
                label="其他字体设置"
                width="120"
              >
                <template slot-scope="scope">
                  <!-- <el-checkbox-group v-model="scope.row.checkList"> -->
                  <el-checkbox-group
                    v-model="scope.row.checkList"
                    @change="handleCheckboxChange(scope.row)"
                  >
                    <el-checkbox
                      v-for="city in others"
                      :label="city"
                      :key="city"
                      >{{ city }}</el-checkbox
                    >
                  </el-checkbox-group>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            adddia = false;
            resetForm();
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="add_mb()">保 存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改格式错误" :visible.sync="change_adddia" width="45%">
      <el-form ref="userForm" :model="form">
        <el-form-item>
          <div>
            <el-table :data="tableDatach" style="width: 100%">
              <el-table-column label="字体设置">
                <template slot-scope="scope">
                  <div style="position: relative; width: 100%">
                    <!-- 字体选择 -->
                    <el-select
                      v-model="scope.row.font"
                      placeholder="选择字体"
                      clearable
                      filterable
                      style="width: 75%; margin-bottom: 5px"
                    >
                      <el-option
                        v-for="option in options3"
                        :key="option.font_id"
                        :label="option.font_name"
                        :value="option.font_id"
                      >
                      </el-option>
                    </el-select>

                    <!-- 字号选择和输入 -->
                    <div style="position: relative">
                      <el-select
                        v-model="scope.row.fontSize"
                        placeholder="选择字号"
                        filterable
                        style="width: 75%"
                      >
                        <el-option
                          v-for="option in optionssi"
                          :key="option.font_size_name"
                          :label="option.font_size_name"
                          :value="option.font_size_name"
                        >
                        </el-option>
                      </el-select>
                      <el-input
                        v-model="scope.row.fontSize"
                        placeholder="手动输入字号"
                        style="
                          position: absolute;
                          top: 0;
                          left: 0%;
                          width: 65%;
                          z-index: 1;
                        "
                        clearable
                        @input="handleInputChange(scope.row)"
                      />
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="checkList" label="其他字体设置">
                <template slot-scope="scope">
                  <!-- <el-checkbox-group v-model="scope.row.checkList"> -->
                  <el-checkbox-group v-model="scope.row.checkList">
                    <el-checkbox
                      v-for="city in others"
                      :label="city"
                      :key="city"
                      >{{ city }}</el-checkbox
                    >
                  </el-checkbox-group>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="change_adddia = false">取 消</el-button>
        <el-button type="primary" @click="changegs()">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import pdf from "vue-pdf";
import { VueEditor } from "vue2-editor";
import Quill from "quill";
// 添加下划线模块
// import Underline from 'quill-underline';
// Quill.register('formats/underline', Underline);
let BlockEmbed = Quill.import("blots/block/embed");
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute(
      "style",
      "padding: 20px; border: 1px solid #488aff; color:red; position: relative;"
    );
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement("button");
    insertButton.innerHTML = "应用";
    insertButton.className = "btn-insert";
    insertButton.style.position = "absolute";
    // 按钮颜色
    insertButton.style.backgroundColor = "#488aff";
    // 字体为白色
    insertButton.style.color = "white";
    // insertButton.style.top = '10px';
    insertButton.style.right = "70px";
    insertButton.addEventListener("click", () => {
      if (value.flag == 1) {
        value.editorClass.insertText(
          value.Position + value.selectedTextlength,
          value.newText
        );
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement("button");
    cancelButton1.innerHTML = "取消";
    cancelButton1.className = "btn-cancel";
    cancelButton1.style.position = "absolute";
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = "#e64721";
    // 字体为白色
    cancelButton1.style.color = "white";
    cancelButton1.style.right = "10px";
    cancelButton1.addEventListener("click", () => {
      node.remove();
    });

    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = "customBlock";
CustomBlock.tagName = "div";

Quill.register({
  "formats/customBlock": CustomBlock,
});
import {
  rewrite,
  getOcr,
  testcheck,
  getOcrtxt,
  summary_string,
  download_word,
  download_identify,
  insert_check_result,
  delete_check_result,
  update_check_result,
  start_train,
  select_comments,
  save_comments,
  select_comments_logs,
  getdocument_type,
  getfont,
  getfont_size,
  getpaper_size,
  getpreset,
  getoutline_Level,
  getqtgs,
  getqtgs2,
  select_comments_bycommentId,
} from "../api/home.js"; // 接口请求
import { mapState, mapActions } from "vuex";
import store from "../store/index";
// import mammoth from 'mammoth'
export default {
  data() {
    return {
      changeid: "",
      change_adddia: false,
      others: [],
      // tableData: [],
      tableData: [
        // 每一行的数据结构需要设置具体字段
        {
          font: "",
          fontSize: "",
          color: "#000000",
          checkList: [],
          checkList_dl: [],
          lineSpace: 12,
          spaceBefore: 0,
          spaceAfter: 0,
        }, // 一行数据
      ],

      form: {
        region: "正文",
        re1: null,
        page_size: null,
      },
      options: [], // 用于存储后端返回的数据
      options1: [],
      options2: [],
      options3: [],
      optionsle: [],
      optionssi: [],
      tableData1: [],
      add_delete: [],
      add_gs: [],
      addWord: {},
      add_xd: {},
      options: [
        {
          value: "3",
          label: "错别字",
        },
        {
          value: "4",
          label: "病句",
        },
      ],
      value: "",
      histroyVisible: false,
      add: [],
      delete: [],
      change: [],
      activeSubmenuIndex: null,
      menuItems: [
        {
          title: "删除",
          // submenu: [
          //   { title: '二级菜单1-1' },
          //   { title: '二级菜单1-2' }
          // ]
        },
        {
          title: "修改",
          submenu: [{ title: "格式错误" }, { title: "文字错误" }],
        },
      ],
      // };
      customToolbar: [["bold", "italic"]],
      radio: 0,
      suredia: false,
      begin: 0,
      beforeText: "",
      afterText: "",
      // newText: "",
      add_word: "",

      inputwordYes: "",
      addDialogVisible: false,
      adddia: false,
      show_check: false,
      loading1: false,
      selectedText: "",
      newText3: "",
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      res1: [],
      dialogVisible: false,
      paramsxz: {},
      text1: "",
      oldText: "",
      con_loading: false,
      jiaoyanjc: false,
      jydis: false,
      activeNames: [],
      activeNames1: [],
      correctionList0: [],
      correctionList1: [],
      correctionList2: [],
      correctionList3: [],
      correctionList3_add: [],
      correctionList4: [],
      correctionList5: [],
      navShow: true,
      fileList: [],
      file: "",
      filename: "",
      input: "",
      imgURl: "",
      imgURlss: "",
      imgURlcw: "",
      loading: false,
      text: "",
      imgListRes: [],
      imgpd: "",
      type: "",
      filePdfUrl: "",
      filePdfUrlcw: "",
      reverse: true,
      numPages: 0, // 初始化页数
      type: "",
      imgURlcw: "",
      filePdfUrl: "",
      numPages: 0,
      wordHtmlContent: "",
      txtContent: "",
      tableDatach: [{ font: "", fontSize: "", checkList: [] }],
      //  { font: '',  fontSize:'', color:''} // 一行数据
      activities: [
        {
          content: "活动按期开始",
          timestamp: "2018-04-15",
        },
        {
          content: "通过审核",
          timestamp: "2018-04-13",
        },
        {
          content: "通过审核",
          timestamp: "2018-04-13",
        },
        {
          content: "创建成功",
          timestamp: "2018-04-11",
        },
      ],
    };
  },
  components: {
    VueEditor,
    pdf,
  },
  watch: {
    $route(to, form) {
      // 路由变化时调用获取数据的方法
      this.get_fk();
    },
  },
  mounted() {
    this.imgListRes = JSON.parse(JSON.stringify(this.imgList));
    // 使用数组的length属性减1来获取最后一个元素的索引
    let lastIndex = this.imgListRes.length - 1;
    // 通过索引获取最后一条数据
    let lastItem = this.imgListRes[lastIndex];
    this.changeBtn(lastItem);
    // const check_id = JSON.parse(this.$route.query.check_id); // 解析查询参数
    this.get_fk();
    this.getfont();
    this.getfont_size();
    this.getqtgs2();
  },
  computed: {
    ...mapState(["id"]), // 映射 username 和 id

    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 150}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 50}px`,
      };
    },
    ...mapState(["imgList", "check_id"]),
    username() {
      return this.$store.state.username;
    },
    check_id() {
      return this.$store.state.check_id;
    },
  },
      methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
    changegs() {
      // 添加this.changeid
      const character_1 = this.tableDatach.map((row) => {
        return {
          font_id: row.font, // 判断 font_id，如果是 '仿宋_GB2312' 则赋值为 6
          font_size: row.fontSize, // 字号
          qtgs: row.checkList, // 其他设置
          id: this.changeid,
        };
      }); // let params = { //   character_1: character_1, //   // qtys: this.checkedCities, // }; // add_gs //  this.add.push(this.addWord); //  this.addWord.afterText = this.afterText; //  this.addWord.beforeText = this.beforeText;
      this.change.push(character_1);
      console.log(this.change, "修改数组");
      this.$message({
        message: "修改成功",
        type: "success",
      });
      this.change_adddia = false;
    },
    async add_mb() {
      const character_1 = this.tableData.map((row) => {
        return {
          font_id: row.font, // 判断 font_id，如果是 '仿宋_GB2312' 则赋值为 6
          font_size: row.fontSize, // 字号
          qtgs: row.checkList, // 其他设置
          word: this.selectedText,
          afterText: this.afterText,
          beforeText: this.beforeText,
        };
      });
      // let params = {
      //   character_1: character_1,
      //   // qtys: this.checkedCities,
      // };
      // add_gs
      //  this.add.push(this.addWord);
      //  this.addWord.afterText = this.afterText;
      //  this.addWord.beforeText = this.beforeText;
      this.add_gs.push(character_1);
      // console.log(this.add_gs, "新增的数组");
    },
    async getchange(c1) {
      // comment_id
      let params = {
        comment_id: c1,
      };
      let res = await select_comments_bycommentId(params);
      this.change_adddia = true;
      // console.log(res.data.data, "字体111");
      this.tableDatach[0].fontSize = res.data.data.font_size;
      this.tableDatach[0].font = res.data.data.font_name;
      if (res.data.data.qtgs[0] == "不校验") {
        this.tableDatach[0].checkList = [];
      } else {
        this.tableDatach[0].checkList = res.data.data.qtgs;
      }

      // checkList
      console.log(this.tableDatach, "字体111");
      this.changeid = c1;
    },
    handleCheckboxChange(row) {
      // 如果选中的项包含互斥选项
      if (row.checkList.includes("不校验")) {
        // 移除其他选项
        row.checkList = ["不校验"];
      } else {
        // 允许选中其他项
        // 此处你可以添加更多的逻辑，看是否需要处理其他情况
      }
    },
    async getfont() {
      let res = await getfont();
      this.options3 = res.data.data;
      console.log(this.options3[0].font_id, "字体");
    },
    async getfont_size() {
      let res = await getfont_size();
      this.optionssi = res.data.data;
    },
    async getqtgs2() {
      let res = await getqtgs2();
      this.others = res.data.data;
      // console.log(res.data.data, "qita1");
    },
    async get_his(c1) {
      let params = {
        comment_id: c1,
      };
      let res = await select_comments_logs(params);
      // tableData1
      this.tableData1 = res.data.data;
    },
    saveChange_new(c1, c2, text1, c3) {
      c3.flag = 1;
      // 构建的target
      let changeData = c3.comments[0].source.replace(
        c3.comments[0].check,
        c3.comments[0].correct
      );
      console.log(changeData, "要替换的是啥？");
      this.text = this.removetext(this.text);
      this.text = this.text.replace(text1, changeData);
      c3.comments[0].source = changeData;
      console.log(c3.comments[0].source, "source变成啥了?");
      c3.comments[0].check = c3.comments[0].correct;
      const changeObject = {
        comment_id: c2,
        correct: c1,
        flag: c3.flag,
        comments: c3,
      };
      this.change.push(changeObject);
      this.$message({
        message: "修改成功",
        type: "success",
      });
    },
    saveChange(c1, c2) {
      // this.change.push();
      const changeObject = { comment_id: c2, correct: c1 };
      // c3.check=c3.correct;
      // 将对象添加到 change 数组中
      this.change.push(changeObject);
      console.log(this.change, "更改的数组");
      this.$message({
        message: "修改成功",
        type: "success",
      });
    },
    deletes(c1) {
      this.delete.push(c1);
      console.log(this.delete, "删除");
      this.correctionList0 = this.correctionList0.filter(
        (item) => item.comment_id !== c1
      );
      this.correctionList1 = this.correctionList1.filter(
        (item) => item.comment_id !== c1
      );
      this.correctionList2 = this.correctionList2.filter(
        (item) => item.comment_id !== c1
      );
      this.correctionList3 = this.correctionList3.filter(
        (item) => item.comment_id !== c1
      );
      this.correctionList4 = this.correctionList4.filter(
        (item) => item.comment_id !== c1
      );
      this.correctionList5 = this.correctionList5.filter(
        (item) => item.comment_id !== c1
      );
    },
    deletes_add(c1) {
      this.correctionList3_add = this.correctionList3_add.filter(
        (item) => item.comment_id !== c1
      );
    },
    async save_edit() {
      let params = {
        add: this.add,
        // add_gs: this.add_gs,
        change: this.change,
        delete: this.delete,
        cjrid: this.id,
        check_id: this.check_id,
        add_xd: this.correctionList3_add,
        text: this.text,
      };
      let res = await save_comments(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "保存修订成功,请下载结果查看",
          type: "success",
        });
        // this.$router.push("/check");
        this.$router.push("/check");
        this.correctionList3_add = [];
      } else if (res.data.status_code == 9999) {
        this.$message({
          message: "暂无修订权限",
          type: "error",
        });
        this.$router.push("/check");
        this.correctionList3_add = [];
      } else {
        this.$message({
          message: "保存修订失败",
          type: "error",
        });
      }
    },
    back_all() {
      this.add = [];
      this.delete = [];
      this.change = [];
      this.get_fk();
      this.correctionList3_add = [];
    },
    setActiveSubmenu(index) {
      this.activeSubmenuIndex = index;
    },
    async starttrain() {
      let params = {
        check_id: this.check_id,
      };
      let res = await start_train(params);
      if (res.data.status_code == 200) {
        this.begin = 1;
        this.$message({
          message: "训练开始",
          type: "success",
        });
        this.suredia = false;
      } else {
        this.$message({
          message: "训练失败",
          type: "error",
        });
      }
    },
    addcheckword() {
      // 添加留痕数组
      this.addWord.afterText = this.afterText;
      this.addWord.beforeText = this.beforeText;
      this.addWord.selectedText = this.selectedText;
      this.addWord.type = this.value;
      this.addWord.newText = this.add_word;
      this.add.push(this.addWord);
      // 本页新增数组
      // this.add_xd.source = this.beforeText + this.selectedText + this.afterText;
      // this.add_xd.value = this.selectedText;
      // this.add_xd.correct1 = this.add_word;
      // this.add_xd.comment_id = this.generateRandomId(); // 使用随机数生成器
      // this.correctionList3_add.push(this.add_xd);
      // ￥￥￥
      // console.log(this.add, "新增的数组");
      // this.addDialogVisible = false;
      // if(this.add_word==""){
      //   this.$message({
      //     message: "请输入要修改的文字",
      //     type: "warning",
      //   });
      // }
      // else{
      // this.$message({
      //   message: "添加成功",
      //   type: "success",
      // });
      // this.addDialogVisible = false;
      // }
      //      const newAddXd = { // 创建一个新的对象实例
      //   source: this.beforeText + this.selectedText + this.afterText,
      //   value: this.selectedText,
      //   correct1: this.add_word,
      //   comment_id: this.generateRandomId() // 使用随机数生成器
      // };
      // this.correctionList3_add.push(newAddXd);
      if (this.add_word === "") {
        this.$message({
          message: "请输入要修改的文字",
          type: "warning",
        });
      } else {
        const newAddXd = {
          // 创建一个新的对象实例
          beforeText: this.beforeText,
          afterText: this.afterText,
          source: this.beforeText + this.selectedText + this.afterText,
          value: this.selectedText,
          correct1: this.add_word,
          comment_id: this.generateRandomId(), // 使用随机数生成器
        };

        this.correctionList3_add.push(newAddXd);
        this.$message({
          message: "添加成功",
          type: "success",
        });
        this.addDialogVisible = false;
      }
    },

    async get_fk() {
      let params = {
        check_id: this.check_id,
      };
      let res = await select_comments(params);
      // console.log(res.data.file_content, "啥");
      this.text = res.data.file_content;
      this.correctionList0 = res.data.res0;
      this.correctionList1 = res.data.res1;
      this.correctionList2 = res.data.res2;
      this.correctionList3 = res.data.res3;
      // this.correctionList4 = res.data.res4;
      this.correctionList5 = res.data.res5;
    },
    generateRandomId() {
      // 生成一个随机数，并确保它是唯一的
      return Math.random().toString(36).substr(2, 9);
    },

    back_check() {
      this.$router.push("/check");
      this.correctionList3_add = [];
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    async down() {
      let params = {
        identify_content: this.text,
      };
      let data = await download_identify(params);
      // 假设data是Blob对象
      const url = URL.createObjectURL(data); // 创建Blob URL
      // 创建一个a标签
      const a = document.createElement("a");
      a.href = url;
      a.download = "识别结果.txt"; // 设置下载文件名，这里可以根据需要修改
      // 触发点击事件
      document.body.appendChild(a); // 将a标签添加到文档中
      a.click(); // 点击a标签
      document.body.removeChild(a); // 点击后移除a标签
      URL.revokeObjectURL(url); // 释放创建的URL
    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener("click", this.hideContextMenu);
    },
    // showContextMenu(event) {
    //   event.preventDefault();
    //   this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
    //   this.selectedText = window.getSelection().toString();
    //   if (this.selectedText) {
    //     this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    //     this.contextMenuVisible = true;
    //     document.addEventListener("click", this.hideContextMenu);
    //   }
    // },
    //  showContextMenu(event) {
    //   event.preventDefault();

    //   // 获取选中的文本
    //   const selectedText = window.getSelection().toString();
    //   this.selectedText = selectedText;

    //   if (selectedText) {
    //     // 获取选中文本的范围
    //     const range = window.getSelection().getRangeAt(0);

    //     // 获取起始和结束节点
    //     const startNode = range.startContainer;
    //     const endNode = range.endContainer;

    //     // 获取选中文本的父元素
    //     const parentNode = startNode.parentNode;

    //     // 获取父元素的文本内容
    //     const fullText = parentNode.innerText;

    //     // 获取选中文本前后的文本
    //     const startIndex = range.startOffset; // 选中区域的开始位置
    //     const endIndex = range.endOffset; // 选中区域的结束位置

    //     // 获取前10个字符
    //     const beforeText = fullText.substring(Math.max(0, startIndex - 10), startIndex);
    //     // 获取后10个字符
    //     const afterText = fullText.substring(endIndex, Math.min(fullText.length, endIndex + 10));

    //     // 如果需要，可以将提取的文本存储到实例中
    //     this.beforeText = beforeText;
    //     this.afterText = afterText;

    //     // 计算并设置上下文菜单的位置，如之前的实现
    //     this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    //     this.contextMenuVisible = true;
    //     document.addEventListener("click", this.hideContextMenu);
    //   }
    // },
    showContextMenu(event) {
      event.preventDefault();

      // 获取选中的文本
      const selectedText = window.getSelection().toString();
      this.selectedText = selectedText;

      // 判断选中的文本长度是否超过 4
      if (selectedText && selectedText.length >= 0) {
        // 获取选中文本的范围
        const range = window.getSelection().getRangeAt(0);

        // 获取起始和结束节点
        const startNode = range.startContainer;
        const endNode = range.endContainer;

        // 获取选中文本的父元素
        const parentNode = startNode.parentNode;

        // 获取父元素的文本内容
        const fullText = parentNode.innerText;

        // 获取选中文本前后的文本
        const startIndex = range.startOffset; // 选中区域的开始位置
        const endIndex = range.endOffset; // 选中区域的结束位置

        // 获取前10个字符
        const beforeText = fullText.substring(
          Math.max(0, startIndex - 10),
          startIndex
        );
        // 获取后10个字符
        const afterText = fullText.substring(
          endIndex,
          Math.min(fullText.length, endIndex + 10)
        );

        // 如果需要，可以将提取的文本存储到实例中
        this.beforeText = beforeText;
        this.afterText = afterText;
        // 计算并设置上下文菜单的位置，如之前的实现
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener("click", this.hideContextMenu);
      } else {
        // 如果选中的文本长度超过 4，可以给出提示或执行其他操作
        this.$message({
          message: "请选择修改的文本",
          type: "warning",
        });
      }
    },
    // },

    toxg() {
      // alert(1)
      this.$router.push("/grzx4");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    wdsc() {
      // alert(1)
      this.$router.push("/wdsc");
    },
    togrzx() {
      // alert(1)
      this.$router.push("/grzx1");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    async getSummary() {
      this.con_loading = true;
      let params = {
        text: this.text,
      };
      let data = await summary_string(params);
      console.log(data.data.status_code, "w1shi1sbxc");
      this.text1 = data.data.data;
      let that = this;
      // 调用 Vuex action 来插入数据到指定索引
      // this.$store.dispatch('addItemAction', data.data, index);
      if (data.data.status_code == 200) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "success",
        });
      } else if (data.data.status_code == 500) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "error",
        });
      } else if (data.data.status_code == 10001) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "error",
        });
      } else if (data.data.status_code == 10002) {
        this.con_loading = false;
        this.$message({
          message: data.data.message,
          type: "error",
        });
      }
    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    highlightError(error, word, c3) {
      if (c3.flag == 1) {
        this.text = this.removetext(this.text);
        //
        let a = c3.comments[0].source.replace(
          c3.comments[0].correct,
          `<span class="highlight" style="background-color:yellow!important;">${c3.comments[0].correct}</span>`
        );
        this.text = this.text.replace(c3.comments[0].source, a);
        console.log(this.text);
        this.$nextTick(() => {
          // 确保 DOM 更新完成后，滚动到高亮文字
          this.scrollToHighlight();
        });
      } else {
        this.text = this.removetext(this.text);
        let a = error.replace(
          word,
          `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
        );
        this.text = this.text.replace(error, a);
        console.log(this.text);
        this.$nextTick(() => {
          // 确保 DOM 更新完成后，滚动到高亮文字
          this.scrollToHighlight();
        });
      }
    },
    highlightError1(error, word) {
      this.text = this.removetext(this.text);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.text = this.text.replace(error, a);
      console.log(this.text);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    highlightError_new(error1, error2, word) {
      // 将三个参数拼接在一起
      let error = error1 + error2 + word;
      console.log(error, "看看结果");
      this.text = this.removetext(this.text);
      let a = error.replace(
        error,
        `<span class="highlight" style="background-color:yellow!important;">${error}</span>`
      );
      this.text = this.text.replace(error, a);
      console.log(this.text);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    // 高亮方法
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(
        this.$refs.editor.$el.querySelector("div.ql-editor"),
        "editorIframe"
      );
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    async ignore(c1) {
      let params = {
        wordId: c1,
      };
      let res = await delete_check_result(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: "删除成功",
          type: "success",
        });

        this.get_fk();
      } else {
        this.$message({
          message: "删除失败",
          type: "error",
        });
      }
    },
    highlightChange(c1) {
      //   let params = {
      //     wordYes: c1,
      //   };
      //   // 如果c1和c3的长度相同
      //   if (c1.length === c3.length) {
      //     let res = await update_check_result(params);
      //     if (res.data.status_code == 200) {
      //       this.$message({
      //         message: "修改成功",
      //         type: "success",
      //       });
      //       this.get_fk();
      //     } else {
      //       this.$message({
      //         message: "修改失败",
      //         type: "error",
      //       });
      //     }
      //   } else {
      //     this.$message({
      //       message: "修改失败，请确保字数相同",
      //       type: "error",
      //     });
      //     this.get_fk();
      //   }
      // },
      // save() {
      //   // this.text = this.removetext(this.text)
      //   // this.activeNames = []
      //   this.jiaoyanjc = false;
      console.log(c1, "c1");
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },

    shibie() {
      if (this.text == "") {
        this.text = this.oldText;

        this.$message({
          message: "识别成功",
          type: "success",
        });
      } else {
        this.$message({
          message: "识别已完成，请勿重复识别",
          type: "warning",
        });
      }
    },
    zhaiyao() {
      this.getSummary();
    },
    async jiaoyan() {
      this.con_loading = true;
      this.text = this.removetext(this.text);
      testcheck({
        test_all: this.text,
      }).then(async (data) => {
        this.correctionList = data.data.data;
        if (this.correctionList.length == 0) {
          this.jiaoyanjc = false;
          this.loading = false;
          this.con_loading = false;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          if (this.type == "img") {
            let fd = {
              imgs_name: "1.png",
              base_string: this.imgURlss,
            };
            getOcrtxt(fd).then(async (data) => {
              console.log(data, "362");
              this.imgURlcw = data.data;
              this.con_loading = false;
            });
          } else {
            this.con_loading = false;
          }
          console.log(data, "619");
          this.jiaoyanjc = true;
        }
      });
    },
    ...mapActions(["addItemAction"]),
    ...mapActions(["addSczyAction"]),
    changeBtn(val) {
      this.text = "";
      this.text1 = "";

      // console.log(val.imgs_base);
      if (val) {
        this.imgpd = "data:image/jpeg;base64," + val.re_imgs_base;
        // this.imgURl = val.imgs_base;
        this.imgURl = val.re_imgs_base;
        this.imgURlss = val.base_string;
        // this.text1 = '习近平在中央政治局第十一次集体学习中强调，发展新质生产力是推动高质量发展的关键。总结新时代高质量发展的成就，并分析当前的突出矛盾和问题，提出改进措施以推动新的突破。高质量发展已经成为全党共识，取得了显著成效，但仍存在诸多制约因素，如关键技术受制于人、城乡和收入分配差距较大等。习近平指出，必须全面贯彻新发展理念，推进现代化经济体系建设和科技自立，深化改革开放，确保高质量发展基础。习近平提出的新质生产力，强调创新为主导，摆脱传统增长模式，具有高科技、高效能、高质量特征。要通过科技创新、产业创新、发展方式创新、体制机制创新和人才机制创新，推动新质生产力的发展。具体措施包括加强原创性科技创新，促进科技成果转化为现实生产力，推动绿色低碳转型，深化改革形成新型生产关系，以及完善人才培养和激励机制。';
        if (val.type == "pdf" || val.type == "ofd" || val.type == "txt") {
          this.filePdfUrl = "data:application/pdf;base64," + val.re_imgs_base;
          const loadingTask = pdf.createLoadingTask(this.filePdfUrl);
          loadingTask.promise
            .then((pdf) => {
              this.numPages = pdf.numPages;
            })
            .catch((err) => {
              console.error("Error while loading the PDF", err);
            });
          this.$nextTick(() => {
            this.$refs.pdfContainer.scrollTop = 0;
          });
        }
        this.type = val.type;
        this.oldText = val.txt;
        // this.text = val.txt;
        // this.getSummary();
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickTopAi() {
      this.$router.push("/index");
    },
    clickToptywzsb() {
      this.$router.push("/tywzsb");
    },
    clickTopLg() {
      this.$router.push("/lgsc");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    clickTopsfw() {
      this.$router.push("/sfw");
    },
    // beforeAvatarUpload(file) {
    //   const isJPG = file.type === "image/jpeg";
    //   const isPNG = file.type === "image/png";
    //   const isPDF = file.type === "application/pdf";
    //   const isDOCX =
    //     file.type ===
    //     "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    //   const isDOC = file.type === "application/msword";

    //   if (!isJPG && !isPNG && !isPDF && !isDOCX && !isDOC) {
    //     this.$message.error("上传文件只能是 JPG/PNG/PDF/DOCX/DOC 格式!");
    //   }
    //   return isJPG || isPNG || isPDF || isDOCX || isDOC;
    // },

    uploadFile(item) {
      let file;
      this.filename = item.file.name;
      this.blobToBase64(item.file, (dataurl) => {
        file = dataurl.split(",")[1];
        this.file = JSON.parse(JSON.stringify(file));
        // if (
        //   item.file.type ===
        //     "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
        //   item.file.type === "application/msword"
        // ) {
        // this.uploadMp1(this.file);
        // } else {
        this.uploadMp(this.file);
        //   }
      });
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    async uploadMp(val) {
      console.log(val);
      console.log(this.filename, "文件名");
      let that = this;
      this.loading = true;
      let fd = new FormData();
      fd.append("imgs_name", this.filename);
      fd.append("base_string", val);
      let resData = await getOcr(fd);
      this.$refs.upload.clearFiles();
      if (resData.data.type == "1") {
        // console.log(resData.data.data, "resData.data.dataresData.data.data");
        that.addItemAction(resData.data.data);
        this.imgListRes = JSON.parse(JSON.stringify(this.imgList));
        // 使用数组的length属性减1来获取最后一个元素的索引
        let lastIndex = this.imgListRes.length - 1;
        // 通过索引获取最后一条数据
        let lastItem = this.imgListRes[lastIndex];
        this.changeBtn(lastItem);

        // that.$store.commit("addItem", resData.data.data);
        this.$message({
          showClose: true,
          message: resData.data.message,
          type: "success",
        });
        this.loading = false;
        // this.imgURl = resData.data.data.re_imgs_base;
        // this.text = resData.data.data.txt;
        // this.text1 = '习近平在中央政治局第十一次集体学习中强调，发展新质生产力是推动高质量发展的关键。总结新时代高质量发展的成就，并分析当前的突出矛盾和问题，提出改进措施以推动新的突破。高质量发展已经成为全党共识，取得了显著成效，但仍存在诸多制约因素，如关键技术受制于人、城乡和收入分配差距较大等。习近平指出，必须全面贯彻新发展理念，推进现代化经济体系建设和科技自立，深化改革开放，确保高质量发展基础。习近平提出的新质生产力，强调创新为主导，摆脱传统增长模式，具有高科技、高效能、高质量特征。要通过科技创新、产业创新、发展方式创新、体制机制创新和人才机制创新，推动新质生产力的发展。具体措施包括加强原创性科技创新，促进科技成果转化为现实生产力，推动绿色低碳转型，深化改革形成新型生产关系，以及完善人才培养和激励机制。';
        this.jydis = true;
        // let zpxx = this.zpzm(resData.data.imgs_base);
        // this.imgURl = zpxx;
      } else if (resData.data.status_code == 400) {
        this.$message({
          showClose: true,
          message: resData.data.message,
          type: "error",
        });
      } else if (resData.data.status_code == 500) {
        // this.loading = false;

        this.$message({
          showClose: true,

          message: resData.data.message,
          type: "error",
        });
      }
      if (resData.data.type == "2") {
        if (resData.data.status_code == 200) {
          this.$message({
            showClose: true,
            message: resData.data.message,
            type: "success",
          });
          this.res1 = resData;
          this.loading = false;
          this.dialogVisible = true;
        } else if (resData.data.status_code == 9999) {
          this.loading = false;
          this.$message({
            showClose: true,
            message: resData.data.message,
            type: "error",
          });
          this.loading = false;
          this.correctionList3_add = [];
        } else if (resData.data.status_code == 500) {
          // this.loading = false;
          this.$message({
            showClose: true,
            message: resData.data.message,
            type: "error",
          });
          this.loading = false;
        }
      }
    },
    download_word() {
      this.paramsxz = {
        // resData.data.
        base_string1: this.res1.data.data.re_imgs_base.replace('"', ""),
        imgs_name1: this.res1.data.data.name.replace('"', ""),
        // base_string:resData.data.data.re_imgs_base,
      };
      download_word(this.paramsxz).then((blob) => {
        // 创建一个 URL 对象
        const url = window.URL.createObjectURL(blob);
        // 创建一个 <a> 元素
        const a = document.createElement("a");
        // 设置下载属性
        a.href = url;
        a.download = this.paramsxz.imgs_name1 || "download.docx"; // 设置默认文件名
        // 将 <a> 元素添加到文档中
        document.body.appendChild(a);
        // 触发点击事件
        a.click();
        // 移除 <a> 元素
        document.body.removeChild(a);
        // 释放 URL 对象
        window.URL.revokeObjectURL(url);
        this.dialogVisible = false;
      });
    },

    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    zpzm(zp) {
      const iamgeBase64 = "data:image/jpeg;base64," + zp;
      let zpxx;
      if (typeof iamgeBase64 === "string") {
        // 复制某条消息
        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64)) {
          // debugger;
          // let that = this;

          function previwImg(item) {
            zpxx = item;
          }
          previwImg(iamgeBase64);
        }
      }
      return zpxx;
    },
  },
};
</script>
<style lang="less" scoped>
.context-menu {
  margin-left: calc(-100vw * 350 / 1920);
  position: absolute;
  background: #fff;
  box-shadow: 0 calc(100vh * 2 / 1080) calc(100vh * 10 / 1080) rgba(0, 0, 0, 0.1);
  z-index: 1080;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.context-menu li {
  padding: calc(100vh * 8 / 1080) calc(100vw * 16 / 1920);
  cursor: pointer;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}
.upload-demo {
  // width: 100%;
  // height: 100%;
  font-size: calc(100vw * 16 / 1920);
}
::v-deep .custom-dialog .el-dialog__body {
  padding-top: calc(100vh * 120 / 1080); /* 调整这个值以达到你想要的移动距离 */
}

::v-deep .custom-dialog .el-dialog {
  height: calc(100vh * 400 / 1080);
  background: url("../assets/tc.png") no-repeat center !important;
  background-size: cover !important; /* 确保背景图片覆盖整个对话框 */
}

::v-deep .custom-dialog .dialog-footer {
  padding-top: calc(100vh * 330 / 1080); /* 调整这个值以达到你想要的移动距离 */
}

.el-button span {
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 16 / 1920);
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  width: calc(100vw * 300 / 1920);
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: linear-gradient(76deg, #07389c 0%, #3d86d1 0%, #3448b3 100%);

  img {
    width: calc(100vw * 120 / 1920);
    height: calc(100vh * 48 / 1080);
    position: absolute;
  }

  p {
    border: calc(100vw * 1.54 / 1920) solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: calc(100vw * 5 / 1920);
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px calc(100vh * 2 / 1080) calc(100vh * 4 / 1080) 0px
    rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vw * 40 / 1920);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: calc(100vw * 1 / 1920) solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vw * 130 / 1920);
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vw * 16 / 1920);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vw * 14 / 1920);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0px
                rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0px
              rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: calc(100vw * 93 / 1920);
        height: calc(100vh * 80 / 1080);

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 16 / 1920);
            height: calc(100vh * 16 / 1080);
          }
        }

        .btn {
          margin-left: calc(100vw * 30 / 1920);
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 28 / 1920);
            height: calc(100vh * 28 / 1080);
            float: left;
          }

          p {
            float: left;
            margin-left: calc(100vw * 22 / 1920);
            margin-top: calc(100vh * -2 / 1080);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  // background-color: #f8f9fd;
  // background-size: 100% 100%;
  // color: #333;
  // text-align: center;
  // width: 100%;
  // height: calc(100% - calc(100vh * 80 / 1080));
  // position: absolute;
  // right: 0;
  // top: calc(100vh * 80 /1080);
  // padding: calc(100vh * 20 / 1080);
  background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 /1080);
  padding: 20px 10px;
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw * 14 / 1920);
  letter-spacing: 0;
  line-height: calc(100vh * 16 / 1080);
  overflow: hidden;
  height: calc(100vh * 20 / 1080);

  .fir-kuai {
    width: calc(100vw * 6 / 1920);
    height: calc(100vh * 16 / 1080);
    margin-right: calc(100vw * 8 / 1920);
    float: left;
    background: #4081ff;
    border-radius: calc(100vw * 1.5 / 1920);
  }

  .fir-title-p {
    color: #222;
    font-weight: 500;
    font-size: calc(100vw * 14 / 1920);
    letter-spacing: 0;
    line-height: calc(100vh * 16 / 1080);
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}

.xxjs {
  width: calc(100vw * 902 / 1920);
  height: calc(100vh * 40 / 1080);
  word-wrap: break-word;
  text-align: left;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #999999;
  letter-spacing: 0;
  margin-top: calc(100vh * 12 / 1080);
}

.flexLd {
  display: flex;
  justify-content: space-between;
  height: auto;
}

.btnLeft {
  width: calc(100vw * 332 / 1920);
  height: auto;

  .el-button {
    width: calc(100vw * 104 / 1920);
    height: calc(100vh * 30 / 1080);
    line-height: calc(100vh * 15 / 1080);
    padding: calc(100vh * 5 / 1080);
    font-size: calc(100vw * 14 / 1920);
    margin-left: 0;
    margin-right: calc(100vw * 9 / 1920);
    margin-top: calc(100vh * 10 / 1080);
    background: #f5f8fc;
    border: calc(100vw * 1 / 1920) solid rgba(31, 82, 176, 1);
    border-radius: calc(100vw * 4 / 1920);
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw * 14 / 1920);
    color: #1f52b0;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;

    span {
      font-family: SourceHanSansSC-Medium;
      font-size: calc(100vw * 16 / 1920);
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }
  }
}

::v-deep(.el-button) {
  span {
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw * 16 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
}

.con {
  width: 100%;
  height: auto;
  // margin-top: calc(100vh * 20 / 1080);
  // background: #ffffff;
  padding: calc(100vh * 20 / 1080);
  .leftTp {
    width: calc(100vw * 100 / 1920);
    height: auto;

    .tpBG {
      width: 100%;
      height: calc(100vh * 555 / 1080);
      margin-bottom: calc(100vh * 20 / 1080);

      .zxTp {
        width: calc(100vw * 162 / 1920);
        height: 100%;
        overflow-y: scroll;

        .xTpk {
          width: 100%;
          height: calc(100vh * 136 / 1080);
          background: #dcdfe5;
          margin-bottom: calc(100vh * 8 / 1080);

          &:hover {
            border: calc(100vw * 1.35 / 1920) solid rgba(26, 102, 255, 1);
          }

          .tpbj {
            width: 100%;
            height: calc(100vh * 110 / 1080);
            // background-color: #ffffff;

            img {
              width: calc(100vw * 140 / 1920);
              height: calc(100vh * 110 / 1080);
              object-fit: contain;
            }
          }

          &:active {
            border: calc(100vw * 1.35 / 1920) solid rgba(26, 102, 255, 1);
            width: 100%;
            height: calc(100vh * 136 / 1080);
            background: #dcdfe5;
            margin-bottom: calc(100vh * 8 / 1080);

            .tpbj {
              width: calc(100vw * 140 / 1920);
              height: calc(100vh * 110 / 1080);
              // background-color: #ffffff;

              img {
                width: 140px;
                height: 110px;
              }
            }
          }
        }

        .activeImg {
          border: 1.35px solid rgba(26, 102, 255, 1);
          width: 100%;
          height: 136px;
          background: #dcdfe5;
          margin-bottom: 8px;

          .tpbj {
            width: 140px;
            height: 110px;
            // background-color: #ffffff;

            img {
              width: 140px;
              height: 110px;
            }
          }
        }
      }

      .dxTp {
        width: 848px;
        height: 100%;
        // background: #dcdfe5;

        .tpbj {
          width: 100%;
          height: 100%;
          // background-color: #ffffff;

          img {
            // width: 350px;
            height: 450px;
            object-fit: contain;
          }
        }
      }

      .zwTp {
        background: url(../img/bga.png) no-repeat center;
        width: 88%;
        height: 100%;
        background-size: 50%;
      }
    }

    .tpUpload {
      width: 70%;
      height: 82px;
      padding-left: 18px;
      align-items: center;

      .upBtn {
        // width: 100%;
        height: calc(100vh * 40/ 1080);
        font-size: calc(100vh * 40 / 1920);

        // margin-left: 120px;
        ::v-deep(.el-button) {
          background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
          // box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
          border-radius: 4px;
          width: calc(100vw * 160 / 1920);
          height: calc(100vh * 40/ 1080);
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw *  16 / 1920);
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }

        .sizeColor {
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
          margin: 10px;
        }

        ::v-deep(.el-input__inner) {
          background: #ffffff;
          border: 1px solid rgba(186, 203, 228, 1);
          border-radius: 4px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
        }
      }

      .upBtn1 {
        // width: 100%;
        height: calc(100vh * 40/ 1080);
        margin-left: 60px;

        ::v-deep(.el-button) {
          background: #1a66ff;
          border: 1px solid rgba(31, 82, 176, 1);
          border-radius: 4px;
          width: calc(100vw * 160 / 1920);
          height: calc(100vh * 40/ 1080);
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw *  16 / 1920);
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }

        .sizeColor {
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
          margin: 10px;
        }

        ::v-deep(.el-input__inner) {
          background: #ffffff;
          border: 1px solid rgba(186, 203, 228, 1);
          border-radius: 4px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
        }
      }

      .zcSize {
        font-family: PingFangSC-Semibold;
        font-size: calc(100vw * 14 / 1920);
        color: #999999;
        letter-spacing: 0;
        height: auto;
        text-align: left;
        margin-left: calc(100vw * 10 / 1920);
        width: calc(100vw * 600 / 1920);
      }

      .zcSize1 {
        font-family: PingFangSC-Semibold;
        font-size: calc(100vw *  14 / 1920);
        color: #999999;
        letter-spacing: 0;
        // font-weight: 600;
        height: auto;
        text-align: left;
        margin-left: 10px;
        width: 550px;
        vertical-align: middle;
      }
    }
  }

  .rightJson {
    // margin-left: 130px;
    width: 1060px;
    height: 700px;
    background: #ecf5ff;
    padding: 20px;
    position: relative;
    // margin-right: 400px;
    .fir-textarea {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #f8f9fd;
      border: 1px solid rgba(229, 232, 245, 1);
      border-radius: 4px;
      margin-top: 20px;
      margin-bottom: 0;
      height: 158px;

      ::v-deep(.el-textarea__inner) {
        font-size: 14px !important;
        background-color: #f8f9fd !important;
        height: 100% !important;
        font-family: PingFangSC-Regular;
        padding: 13px 18px 33px 16px;
      }
    }

    .fir-textarea-max {
      height: 85% !important;
    }

    .jsTitle {
      font-family: PingFangSC-Regular;
      font-size: calc(100vw *  16 / 1920);
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
      height: auto;
      text-align: left;
      margin-bottom: 5px;
    }

    .jsbor {
      border: 1px solid rgba(150, 171, 214, 1);
      height: auto;
      margin-bottom: 14px;
    }

    .jsConNr {
      width: 100%;
      height: calc(100% - 84px);
      font-family: PingFangSC-Regular;
      font-size: calc(100vw *  16 / 1920);
      color: #333333;
      letter-spacing: 0;
      line-height: 35px;
      font-weight: 400;
      overflow-y: scroll;
      text-align: left;
    }
  }
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw *  12 / 1920);
  right: 10px;
  height: 10px;
}

::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
  display: none !important;
}

::v-deep(.ql-blank) {
  display: none !important;
}

::v-deep(.ql-container.ql-snow) {
  border: 0;
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;
    // font-size: calc(100vw *  15 / 1920);

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.clbutton1 {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}
.clbutton1_grey {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  // background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  // box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}
.clbutton3 {
  font-size: calc(100vw * 16 / 1920);

  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
    rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  float: right;
  margin: calc(100vh * 10 / 1080) calc(100vw * 10 / 1920) 0 0;
}

.clbutton2 {
  left: 39%;
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: calc(100vh * 48 / 1080);

  .elcol-title-text {
    padding-left: calc(100vw * 10 / 1920);
    text-align: left;
    width: 40%;
    height: 100%;
  }

  .elcol-title-text2 {
    font-size: calc(100vw * 14 / 1920);
    color: #303133;
    width: calc(100vw * 75 / 1920);
    // overflow: hidden;
  }

  .elcol-input {
    float: left;
    width: calc(60% - calc(100vw * 75 / 1920));
    border: none !important;
  }
}

.fir-timeline {
  width: 100%;
  height: calc(100vh * 158 / 1080);
  margin-top: calc(100vh * 20 / 1080);
  margin-bottom: 0;
  overflow-y: scroll;
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: calc(100vh * 20 / 1080);
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

::v-deep(.el-textarea__inner) {
  padding: calc(100vh * 16 / 1080);
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: calc(100vh * 20 / 1080);
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

::v-deep(.el-collapse) {
  border: 0 solid #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: calc(100vw * 1 / 1920) solid #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40 / 1080);
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: calc(100vh * 4 / 1080);
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: calc(-100vw * 10 / 1920);
    top: calc(100vh * 2 / 1080);
    width: calc(100vw * 4 / 1920);
    height: calc(100vh * 15 / 1080);
    background: #5585f0;
    border-radius: calc(100vh * 2 / 1080);
  }
}

.elcol-title-left {
  width: calc(100vw * 8 / 1920);
  height: calc(100vh * 8 / 1080);
  border-radius: 50%;
  margin-top: calc(100vh * 20 / 1080);
  margin-left: calc(100vw * 10 / 1920);
}

.highlight {
  background-color: yellow !important;
  cursor: pointer;
}

::v-deep .custom-dialog .el-dialog__body {
  padding-top: calc(100vh * 120 / 1080);
}

::v-deep .custom-dialog .el-dialog {
  height: calc(100vh * 400 / 1080);
  background: url("../assets/tc.png") no-repeat center !important;
  background-size: cover !important;
}

::v-deep .custom-dialog .dialog-footer {
  position: absolute;
  bottom: calc(100vh * 20 / 1080);
  left: 50%;
  transform: translateX(-50%);
  padding-top: 0;
  width: 100%;
  display: flex;
  justify-content: center;
}

.el-button span {
  font-family: SourceHanSansSC-Medium;
}
ul {
  list-style-type: none;
}

ul li {
  cursor: pointer;
  position: relative;
}

ul li:hover > ul {
  display: block;
}

ul li > ul {
  display: none;
  position: absolute;
  left: 100%;
  top: 0;
}
// /* 滚动条上的滚动滑块 */
// ::-webkit-scrollbar-thumb {
//   background-color: rgb(128, 128, 128);
//   border-radius: 32px;
// }

// /* 滚动条轨道 */
// ::-webkit-scrollbar-track {
//   background-color: #ffffff;
//   border-radius: 32px;
// }
// /* 整个滚动条 */
// ::-webkit-scrollbar {
//   /* 对应纵向滚动条的宽度 */
//   width: 10px;
//   /* 对应横向滚动条的宽度 */
//   height: 10px;
// }
</style>
