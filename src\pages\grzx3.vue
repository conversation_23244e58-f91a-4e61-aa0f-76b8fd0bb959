<!-- 本页为管理员的账户管理-->
<template>
  <div class="work-container" v-loading="con_loading">
    <el-container>
      <el-header>
        <div class="ejdhlTitle"></div>
        <div class="ai-header">
          <div class="ai-bar">
            <div class="ai-left-bar">
              <ul class="ai-left-bar-ul">
                <li class="ai-left-bar-li" @click="sfw111()">
                  <img src="../assets/icon-jj.png" alt="" />文
                </li>
                <!-- <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
                <!-- <li @click="clickTopTy" class="ai-left-bar-li">
                  <img src="../assets/icon15-h.png" alt="" />Ai读
                </li>
                <li class="ai-left-bar-li" @click="clickTopAi">
                  <img src="../assets/icon101.png" alt="" />AI写
                </li>
                <li @click="clickTopLg" class="ai-left-bar-li">
                  <img src="../assets/icon13-h.png" alt="" />Ai想
                </li>
                <li @click="clickTopJq" class="ai-left-bar-li">
                  <img src="../assets/icon14-h.png" alt="" />Ai听
                </li> -->
                <!-- <li @click="clickTopbmai" class="ai-left-bar-li">
                  <img src="../assets/icon204a.png" alt="" />助手
                </li> -->
                <!-- <li class="ai-left-bar-li actived" @click="sfw111">
                  <img src="../assets/icon-jj-hover.png" alt="" />收文
                </li> -->
              </ul>
            </div>
            <div class="ai-right-bar">
               <!-- <div class="top-button" @click="clickTopNav">
               
                <img src="../assets/icon16.png" alt="" />
              </div> -->
                           
              <!-- <div class="top-button btn" @click="clickTopNav">
               
                <img src="../assets/icon17.png" alt="" />
              </div> -->
              <el-dropdown :hide-on-click="false" trigger="click">
                <div class="ai-avant btn">
                  <img src="../assets/icon18.png" alt="" />
                  <p>{{ username }}</p>
                </div>
                <el-dropdown-menu
                  slot="dropdown"
                  style="height: 50%; width: 20%; margin-right: -50px"
                >
                  <div class="user-menu-content">
                    <div class="user-info">
                      <div class="avatar-wrapper">
                        <img src="../assets/icon_tx.png" alt="" />
                      </div>
                      <div class="name-wrapper">
                        <div class="name">{{ username }}</div>
                        <div class="id">ID：001</div>
                      </div>
                    </div>
                    <div class="divider"></div>
                    <div class="options">
                      <!-- <a rel="opener" class="option" href="" target="_blank">
                        <div class="icon personal-center"></div>
                        <div class="text">个人中心</div>
                      </a> -->
                      <!-- <div rel="opener" class="option" href="" target="_blank" @click="togrzx()">
                        <div class="icon personal-center"></div>
                        <div class="text">个人中心</div>
                      </div> -->
                      <!-- <div
                        rel="opener"
                        class="option"
                        href=""
                        target="_blank"
                        @click="wdwd()"
                      >
                        <div class="icon my-document"></div>
                        <div class="text">我的文档</div>
                      </div> -->
                      <!-- <a rel="opener" class="option" href="" target="_blank">
                        <div class="icon my-favourite"></div>
                        <div class="text">我的收藏</div>
                      </a> -->
                      <a class="option" @click="logout()">
                        <div class="icon logout"></div>
                        <div class="text">退出登录</div>
                      </a>
                    </div>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </el-header>

      <el-main style="transition: ease-out 0.4s; display: flex">
        <!-- <div class=""> -->
        <div class="el-dialog__wrapper">
          <div
            role="dialog"
            aria-modal="true"
            aria-label="修改密码"
            class="el-dialog"
            style="margin-top: 25vh; margin-left: 30vw; width: 40%"
          >
            <div class="el-dialog__header">
              <span class="el-dialog__title">修改密码</span
              ><button
                type="button"
                aria-label="Close"
                class="el-dialog__headerbtn"
              >
                <i class="el-dialog__close el-icon el-icon-close"></i>
              </button>
            </div>
            <div class="el-dialog__body">
              <span
                ><div class="dChangePwd">
                  <form class="el-form demo-ruleForm">
                    <div
                      class="el-form-item el-form-item--feedback el-form-item--medium"
                    >
                      <label
                        for="oldPwd"
                        class="el-form-item__label"
                        style="width: 120px"
                        >输入旧密码</label
                      >
                      <div
                        class="el-form-item__content"
                        style="margin-left: 120px"
                      >
                        <div class="el-input el-input--medium el-input--suffix">
                          <!---->
                          <!-- <el-input placeholder="请输入密码" v-model="passw" show-password style="width: 64%;"></el-input> -->

                          <el-input
                            show-password
                            type="password"
                            autocomplete="off"
                            v-model="old_passw"
                          />
                        </div>
                        <!---->
                      </div>
                    </div>
                    <div
                      class="el-form-item el-form-item--feedback el-form-item--medium"
                    >
                      <label
                        for="pass"
                        class="el-form-item__label"
                        style="width: 120px"
                        >输入新密码</label
                      >
                      <div
                        class="el-form-item__content"
                        style="margin-left: 120px"
                      >
                        <div class="el-input el-input--medium el-input--suffix">
                          <!---->
                          <!-- <input
                            type="password"
                            autocomplete="off"
                            maxlength="18"
                            class="el-input__inner"
                          /> -->
                          <el-input
                            show-password
                            type="password"
                            autocomplete="off"
                            v-model="new_passw"
                          />
                          <!----><span class="el-input__suffix"
                            ><span class="el-input__suffix-inner"
                              ><!----><!----><!----><!----></span
                            ><!----></span
                          ><!----><!---->
                        </div>
                        <!---->
                      </div>
                    </div>
                  </form>
                  <div class="footerInline">
                    <div class="tsFont">提示：密码为6-16位字符。</div>
                    <div slot="footer" class="dialog-footer">
                      <button
                        @click="changePwd()"
                        type="button"
                        class="el-button el-button--primary el-button--mini"
                        style="margin: 0px"
                      >
                        <!----><!----><span>确 定</span>
                      </button>
                    </div>
                  </div>
                </div></span
              >
            </div>
            <!---->
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import App from "../App";
import Wordcloud from "../components/Wordcloud.vue";
import { VueEditor } from "vue2-editor";
import CustomSteps from "../components/Step.vue";
//   import store from '../store/index'
import { mapState } from "vuex";
// ****************************
import Quill from "quill";
let BlockEmbed = Quill.import("blots/block/embed");
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute(
      "style",
      "padding: 20px; border: 1px dashed #488aff; color:red; position: relative;"
    );
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement("button");
    insertButton.innerHTML = "应用";
    insertButton.className = "btn-insert";
    insertButton.style.position = "absolute";
    // 按钮颜色
    insertButton.style.backgroundColor = "#488aff";
    // 字体为白色
    insertButton.style.color = "white";
    // insertButton.style.top = '10px';
    insertButton.style.right = "70px";
    insertButton.addEventListener("click", () => {
      // console.log(value.editorClass, '编辑器')
      // console.log(value.newText, '续写内容！')
      // // 这里的位置，是替换的传position，是插入的传position加length
      // console.log(value.Position, '位置')
      // console.log(value.selectedTextlength, '长度？？？？？？？？')
      // console.log(value.editorClass.getSelection(), '光标位置？？？？')
      if (value.flag == 1) {
        value.editorClass.insertText(
          value.Position + value.selectedTextlength,
          value.newText
        );
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement("button");
    cancelButton1.innerHTML = "取消";
    cancelButton1.className = "btn-cancel";
    cancelButton1.style.position = "absolute";
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = "#e64721";
    // 字体为白色
    cancelButton1.style.color = "white";
    cancelButton1.style.right = "10px";
    cancelButton1.addEventListener("click", () => {
      node.remove();
      // alert('取消按钮点击');
      // // 这里可以添加取消按钮的逻辑
    });
    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = "customBlock";
CustomBlock.tagName = "div";

Quill.register({
  "formats/customBlock": CustomBlock,
});
import store from "../store/index";
import {
  js,
  fwsc,
  getSecDtDatas,
  getSuccessInfo,
  getSuccessInfoItem,
  getLabel1,
  getLabel2,
  testcheck,
  bcwz,
  rewrite,
  xz,
  cyt,
  get_user,
  getwrite,
  get_login_user,
  xg,
} from "../api/home.js"; // 接口请求
 export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id
    username() {
      return this.$store.state.username;
    },
    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 600}px`,
      };
    },
    id() {
      return this.$store.state.id;
    },
    imageSrc() {
      return `data:image/jpeg;base64,${this.base64Image}`; // 注意根据实际的MIME类型替换'image/jpeg'
    },
  },
  data() {
    return {
      sure_passw: "",
      new_passw: "",
      old_passw: "",
      form1: {
        xm: "",
        xb: "",
        age: "",
        szdw: "",
        ssgw: "",
        sjgzly: "",
        ssqy: "",
      },
      form: {
        type: [],
        type1: [],
        type2: [],
      },
      formLabelWidth: "100px",
      dialogVisible: false,

      up_img: false,
      nan_img: false,
      nv_img: false,
      backgroundImage: "", // 默认背景图
      tags: [], // 存储从后端获取的“句式特点”标签
      tags1: [], // 存储从后端获取的“句式特点”标签
      tags2: [], // 存储从后端获取的“句式特点”标签

      base64Image: "",
      xb: "",
      xm: "",
      szdw: "",
      ssgw: "",
      sjgzly: "",
      ssqy: "",
      age: "",
      wcdata: [],
      // 定义变量存词云的返回值，然后用vuex传给词云组件
      fzqwnr: "",
      newText: "",
      newText1: "",
      newText2: "",
      newText3: "",
      xxdialog: false,
      xxcontent: "",
      kxdialog: false,
      sxdialog: false,
      rsdialog: false,
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      selectedText: "",
      con_loading: false,
      loading1: false,
      correctionList: [],
      jydis: true,
      navShow: true,
      textarea: "",
      textarea2: [],
      textarea21: [],
      textarea22: [],

      textarea3: "",
      textarea4: "",
      textarea4: "",
      dialogShow: false,
      dialog2Show: false,
      gzbj: [],
      stepActived: 1,
      loading: false,
      curIndex: "2",
      articles: "",
      artShow: false,
      mask: false,
      maskAll: false,
      /**
       * @description:  功能性按钮
       * @return {*}    传入组件->FunctionTable
       */
      steps: [
        { title: "录入角色" },
        { title: "录入写作风格" },
        { title: "生成词云" },
      ],

      currentStep: 2, // 初始化当前步骤为第二个步骤
      secRecommend: [], // 第二步推荐
      dynamicTags: [],
      inputVisible: true,
      inputValue: "",
      resDataItem: [],
      progressPercent: 0,
      fetchResPrams: [],
      buttonShow: true,
      drawer: false,
      direction: "ltr",
      activeNames: [],
      // ruleForm: {
      //   name: "",
      //   region: "",
      //   date1: "",
      //   date2: "",
      //   delivery: false,
      //   type: [],
      //   type1: [],
      //   type2: [],
      //   resource: "",
      //   desc: "",
      // },
      // rules: {
      //   name: [
      //     { required: true, message: "请输入活动名称", trigger: "blur" },
      //     { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
      //   ],
      //   region: [
      //     { required: true, message: "请选择活动区域", trigger: "change" },
      //   ],
      //   date1: [
      //     {
      //       type: "date",
      //       required: true,
      //       message: "请选择日期",
      //       trigger: "change",
      //     },
      //   ],
      //   date2: [
      //     {
      //       type: "date",
      //       required: true,
      //       message: "请选择时间",
      //       trigger: "change",
      //     },
      //   ],
      //   type: [
      //     {
      //       type: "array",
      //       required: true,
      //       message: "请至少选择一个活动性质",
      //       trigger: "change",
      //     },
      //   ],
      //   resource: [
      //     { required: true, message: "请选择活动资源", trigger: "change" },
      //   ],
      //   desc: [{ required: true, message: "请填写活动形式", trigger: "blur" }],
      // },
    };
  },
  components: {
    App,
    // 注册组件
    CustomSteps,
    VueEditor,
    Wordcloud,
  },
  mounted() {
    // this.getuser();
    // this.getuser();
    // this.get_write();
  },
        methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
    sfw111() {
      this.$router.push("/sfw_notadmin");
    },
    clickTopfw() {
      this.$router.push("/fw_notadmin");
    },
    clickTopbmai() {
      this.$router.push("/bmai_notadmin");
    },
    async changePwd() {
      let params = {
        userName: this.username,
        oldPassWord: this.old_passw,
        newPassWord: this.new_passw,
        // sure_password: this.sure_passw,
      };
      let res = await xg(params);
      // let res = await xg(params);
      // console.log(res.data.data.code);
      if (res.data.status_code == 200) {
        // this.$message({
        this.$message({
          message: "修改成功，请重新登录",
          type: "success",
        });
        // this.$store.dispatch("logout");
        this.$router.push("/login");
      } else {
        this.$message({
          message:res.data.message ,
          type: "error",
        });
      }
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    async getcyt() {
      // this.currentStep = 3;
      let params = {
        name: this.username,
      };
      let res = await cyt(params);
      this.wcdata = res.data.data;
      console.log("词云数据:", this.wcdata);
      this.$store.dispatch("updatecy", this.wcdata);
    },
    async onSubmit() {
      let params = {
        name: this.username,
        xm: this.form1.xm,
        xb: this.form1.xb,
        age: this.form1.age,
        szdw: this.form1.szdw,
        ssgw: this.form1.ssgw,
        sjgzly: this.form1.sjgzly,
        ssqy: this.form1.ssqy,
      };
      let res = await js(params);
      this.dialogVisible = false;
      // this.getuser();
      this.getcyt();
      this.$message({
        message: "修改成功",
        type: "success",
      });
      // this.getuser();
    },
    async scfw() {
      console.log("FormData123456:", this.formData.getAll("files[]"));
      if (!this.formData) {
        console.error("没有文件被上传");
        return;
      }
      try {
        let res = await fwsc(this.formData); // 直接传递 formData 进行上传
        console.log("w看一下返回值结果:", res.data.status_code);
        if (res.data.status_code == 200) {
          this.$message({
            message: "上传成功",
            type: "success",
          });
        } else {
          this.$message({
            message: "上传失败",
            type: "error",
          });
        }
        // this.loading = false;
        //         // this.xxdialog=true;
        //         this.xxcontent=this.newText;
        //         // this.kxdialog=true;
        //         // this.sxdialog=true;
        //         // this.rsdialog=true;
        //         // this.textarea2=res.data.data;
        //         // this.textarea3=res.data.data;
        //         // this.textarea4=res.data.data;
        //         // this.textarea5=res.data.data;
        //         // this.textarea6=res.data.data;
        //         // this.textarea7=res.data.data;
        //         // this.textarea8=res.data.data;
        // console.log("上传结果:", res);
      } catch (error) {
        console.error("上传失败:", error);
      }
    },
    beforeAvatarUpload(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "述职报告"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload1(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "心得体会"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload2(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];
      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "领导讲话"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload3(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "工作方案"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload4(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "调研报告"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    beforeAvatarUpload5(files) {
      this.text = false;
      const formData = new FormData();

      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "files[]",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          formData.append("file_type", "宣传材料"); // 添加额外字段
          formData.append("name", this.username); // 添加额外字段

          this.formData = formData; // 将 formData 存储在组件的属性中
          console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    togrzx() {
      // alert(1)
      this.$router.push("/grzx1");
    },
    async getuser() {
      let res2 = await get_login_user();
    },
    async get_write() {
      let params = {
        name: this.username,
      };
      let res2 = await getwrite(params);
      // console.log(res2.data.data.szqt, "受众群体");
      this.tags = res2.data.data.jstd
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
      this.tags1 = res2.data.data.wzfg
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
      this.tags2 = res2.data.data.szqt
        .match(/'([^']+)'/g)
        .map((item) => item.replace(/'/g, ""));
    },

    showContextMenu(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener("click", this.hideContextMenu);
      }
    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener("click", this.hideContextMenu);
    },
    // 续写接口
    async sendToBackend() {
      this.loading1 = true;
      this.mask = true;
      let params1 = {
        text: this.selectedText,
        flag: "1",
      };
      let res = await rewrite(params1);
      this.newText = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "续写成功",
          type: "success",
        });
        // 注册自定义样式
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();
        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);
        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText,
              newText: this.newText,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 1,
            },
            Quill.sources.USER
          );
          // 将后端返回的续写文字插入到该位置后面
          // editor.insertText(position + this.selectedText.length, this.newText);
          // editor.formatText(position + this.selectedText.length, this.newText.length, 'color', 'red');
          // // 将光标移动到新插入的文本后面
          // editor.setSelection(position + this.selectedText.length + this.newText.length, 0);
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText);
          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText.length
          );
          console.log("Formatting text length:", this.newText.length);
          editor.formatText(
            editor.getLength() - this.newText.length,
            this.newText.length
          );

          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "续写失败",
          type: "error",
        });
      }
    },

    // 2.扩写接口
    async sendToBackend2() {
      this.loading1 = true;
      this.mask = true;
      let params2 = {
        text: this.selectedText,
        flag: "2",
      };
      let res = await rewrite(params2);
      this.newText1 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "扩写成功",
          type: "success",
        });
        // this.loading = false;
        //         // this.xxdialog=true;
        //         this.xxcontent=this.newText;
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          const position = allText.indexOf(this.selectedText);

          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText1,
              newText: this.newText1,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 2,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText1);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText1.length
          );
          console.log("Formatting text length:", this.newText1.length);
          editor.formatText(
            editor.getLength() - this.newText1.length,
            this.newText1.length
          );
          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "扩写失败",
          type: "error",
        });
      }
    },
    // 3.缩写接口
    async sendToBackend3() {
      this.loading1 = true;
      this.mask = true;
      let params3 = {
        text: this.selectedText,
        flag: "3",
      };
      let res = await rewrite(params3);
      this.newText2 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "缩写成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText2,
              newText: this.newText2,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 3,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText2);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText2.length
          );
          console.log("Formatting text length:", this.newText2.length);
          editor.formatText(
            editor.getLength() - this.newText2.length,
            this.newText2.length
          );
          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "缩写失败",
          type: "error",
        });
      }
    },
    // 4.润色接口
    async sendToBackend4() {
      this.loading1 = true;
      this.mask = true;
      let params4 = {
        text: this.selectedText,
        flag: "4",
      };
      let res = await rewrite(params4);
      this.newText3 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading1 = false;
        this.mask = false;
        this.$message({
          message: "润色成功",
          type: "success",
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(
            position + this.selectedText.length,
            "customBlock",
            {
              html: this.newText3,
              newText: this.newText3,
              editorClass: this.$refs.editor.quill,
              Position: position,
              selectedTextlength: this.selectedText.length,
              flag: 4,
            },
            Quill.sources.USER
          );
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText3);

          // 将新插入的文本格式化为红色
          console.log(
            "Formatting text at position:",
            editor.getLength() - this.newText3.length
          );
          console.log("Formatting text length:", this.newText3.length);
          editor.formatText(
            editor.getLength() - this.newText3.length,
            this.newText3.length
          );

          editor.setSelection(editor.getLength(), 0);
        }
      } else {
        this.$message({
          message: "润色失败",
          type: "error",
        });
      }
    },
    copyText() {
      // 复制逻辑
      console.log("复制");
    },
    pasteText() {
      // 粘贴逻辑
      console.log("粘贴");
    },
    cutText() {
      // 剪切逻辑
      console.log("剪切");
    },
    // }
    async baocun() {
      console.log(this.$route.query.userName);
      let params = {
        wznr: this.textarea4.replace('"', '/"'),
        wzlx: "述职报告",
        userName: this.username,
      };
      let resbcwz = await bcwz(params);
      //  console.log(resbcwz)
      resbcwz.data.status_code == 200
        ? this.$message({
            message: "保存成功,请前往我的文档查看",
            type: "success",
          })
        : this.$message({
            message: "保存失败",
            type: "error",
          });
    },
    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "************************************");
      // this.textarea3 = html;
      // return html;
    },
    getPlainText(html) {
      const div = document.createElement("div");
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    },
    //     getHtmlContent(html) {
    //   const div = document.createElement('div');
    //   div.innerHTML = html;
    //   return div.innerHTML;
    // },

    onCopySuccess() {
      console.log("复制成功");
    },
    onCopyError() {
      console.log("复制失败");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    ignore() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
    },
    highlightChange(text1, text2) {
      console.log(this.textarea4);
      let changeData = text2;
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.textarea4 = this.removetext(this.textarea4);
      this.textarea4 = this.textarea4.replace(text1, changeData);
    },
    highlightError(error, word) {
      this.textarea4 = this.removetext(this.textarea4);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.textarea4 = this.textarea4.replace(error, a);
      console.log(this.textarea4);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(this.$refs.editor.$el, "editorIframe");
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    removetextp(text) {
      let str = text;
      let reg1 = new RegExp(`<p>`, "g");
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</p>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    onCopySuccess() {
      this.$message({
        message: "内容已复制到剪贴板！",
        type: "success",
      });
    },
    onCopyError() {
      this.$message({
        message: "复制失败，请手动复制!",
        type: "warning",
      });
    },

    async jiaoyan() {
      this.textarea4 = this.removetext(this.textarea4);
      this.con_loading = true;
      // let text = this.removetextp(this.textarea4)
      testcheck({
        test_all: this.textarea4,
      }).then(async (data) => {
        this.correctionList = data.data.data;
        this.con_loading = false;
        if (this.correctionList.length == 0) {
          this.jydis = true;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          console.log(data, "619");
          this.jydis = false;
        }
      });
    },
    handleClosedrawer() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    // async jiaoyan(){
    //   this.jydis = false
    // },
    save() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.jydis = true;
    },
    fanhui() {
      this.textarea4 = this.removetext(this.textarea4);
      this.activeNames = [];
      this.drawer = false;
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    handleStepClick(index) {
      this.lock++;
      this.activeIndex = index; // 更新activeIndex的值
    },
    upShow() {
      this.navShow = !this.navShow;
    },
    async textareaFocus() {
      let data = await getLabel1();
      this.gzbj = data.data;
      this.dialogShow = true;
    },
    closeDialog() {
      this.dialogShow = false;
    },
    textarea2Focus() {
      this.dialog2Show = true;
    },
    closeDialog2() {
      this.dialog2Show = false;
    },
    async getText(e) {
      console.log(e);
      let params = {
        label: e,
      };
      let data = await getLabel2(params);
      console.log(data, "583");
      this.secRecommend = data.data;
      this.textarea = e;
      this.textarea2 = [];
    },
    getText2(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea2.push(e);
        this.dynamicTags.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm(e);
      } else {
        console.log(111);
      }
    },
    getText21(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea21.push(e);
        this.dynamicTags1.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm1(e);
      } else {
        console.log(111);
      }
    },
    getText22(e) {
      if (!this.dynamicTags2.includes(e)) {
        this.textarea22.push(e);
        this.dynamicTags2.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm2(e);
      } else {
        console.log(111);
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickszbg() {
      this.$router.push("/xz");
    },

    //角色
    clickTopld() {
      this.$router.push("/grzx");
    },
    clickTopLd() {
      this.$router.push("/sfw");
    },
    //   提示词
    clickToptsc() {
      this.$router.push("/tsc");
    },
    firstNextStep() {
      if (this.textarea == "") {
        this.$notify({
          title: "提示",
          message: "请填写工作背景",
          type: "warning",
        });
      } else if (this.textarea2.length == 0) {
        this.$notify({
          title: "提示",
          message: "请填写工作要点关键词",
          type: "warning",
        });
      } else {
        this.mask = true;
        if (this.mask == true) {
          this.getInfo(this.textarea, this.dynamicTags);
        }
      }
    },
    async getInfo(c1, c2) {
      this.loading = true;
      let params = {
        test_1: c1,
        test_2: c2,
      };
      let res = await getSecDtDatas(params);
      // console.log(res.data.status_code,'我是状态码11111')
      // console.log(res.data.message,'我是提示33332222')
      if (res.data.status_code == 200) {
        // 弹出提示
        this.$message({
          message: res.data.message,
          type: "success",
        });
        this.textarea3 = "";

        // console.log(res.data, 55555555);
        this.textarea3 = res.data.data;
        // console.log(this.textarea3,66666);
        // 这里放置需要执行的逻辑或调用其他方法
        this.buttonShow = true;
        // this.stepActived = 2;
        this.currentStep = 2;
        this.loading = false;
        this.mask = false;
        this.curIndex = "2";
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10001) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      } else if (res.data.status_code == 10002) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    repeatStep() {
      this.loading = true;
      this.mask = true;
      if (this.mask == true) {
        this.getInfo(this.textarea, this.dynamicTags);
      }
    },
    success() {
      this.artShow = true;
      this.loading = true;
      // 这里放置需要执行的逻辑或调用其他方法
      if (this.textarea3 == "") {
        this.$notify({
          title: "提示",
          message: "大纲内容为空，无法生成",
          type: "warning",
        });
      } else {
        this.progressPercent = 0;
        this.getSuccessInfo(this.textarea3, this.textarea);
      }
    },
    // 依次请求接口
    async fetchResData(index, nextIndex) {
      if (index >= this.fetchResPrams.length) {
        this.fetchData(nextIndex);
        return;
      }
      console.log(this.fetchResPrams[index], "675");
      let pams = {
        string: this.fetchResPrams[index],
        id_name: this.textarea,
      };
      const response = await getSuccessInfoItem(pams);
      if (response.data.status_code == 200) {
        // this.$message({
        //   message: response.data.message,
        //   type: 'success'
        // });
        this.textarea4 += response.data.data;

        let totalLength = 0;
        let allSubArrays = this.resDataItem.filter(
          (item) => typeof item != "string"
        );
        allSubArrays.forEach((subArray) => {
          totalLength += subArray.length;
        });
        let kuai = 100 / totalLength;
        this.progressPercent += Math.floor(kuai);
        await this.fetchResData(index + 1, nextIndex);
      } else if (response.data.status_code == 500) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10001) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      } else if (response.data.status_code == 10002) {
        this.$message({
          message: response.data.message,
          type: "error",
        });
      }
    },
    // 一段式生成
    async fetchData(index) {
      if (index >= this.resDataItem.length) {
        this.progressPercent = 100;
        this.maskAll = false;
        this.buttonShow = false;
        return;
      }
      if (typeof this.resDataItem[index] == "string") {
        this.textarea4 += this.resDataItem[index];
        await this.fetchData(index + 1);
      } else {
        this.fetchResPrams = this.resDataItem[index];
        this.fetchResData(0, index + 1);
      }
      // this.stepActived = 3;
      this.currentStep = 3;
      this.loading = false;
    },
    async getSuccessInfo(c1, c2) {
      this.loading = true;
      this.maskAll = true;
      let params = {
        string: c1,
        work: c2,
      };
      let res = await getSuccessInfo(params);
      if (res.data.status_code == 200) {
        // this.$message({
        //   message: res.data.message,
        //   type: 'success'
        // })
        this.textarea4 = "";
        this.resDataItem = res.data.data;
        this.fetchData(0);
        this.currentStep = 3;
        // this.stepActived = 3;
        this.loading = false;
        // }
      } else if (res.data.status_code == 500) {
        this.$message({
          message: res.data.message,
          type: "error",
        });
      }
    },
    clickTab(i) {
      this.curIndex = i;
      // }
    },
  },
  watch: {
    xb(newVal) {
      if (newVal) {
        this.backgroundImage = "../img/bga.png"; // 男背景图
      } else {
        this.backgroundImage = "../img/hahu3.png"; // 女背景图
      }
    },
  },
  // };
};
</script>
<style lang="less" scoped>
.dChangePwd .footerInline .tsFont {
  width: 100%;
  line-height: 30px;
  margin-top: 0;
  color: #f60;
}
.ellipsis-container {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-content {
  display: inline;
}

.ellipsis .cell {
  height: calc(100vh *  50 / 1080);
  /* 你可以根据需要调整高度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 移除悬停展示的样式 */
.ellipsis .cell:hover {
  /* 移除或覆盖悬停展示的样式 */
  text-overflow: ellipsis;
  /* 确保悬停时不展示完整内容 */
}

.custom-label {
  margin-left: 50px;
  font-weight: 600;
  font-size: calc(100vw *  18 / 1920);
  letter-spacing: 0px;
  line-height: 16px;
  //  active时的样式
  // :active{
  // color:#fff;
  // background-color:#3a6bc6;
  // border-radius:20px;
  // }
}

.tableCon {
  .ellipsis {
    height: 200px;
  }
}

.highlight {
  background-color: yellow;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: linear-gradient(76deg,
  //     #07389c 0%,
  //     #3d86d1 0%,
  //     #3448b3 100%);

  img {
    width: 120px;
    height: 48px;
  }

  p {
    border: 1.54px solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vh * 40 / 1080);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: 1px solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vh * 130 / 1080);
            color: #000;
            font-size: calc(100vh * 14 / 1080);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vh * 14 / 1080);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vh * 16 / 1080);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vh * 14 / 1080);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw *  12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 /1080);
  padding: 20px 10px;
  // overflow: hidden;
}

.LgscCon {
  width: 1002px;
  height: auto;
  margin: 0 auto;

  .button {
    width: 108px;
    height: 58px;
    background: #4c91ff;
    border-radius: 0px 100px 100px 0px;
    cursor: pointer;
  }

  img {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }

  p {
    line-height: 58px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flexcz {
  display: flex;
  align-items: center;
}

.flexNoCz {
  display: flex;
  justify-content: center;
}

.floatLeft {
  float: left;
}

.lgfl {
  width: 100%;
  height: calc(100vh * 40/ 1080);
  // background: #000;
  margin-top: 42px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  // font-weight: 600;
  display: flex;
  align-items: center;
}

.lgfl1 {
  margin-top: 10px;
}

.mgr1 {
  margin-right: 26px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  // font-weight: 600;
  // cursor: pointer;
}

.mgr {
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  // font-weight: 600;
  width: 96px;
  margin-right: 26px;
  cursor: pointer;

  &:hover {
    width: 96px;
    height: 100%;
    background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
    border-radius: 20px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    // font-weight: 600;
  }

  &:active {
    width: 96px;
    height: 100%;
    background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
    border-radius: 20px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.activeFl {
  width: 96px;
  height: 100%;
  background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
  border-radius: 20px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  // font-weight: 600;
}

.nrCon {
  margin-top: 30px;
  width: 100%;
  height: calc(100vh * 750 / 1080);
  overflow-y: scroll;

  .nrConLeft {
    width: calc(50% - 10px);
    height: auto;
    float: left;
    // display: flex;
    // flex-flow: row wrap;
    // // align-items: flex-start;
    // // flex-basis: auto;
    // align-items: flex-start;
    // margin-right: 20px;
  }

  .nrConRight {
    width: calc(50% - 10px);
    height: auto;
    float: right;
  }
}

.nrConLeftBk {
  width: auto;
  height: auto;
  background: #ffffff;
  box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.13);
  border-radius: 12px;
  padding: 28px 40px;
  position: relative;
  margin-bottom: 20px;

  .bkTitle {
    height: auto;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    text-align: left;
    // font-weight: 600;
  }

  .bkCon {
    height: auto;
    margin-top: 19px;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    text-align: left;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
  }

  .bkBtn {
    width: 100%;
    margin-top: 16px;
    height: auto;

    // background: #000;
    .bkBtnLeft {
      width: 50%;
      height: 24px;

      .xztg {
        width: auto;
        height: 24px;
        background: #f5f8fc;
        border-radius: 12px;
        padding: 0 10.5px;
        font-family: PingFangSC-Regular;
        font-size: calc(100vw *  14 / 1920);
        color: #7b7e9e;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        cursor: pointer;
      }

      .qt {
        width: auto;
        height: 24px;
        background: #f5f8fc;
        border-radius: 12px;
        padding: 0 10.5px;
        font-family: PingFangSC-Regular;
        font-size: calc(100vw *  14 / 1920);
        color: #7b7e9e;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        margin-left: 10px;
        cursor: pointer;
      }
    }

    .bkBtnright {
      // width: 131px; //复制加收藏
      width: 54px;
      height: 24px;

      .fz {
        width: 53px;
        height: 24px;
        cursor: pointer;

        img {
          width: 16px;
          height: 16px;
        }

        p {
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw *  14 / 1920);
          color: #666666;
          letter-spacing: 0;
          line-height: 24px;
          font-weight: 500;
        }
      }

      .sc {
        width: 53px;
        height: 24px;
        cursor: pointer;

        img {
          width: 16px;
          height: 16px;
        }

        p {
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw *  14 / 1920);
          color: #666666;
          letter-spacing: 0;
          line-height: 24px;
          font-weight: 500;
        }
      }
    }
  }
}

.flexLd {
  display: flex;
  justify-content: space-between;
}

::v-deep(.el-input__inner) {
  // height: 58px;
  border-radius: 0;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.dialog-content {
  height: 400px;
  overflow-y: scroll;
}
</style>