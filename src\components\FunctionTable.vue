<!--
 * @Author: z<PERSON><PERSON>ying 2515550548@.com
 * @Date: 2023-08-01 13:49:26
 * @LastEditors: zhangzuying 2515550548@.com
 * @LastEditTime: 2023-08-01 16:54:34
 * @FilePath: \gld-px\src\app\zxksxt\components\FunctionTable.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <section class='base-header-container'>
    <header :style="{paddingBottom:paddingBottom||'20px'}">
      <span class="jee-font-second-light"
            :style="{marginRight:marginRight||'10px'}"
            v-if="title">{{title}}</span>
      <div class="button-list-wrap">
        <template v-for="(item, index) in buttons">
          <Button :class="className||'list-wrap-btn'"
                     :style="item.style"
                     :key="index"
                     v-if="!item.btnType&&index<maxShowIndex&&!item.isHidden"
                     :size="item.size||'medium'"
                     :type="item.plain?'': 'primary'"
                     :disabled="item.disabled"
                     @click="handleEvent(item.type, $event,item)">
            <!-- <BaseIcon v-if="item.icon" class="baseIconStyle" :iconClass="item.icon" :style="{ fontSize: item.fontSize, width: item.iconSize, height: item.iconSize }"></BaseIcon> -->
            
            <img :src="item.iconSrc" :style="{ width: item.iconSize, height: item.iconSize }" alt="">
            <span>{{ item.text }}</span>
          </Button>
        </template>
        <slot></slot>
      </div>
    </header>
    <!-- <el-collapse-transition>
      <div v-show="showAlertIcon && showAlert && showType === 'click'">
        <el-alert :title="content"
                  type="warning"
                  show-icon
                  @close="showAlert = false"></el-alert>
      </div>
    </el-collapse-transition> -->
    <div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'baseHeader',
  props: {
    buttons: {
      type: Array,
      default: () => []
    },
    maxShowIndex: {
      type: Number,
      default: 20
    },
    title: {
      type: String,
      default: ''
    },
    paddingBottom: {
      type: String,
      default: '20px'
    },
    marginRight: {
      type: String,
      default: '10px'
    },
    className: {
      type: String,
      default: ''
    },
    showAlertIcon: {
      type: Boolean,
      default: true
    },
    content: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'caozuoshuoming1'
    },
    showType: {
      type: String,
      default: 'click'
    },
    popoverData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      show3: true,
      showAlert: true
    }
  },
  mounted () {
  },
  computed: {
    isPrimaryIndex: function () {
      let index = 0
      for (let i = 0; i < this.buttons.length; i++) {
        if (!this.buttons[i].isHidden) {
          index = i
          break
        }
      }
      return index
    }
  },
         methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
     togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    handleCommand (type, val, item) {
      this.$emit(`handleCommand${type}`, val, item)
    },
    handleEvent (type, e, item) {
      e.preventDefault()
      this.$emit(`handle${type}`, item)
    }
  }
}
</script>

<style lang="less" scoped>
.base-header-container {
  header {
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    .button-list-wrap {
      display: flex;
      flex-wrap: wrap;
      ::v-deep .el-button:focus, .el-button:hover {
        // color: var(--fontColor);
        // border-color: var(--btnbkg) !important;
        // background-color:  transparent !important;
      }
      .jee-dropdown-container,
      .el-button {
        // margin-bottom: 10px;
        margin-right: 10px;
        margin-left: 0px;
        border-radius: 0;
      }
      .baseIconStyle {
        margin-right: 10px;
      }
    }
    .tip {
      cursor: pointer;
    }
    .tipCenter {
      cursor: pointer;
      margin-bottom: 10px;
    }
  }
  .el-alert {
    display: flex !important;
    align-items: flex-start !important;
    margin-bottom: 20px;
  }
}
</style>
<style lang="less">
.custom-grayBlue {
  .el-alert__icon {
    margin-top: 3px;
  }
}
</style>
