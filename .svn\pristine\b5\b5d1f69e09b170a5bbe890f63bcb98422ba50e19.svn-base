<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle">
          <!-- <img src="../assets/lo.png" alt="" />
          <p v-if="navShow">思盒</p> -->
          <img src="../img/logo05.png" width="100%" alt="" />
        </div>
        <div class="ai-header">
          <div class="ai-bar">
            <div class="ai-left-bar">
              <ul class="ai-left-bar-ul">
                <li @click="clickTopTy" class="ai-left-bar-li">
                  <img src="../assets/icon15-h.png" alt="" />Ai读
                </li>
                <li class="ai-left-bar-li" @click="clickTopAi">
                  <img src="../assets/icon101.png" alt="" />AI写
                </li>
                <!-- <li @click="clickTopNav" class="ai-left-bar-li">
                    <img src="../assets/icon102-h.png" alt="" />
                    AI校对润色
                  </li>
                  <li @click="clickTopNav" class="ai-left-bar-li">
                    <img src="../assets/icon12-h.png" alt="" />笔墨文库
                  </li> -->
                <li @click="clickTopLg" class="ai-left-bar-li">
                  <img src="../assets/icon13-h.png" alt="" />Ai想
                </li>
                <li @click="clickTopJq" class="ai-left-bar-li">
                  <img src="../assets/icon14-h.png" alt="" />Ai听
                </li>

                <!-- <li class="ai-left-bar-li  actived">
                  <img src="../assets/icon-jj-hover.png" alt="" />辅助定密
                </li> -->
                <li @click="clickTopbmai" class="ai-left-bar-li">
                  <img src="../assets/icon204a.png" alt="" />助手
                </li>
              </ul>
            </div>
            <div class="ai-right-bar">
               <!-- <div class="top-button" @click="clickTopNav">
               
                <img src="../assets/icon16.png" alt="" />
              </div> -->
              <!-- <div class="top-button btn" @click="clickTopNav">
               
                <img src="../assets/icon17.png" alt="" />
              </div> -->
              <el-dropdown :hide-on-click="false" trigger="click">
                <div class="ai-avant btn">
                  <img src="../assets/icon18.png" alt="" />
                  <p>{{username}}</p>
                </div>
                <el-dropdown-menu slot="dropdown" style="height: 50%;width:20%;margin-right: -50px;">

                  <div class="user-menu-content">
                    <div class="user-info">
                      <div class="avatar-wrapper">
                        <img src="../assets/icon_tx.png" alt="" />
                      </div>
                      <div class="name-wrapper">
                        <div class="name">{{username}}</div>
                        <div class="id">ID：001</div>
                      </div>
                    </div>
                    <div class="divider"></div>
                    <div class="options">
                      <a rel="opener" class="option" href="" target="_blank">
                        <div class="icon personal-center"></div>
                        <div class="text">个人中心</div>
                      </a>
                      <div rel="opener" class="option" href="" target="_blank" @click="wdwd()">

                        <div class="icon my-document"></div>
                        <div class="text">我的文档</div>
                      </div>
                      <a rel="opener" class="option" href="" target="_blank">
                        <div class="icon my-favourite"></div>
                        <div class="text">我的收藏</div>
                      </a>
                      <a class="option">
                        <div class="icon logout"></div>
                        <div class="text">退出登录</div>
                      </a>
                    </div>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </el-header>
      <el-main v-loading="loading" element-loading-text="结果生成中，请稍后..."
        element-loading-background="rgba(255, 255, 255, 0.7)">
        <div class="LgscCon">
          <div class="fir-title">
            <div class="fir-kuai"></div>
            <p class="fir-title-p">辅助定密</p>
          </div>
          <div class="uploadButton">
            <el-upload class="upload-demo" action="#" :show-file-list="false" :before-upload="beforeAvatarUpload"
              :http-request="uploadFile" drag multiple>
              上传文件
              <!-- <div>
                  <img style="width: 100px; height: 100px;" src="../img/qujian.png" alt=""> <br />
                  <div class="t1">点击上传文件</div>
                  <div class="t2">仅支持上传png、jpg、pdf、word文件</div>
                </div> -->
            </el-upload>
          </div>
          <div class="nrCon">
            <!-- <div class="nrConLeft floatLeft">
              <el-upload class="upload-demo" action="#" :show-file-list="false" :before-upload="beforeAvatarUpload"
                :http-request="uploadFile" drag multiple>
                <div>
                  <img style="width: 100px; height: 100px;" src="../img/qujian.png" alt=""> <br />
                  <div class="t1">点击上传文件</div>
                  <div class="t2">仅支持上传png、jpg、pdf、word文件</div>
                </div>
              </el-upload>
            </div> -->
            <div class="nrConRight floatLeft">
              <div class="nrConLeftBk" v-if="this.qhsb == true">
                <!-- <div class="k-s"> -->
                <div class="bktitle">识别结果</div>
                <div class="fir-kuai1"></div>
                <div class="k-s" id="result" ref="myDiv"></div>
                <!-- </div> -->
                <!-- <div class="k-x"> -->
                <div class="bktitle mtp">
                  定密信息
                </div>
                <div class="fir-kuai1"></div>
                <div class="k-x">
                  <div class="bkUl">
                    <!--***-->
                    <span id="mj" ref="mjInfo"></span>
                  </div>
                  <div class="bkUl">
                    <span id="qx" ref="qxInfo"></span>
                  </div>
                </div>
                <!-- ***用于切换查看不同关键字识别结果的div -->
                <div id="parent-div">
                </div>

              </div>
              <div class="nrConLeftBk" v-if="this.qhsb == false">
                <div class="nrS">{{ text }}</div>
              </div>
              <div class="btnqh" v-if="this.qhsb == false">
                <el-button plain v-if="this.qhsb == false" @click="qhClick(1)">切换</el-button>
              </div>
              <div class="btnqh" v-if="this.btnxs == true">
                <el-button plain @click="qhClick(2)">切换</el-button>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import { getAsr1 } from "../api/home.js"; // ***接口请求
export default {
  data() {
    return {
      navShow: true,
      btn: "",
      file: "",
      filename: "",
      text: "",
      qhsb: true,
      btnxs: false,
      loading: false,
    };
  },
  components: {},
  mounted() { },
         methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
     togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickTopAi() {
      this.$router.push("/index");
    },
    clickTopLg() {
      this.$router.push("/lgsc");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    beforeAvatarUpload(file) {
      console.log(file);
      // ***
      // const isMP3 = file.type === "audio/mpeg";
      // const isMP4 = file.type === "video/mp4";
      const isJpg = file.type === "image/jpeg" || file.type === "image/pjpeg";
      const isPng = file.type === "image/png";
      const isPdf = file.type === "application/pdf";
      const isDoc = file.type === "application/msword";
      const isDocx = file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document";

      if (!isJpg && !isPng && !isPdf && !isDoc && !isDocx) {
        this.$message.error("只能上传jpg、png、pdf、word（.doc 或 .docx）文件！");
      }
      return isJpg || isPng || isPdf || isDoc || isDocx;
    },
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    uploadFile(item) {
      this.btnxs = false;
      this.blobToBase64(item.file, (dataurl) => {
        this.file = dataurl.split(",")[1];
        this.uploadMp(this.file);
      });
      this.filename = item.file.name;
    },
    async uploadMp(val) {
      this.loading = true;
      let params = {
        "file_name": this.filename,
        "base_string": val,
        "language": "Chinese",
      }
      let resData = await getAsr1(params);
      console.log(resData.data)
      this.$refs.myDiv.innerHTML = ''
      if (resData.data.body.length > 0) {
        console.log('resData不是空数组，执行操作');
        // let parentElement = document.getElementById('parent-div');
        // 使用forEach循环遍历对象数组  

        resData.data.body.forEach((item) => {
          // 遍历含关键字的数组
          var highlightedHTML = '';
          item.string.forEach((item, idx) => {
            // items.forEach(function (item) {
            if (Array.isArray(item)) {
              // 如果是数组，则包裹在高亮span中  
              // highlightedHTML += `<span class="highlight">${JSON.stringify(item)}</span>`;
              highlightedHTML += `<span class="highlight">${item.map(el => String(el)).join(', ')}</span>`;
            } else {
              // 如果不是数组，则包裹在普通span中  
              // highlightedHTML += `<span class="item">${item}</span></div>`;
              highlightedHTML += `<span class="item">${String(item)}</span>`;
            }
          }
          );
          this.$refs.myDiv.innerHTML += highlightedHTML + '<br /><br />';
          this.$refs.mjInfo.innerHTML = resData.data.years.Secret
          this.$refs.qxInfo.innerHTML = resData.data.years.years
        })






        // resData.data.forEach(function (obj, index) {
        //   if (resData.data.length > 1) {
        //     let currentIndex = 0;
        //     showDivInfo(resData.data[currentIndex], currentIndex);
        //     var div = document.createElement('div');
        //     var indexPlusOne = index + 1;
        //     div.textContent = '关键字  ' + indexPlusOne;
        //     div.className = 'child';

        //     console.log('254', div)
        //     let parentElement = document.getElementById('parent-div');
        //     parentElement.appendChild(div);
        //     function showDivInfo(obj, index) {
        //       // let div = document.createElement('div');
        //       // let indexPlusOne = index + 1;
        //       // div.textContent = '关键字' + indexPlusOne;
        //       // div.className = 'child';
        //       // parentElement.appendChild(div);
        //       // 创建一个结构用于存放高亮后的结果
        //     var highlightedHTML = '';
        //       resData.data[0].string.forEach((item, idx) => {
        //         // items.forEach(function (item) {
        //         if (Array.isArray(item)) {
        //           // 如果是数组，则包裹在高亮span中  
        //           highlightedHTML += `<span class="highlight">${JSON.stringify(item)}</span>`;
        //         } else {
        //           // 如果不是数组，则包裹在普通span中  
        //           highlightedHTML += `<span class="item">${item}</span></div>`;
        //         }
        //       }
        //       );
        //       const highlightedDiv = document.getElementById('result');
        //       highlightedDiv.innerHTML = highlightedHTML;
        //       // let highlightedHTML = '';
        //       let mj = resData.data[index].Secret;
        //       let qx = resData.data[index].years;
        //       const mjElement = document.getElementById('mj');
        //       mjElement.innerHTML = '密级:' + mj;
        //       const qxElement = document.getElementById('qx');
        //       qxElement.innerHTML = '期限:' + qx;


        //       var div = document.createElement('div');
        //       div.addEventListener('click', (function (obj, index) {
        //         return function () {
        //           alert(1)
        //           showDivInfo(obj, index);
        //         };
        //       })(obj, index));
        //     }
        //   }
        //   else {
        //     let mj = resData.data[0].Secret;
        //     let qx = resData.data[0].years;
        //     const mjElement = document.getElementById('mj');
        //     mjElement.innerHTML = '密级:' + mj;
        //     const qxElement = document.getElementById('qx');
        //     qxElement.innerHTML = '期限:' + qx;
        //     // let highlightedHTML = '';
        //     // 遍历含关键字的数组
        //     var highlightedHTML = '';
        //     resData.data[0].string.forEach((item, idx) => {
        //       // items.forEach(function (item) {
        //       if (Array.isArray(item)) {
        //         // 如果是数组，则包裹在高亮span中  
        //         // highlightedHTML += `<span class="highlight">${JSON.stringify(item)}</span>`;
        //         highlightedHTML += `<span class="highlight">${item.map(el => String(el)).join(', ')}</span>`;
        //       } else {
        //         // 如果不是数组，则包裹在普通span中  
        //         // highlightedHTML += `<span class="item">${item}</span></div>`;
        //         highlightedHTML += `<span class="item">${String(item)}</span>`;
        //       }
        //     }
        //     );
        //     const highlightedDiv = document.getElementById('result');
        //     highlightedDiv.innerHTML = highlightedHTML;
        //     // });
        //   }
        // });

      }
      else {
        this.$message.error("不含涉密信息!");
      }
      this.loading = false;

    },

    qhClick(val) {
      if (val == 1) {
        this.qhsb = true;
        this.btnxs = true;
      }
      if (val == 2) {
        this.qhsb = false;
        this.btnxs = false;
      }
    },

  },
};
</script>
<style lang="less" scoped>
.t1 {
  margin-top: 25px;
  // margin-left: -30px;
  font-weight: 600;
  font-size: calc(100vw *  25 / 1920);
}

.t2 {
  margin-top: 10px;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;

}

.fir-kuai1 {
  width: 6px;
  height: 16px;
  margin-top: -30px;
  margin-right: 8px;
  float: left;
  background: #4081ff;
  border-radius: 1.5px;
}

.k-s {
  height: 60%;
  font-family: PingFangSC-Semibold;
  background-color: #e2f0f8;
  font-weight: 550;
  text-align: left;
  padding-left: 15px;
  overflow-y: scroll;
  padding: 15px;
}

.k-x {
  font-family: PingFangSC-Semibold;
  background-color: #e2f0f8;
  font-weight: 550;
  height: calc(40% - 78px);
  background-color: #e2f0f8;
  overflow-y: scroll;
  padding: 15px;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(76deg,
      #07389c 0%,
      #3d86d1 0%,
      #3448b3 100%);

  img {
    width: 120px;
    height: 48px;
  }

  p {
    border: 1.54px solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: 40px;
        height: calc(100vh * 40/ 1080);
        border-radius: 20px;
        background: #f4f6f8;
        border: 1px solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 42px;
            width: 130px;
            color: #000;
            font-size: calc(100vw *  14 / 1920);
            line-height: 16px;
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: 16px;
              height: 16px;
              margin-right: 14px;
            }

            &:hover {
              background-image: linear-gradient(107deg,
                  #3a6bc6 0%,
                  #488aff 100%);
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: 20px;
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: 20px;
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw *  12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 /1080);
  padding: 20px 10px;
  // overflow: hidden;
}

.LgscCon {
  // width: 1002px;
  height: 100%;
  margin: 0 auto;

  .uploadButton {
    width: 150px;
    height: calc(100vh * 40/ 1080);
    font-size: calc(100vw *  14 / 1920);
    letter-spacing: 0px;
    flex-grow: 1;
    color: #fff;
    /* margin: 0 25px; */
    cursor: pointer;
    line-height: calc(100vh * 40/ 1080);
    margin-top: 20px;
    background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0px 1px 7px 0px #6478d4;
    border-radius: 20px;
    font-family: PingFangSC-Regular;
  }

  .button {
    width: 108px;
    height: 58px;
    background: #4c91ff;
    border-radius: 0px 100px 100px 0px;
  }

  img {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }

  p {
    line-height: 58px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw *  14 / 1920);
  letter-spacing: 0;
  line-height: 16px;
  overflow: hidden;
  // margin-top: 30px;
  height: 20px;

  .fir-kuai {
    width: 6px;
    height: 16px;
    margin-right: 8px;
    float: left;
    // margin-top: 2px;
    background: #4081ff;
    border-radius: 1.5px;
  }

  .fir-title-p {
    color: #222;
    font-weight: 500;
    font-size: calc(100vw *  14 / 1920);
    letter-spacing: 0;
    line-height: 16px;
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.floatLeft {
  float: left;
}

.nrCon {
  margin-top: 20px;
  height: calc(100% - 30px);

  .nrConLeft {
    width: 491px;
    height: auto;
    margin-right: 20px;
  }

  .nrConRight {
    width: 100%;
    height: calc(100% - 70px);
    position: relative;

    .btnqh {
      position: absolute;
      bottom: 45px;
      right: 35px;

      .el-button {
        width: 104px;
        height: 30px;
        line-height: 15px;
        padding: 5px;
        font-size: calc(100vw *  14 / 1920);
        margin-left: 0;
        margin-right: 9px;
        margin-top: 10px;
        font-family: SourceHanSansSC-Medium !important;
      }

      .nomgnl {
        margin: 0;
        margin-top: 10px;
      }
    }
  }
}

.nrConLeftBk {
  width: 100%;
  height: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.13);
  border-radius: 12px;
  padding: 24px;
  position: relative;

  .bktitle {
    height: auto;
    text-align: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
    margin-bottom: 10px;
    margin-left: 10px;
  }

  .bkUl {
    text-align: left;
    // margin-top: 15px;
    // margin-left: 15px;

  }

  .nrS {
    width: 100%;
    height: 100%;
    background: #f2f4f9;
    padding: 20px;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    line-height: 35px;
    font-weight: 400;
    text-align: left;
  }
}

::v-deep(.el-button) {
  span {
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw *  14 / 1920);
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
}

.mtp {
  margin-top: 15px;
}

.ckyl {
  font-family: PingFangSC-Medium;
  font-size: calc(100vw *  14 / 1920);
  color: #2c68ff;
  letter-spacing: 0;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

::v-deep(.el-upload-dragger) {
  width: 150px;
  height: 100%;
  /* box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.13); */
  /* border-radius: 12px; */
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
}

.upload-demo {
  width: 100%;
  height: auto;
  border: none;
}

.nrConRightBk {
  height: 238px;
}

.flexLd {
  display: flex;
  justify-content: space-between;
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw *  12 / 1920);
  right: 10px;
  height: 10px;
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }


      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.activeBtn {
  background: #f5f8fc;
  border: 1px solid rgba(31, 82, 176, 1);
  border-radius: 4px;
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw *  14 / 1920);
  color: #1f52b0;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}

::v-deep(.el-button:hover) {
  background: #f5f8fc;
  border: 1px solid rgba(31, 82, 176, 1);
  border-radius: 4px;
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw *  14 / 1920);
  color: #1f52b0;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}

::v-deep(.el-button.is-plain:focus, .el-button.is-plain:hover) {
  background: #f5f8fc;
  border: 1px solid rgba(31, 82, 176, 1);
  border-radius: 4px;
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw *  14 / 1920);
  color: #1f52b0;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}</style>