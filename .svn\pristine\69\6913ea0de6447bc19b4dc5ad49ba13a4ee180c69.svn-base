import Vue from 'vue';
import Router from 'vue-router';
import store from '../store'; // 确保
Vue.use(Router);
const router = new Router({
    routes: [
        {
            path: '/',
            redirect: '/login'
            // // 版本1.0.0
            // redirect: '/tywzsb'
            // redirect: '/jqkz'
        },
        // redirect: '/tywzsb'},
        {
            name: 'login',
            path: '/login',
            component: resolve => require(['../pages/login.vue'], resolve)
        },
        // 
        {
            name: 'tywzsb',
            path: '/tywzsb',
            component: resolve => require(['../pages/Tywzsb.vue'], resolve)
        },
        // 反馈界面
        {
            name: 'apply',
            path: '/apply',
            component: resolve => require(['../pages/apply.vue'], resolve)
        },
        // 应用界面
        {
            name: 'back',
            path: '/back',
            component: resolve => require(['../pages/back.vue'], resolve)
        },
        // 我的文档界面
        {
            name: 'wdwd',
            path: '/wdwd',
            component: resolve => require(['../pages/wdwd.vue'], resolve)
        },
        // 我的收藏界面
        {
            name: 'wdsc',
            path: '/wdsc',
            component: resolve => require(['../pages/wdsc.vue'], resolve)
        },
        {
            name: 'index',
            path: '/index',
            component: resolve => require(['../pages/Index.vue'], resolve)
        },
        {
            name: 'xdth',
            path: '/xdth',
            component: resolve => require(['../pages/xdth.vue'], resolve)
        },
        // 灵感素材
        {
            name: 'lgsc',
            path: '/lgsc',
            component: resolve => require(['../pages/Lgsc.vue'], resolve)
        },
        // 机器快转
        {
            name: 'jqkz',
            path: '/jqkz',
            component: resolve => require(['../pages/Jqkz.vue'], resolve)
        },
        // 通用文字识别
        {
            name: 'tywzsb',
            path: '/tywzsb',
            component: resolve => require(['../pages/Tywzsb.vue'], resolve)
        },
        // 辅助定密
        {
            name: 'fzdm',
            path: '/Fzdm',
            component: resolve => require(['../pages/Fzdm.vue'], resolve)
        },
        // BMAI
        {
            name: 'bmai',
            path: '/bmai',
            component: resolve => require(['../pages/bmai.vue'], resolve)
        },
        {
            name: 'bmai_notadmin',
            path: '/bmai_notadmin',
            component: resolve => require(['../pages/bmai_notadmin.vue'], resolve)
        },
        // ai写工作总结
        {
            name: 'gzzj',
            path: '/gzzj',
            component: resolve => require(['../pages/gzzj.vue'], resolve)
        },
        // ai写领导讲话
        {
            name: 'ldjh',
            path: '/ldjh',
            component: resolve => require(['../pages/ldjh.vue'], resolve)
        },
        //ai写工作方案 
        {
            name: 'gzfa',
            path: '/gzfa',
            component: resolve => require(['../pages/gzfa.vue'], resolve)
        },
        // ai写调研报告
        {
            name: 'dybg',
            path: '/dybg',
            component: resolve => require(['../pages/dybg.vue'], resolve)
        },
        // ai写宣传材料
        {
            name: 'xccl',
            path: '/xccl',
            component: resolve => require(['../pages/xccl.vue'], resolve)
        },
        // ai写生文通用化
        {
            name: 'other',
            path: '/other',
            component: resolve => require(['../pages/other.vue'], resolve)
        },
        {
            path: '*',
            redirect: '/404'
        },
        // 角色
        {
            name: 'grzx',
            path: '/grzx',
            component: resolve => require(['../pages/grzx.vue'], resolve)
        },
        // 个人中心
        {
            name: 'grzx1',
            path: '/grzx1',
            component: resolve => require(['../pages/grzx1.vue'], resolve)
        },
        // 账号管理（非管理员）
        {
            name: 'grzx2',
            path: '/grzx2',
            component: resolve => require(['../pages/grzx2.vue'], resolve)
        },
        // 账号管理（管理员）
        {
            name: 'grzx3',
            path: '/grzx3',
            component: resolve => require(['../pages/grzx3.vue'], resolve)
        },
        // 术语管理
        {
            name: 'sygl',
            path: '/sygl',
            component: resolve => require(['../pages/sygl.vue'], resolve)
        },
        {
            name: 'xz',
            path: '/xz',
            component: resolve => require(['../pages/xz.vue'], resolve)
        },
        {
            name: 'tsc',
            path: '/tsc',
            component: resolve => require(['../pages/tsc.vue'], resolve)
        },
        // 收文界面
        {
            name: 'sfw',
            path: '/sfw',
            component: resolve => require(['../pages/sfw.vue'], resolve)
        },
        //
        {
            name: 'sfw_notadmin',
            path: '/sfw_notadmin',
            component: resolve => require(['../pages/sfw_notadmin.vue'], resolve)
        },
        // 发文界面
        {
            name: 'fw_notadmin',
            path: '/fw_notadmin',
            component: resolve => require(['../pages/fw_notadmin.vue'], resolve)
        },
        // 发文界面
        {
            name: 'fw',
            path: '/fw',
            component: resolve => require(['../pages/fw.vue'], resolve)
        },
        {
            name: 'grzx4',
            path: '/grzx4',
            component: resolve => require(['../pages/grzx4.vue'], resolve)
        },
        // 会议纪要
        {
            name: 'hyjl_sz',
            path: '/hyjl_sz',
            component: resolve => require(['../pages/hyjl_sz.vue'], resolve)
        },
        {
            name: 'hyjl',
            path: '/hyjl',
            component: resolve => require(['../pages/hyjl.vue'], resolve)
        },
        // 范文管理
        {
            name: 'zsk',
            path: '/zsk',
            component: resolve => require(['../pages/zsk.vue'], resolve)
        },
        // mb_manage
        {
            name: 'mb_manage',
            path: '/mb_manage',
            component: resolve => require(['../pages/mb_manage.vue'], resolve)
        },
        //排版管理
        {
            name: 'pb_manage',
            path: '/pb_manage',
            component: resolve => require(['../pages/pb_manage.vue'], resolve)
        },
        // check
        {
            name: 'check',
            path: '/check',
            component: resolve => require(['../pages/check.vue'], resolve)
        },
        {
            name: 'batter',
            path: '/batter',
            component: resolve => require(['../pages/batter.vue'], resolve)
        },
        {
            name: 'test',
            path: '/test',
            component: resolve => require(['../pages/test.vue'], resolve)
        },
        // maxkbup
        {
            name: 'maxkbup',
            path: '/maxkbup',
            component: resolve => require(['../pages/maxkbup.vue'], resolve)
        },
    ],
});

// 在路由守卫中设置 selectedNavItem 的值
router.beforeEach((to, from, next) => {
    let selectedItem = '';
    let selectedtopItem = '';
    // console.log(to.path, '指定路由');
    // console.log(to, '指定路由');
    switch (to.path) {
        case '/index':
            selectedItem = 'szbg';
            selectedtopItem = 'index';
            break;
        case '/xdth':
            selectedItem = 'xdth';
            selectedtopItem = 'index';
            break;
        // 其他情况...
        case '/ldjh':
            selectedItem = 'ldjh';
            selectedtopItem = 'index';
            break;
        case '/gzfa':
            selectedItem = 'gzfa';
            selectedtopItem = 'index';
            break;
        case '/dybg':
            selectedItem = 'dybg';
            selectedtopItem = 'index';
            break;
        case '/xccl':
            selectedItem = 'xccl';
            selectedtopItem = 'index';
            break;
            // 核稿
                case '/check':
            // selectedItem = 'check';
            selectedtopItem = 'check';
            break;
        // batter
        case '/batter':
            selectedItem = 'batter';
            selectedtopItem = 'index';
            break;
        case '/sfw':
            selectedtopItem = 'sfw';
            break;
        // 述职报告（/index）、心得体会（/xdth）等等的selectedtopItem都是index
        // case '/index':
        //         selectedtopItem = 'index';
        //         break;
        // ai读
        case '/tywzsb':
            selectedtopItem = 'tywzsb';
            break;
        case '/fw':
            selectedtopItem = 'fw';
            break;
        case '/lgsc':
            selectedtopItem = 'lgsc';
            break;
        case '/hyjl_sz':
            selectedtopItem = 'hyjl_sz';
            break;
        case '/bmai':
            selectedtopItem = 'bmai';
            break;
        case '/jqkz':
            selectedtopItem = 'jqkz';
            break;


    }
    store.commit('setSelectedNavItem', selectedItem);
    store.commit('setSelectedtopItem', selectedtopItem);
    next();
});

export default router;
