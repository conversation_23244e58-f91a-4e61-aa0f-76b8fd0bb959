<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle"></div>
        <div class="ai-header">
          <div class="ai-bar">
            <HeadNavigation />
            
          </div>
        </div>
      </el-header>
      <el-main>
        <div class="global">
          <div class="chat-container">
            <div class="chat-box" ref="chatBox">
              <el-dropdown @command="handleCommand" class="chat-dropdown">
                <i class="el-icon-more el-icon--right"></i>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="clearHistory"
                    >清除历史记录</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
              <div
                v-for="(message, index) in messages"
                :key="index"
                :class="{
                  message: true,
                  'user-message': message.role === 'user',
                  'ai-message': message.role === 'ai',
                }"
              >
                <div v-if="message.role === 'ai'">
                  <div style="display: flex">
                    <img
                      src="./img/xh.png"
                      alt=""
                      style="width: 70px; height: 70px; margin-right: 10px"
                    />
                    <!-- 助手的回答区域，没有markdown -->
                    <div
                      class="ai-message-content"
                      style="
                        font-size: calc(100vw * 15 / 1920);
                        margin-top: 20px;
                        padding-right: 40px;
                        width: calc(100% - 70px);
                      "
                      v-html="message.content"
                    ></div>
                  </div>
                  <div class="message-buttons" v-if="message.answerReady">
                    <div v-if="message.selectSffs == 'fs'">
                      <el-button type="text" @click="showknowledges(message)"
                        >引用分段</el-button
                      >
                      <div class="image-container">
                        <img
                          src="./img/copy.png"
                          title="复制"
                          @click="ctrlCopy(message)"
                          alt="复制"
                          class="send-button_new"
                        />
                        <img
                          src="./img/regen.png"
                          title="重新生成"
                          @click="regenerateAnswer(message, index)"
                          alt="重新生成"
                          class="send-button_new"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else style="display: flex">
                  <div style="text-align: justify; margin-right: 10px">
                    {{ message.content }}
                  </div>
                  <img src="./img/tx.png" style="width: 20px; height: 20px" />
                </div>
              </div>
            </div>
            <div class="fgx-container"></div>
            <!-- 消息输入区域 -->
            <div
              class="input-container"
              :class="[loading == true ? 'breathing-bar' : '']"
              v-loading="loading"
            >
              <div class="tzdh-container" v-if="loading == true">
                <el-button type="text" @click="stopClick()">停止对话</el-button>
              </div>
              <div style="display: flex">
                <el-input
                  v-model="newMessage"
                  @keyup.enter.native="sendMessage(newMessage)"
                  placeholder="请输入您的问题~"
                  class="message-input"
                  type="textarea"
                />
              </div>
              <!-- 知识库选择框和容器 -->
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-end;
                  margin-top: 5px;
                "
              >
                <div
                  class="select-container"
                  style="display: flex; align-items: center"
                >
                  <div
                    class="sdskwddj"
                    v-if="isKnowledgeBaseVisible == false"
                    @click="zskClick(1, 'zsk')"
                  >
                    <img
                      src="./img/<EMAIL>"
                      title="llm对话"
                      alt="llm对话"
                      class="send-button_new"
                    />
                    <span> 知识库对话 </span>
                  </div>
                  <div
                    class="sdsk"
                    v-if="isKnowledgeBaseVisible == true"
                    @click="zskClick(2, 'zsk')"
                  >
                    <img src="./img/<EMAIL>" class="send-button_new" />
                    <span> 知识库对话 </span>
                  </div>
                  <!-- 知识库选择框 -->
                  <div v-if="isKnowledgeBaseVisible">
                    <el-select
                      v-model="selectValue"
                      clearable
                      placeholder="选择知识库"
                      size="mini"
                      style="width: 120px"
                    >
                      <el-option
                        v-for="item in selectOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                </div>
                <!-- 发送按钮 -->
                <div
                  @click="sendMessage()"
                  class="send-button"
                  v-if="newMessage != ''"
                >
                  <img src="./img/fs.png" alt="" width="20px" height="20px" />
                </div>
                <div class="send-button send-button1" v-if="newMessage == ''">
                  <img src="./img/nofs.png" alt="" width="20px" height="20px" />
                </div>
              </div>
              <div
                class="inputbox__line"
                v-if="newMessage != ''"
                style="animation: auto ease 0s 1 normal none running none"
              ></div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog
      class="custom-dialog"
      title="知识库引用"
      :visible.sync="lydia"
      v-loading="loading"
      element-loading-text="请稍候..."
      element-loading-background="rgba(255, 255, 255, 0.7)"
    >
      <div
        v-for="(item, index) in responseArray"
        :key="index"
        class="gradient-border"
      >
        <div
          style="
            width: calc(100% - 40px); /* 100%减去左右各20px的margin */
            max-width: 700px;
            margin: 20px auto;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            font-family: Arial, sans-serif;
            overflow: hidden;
          "
        >
          <!-- 头部栏 -->
          <div
            style="
              background-color: #f5f5f5;
              padding: 10px 15px;
              border-bottom: 1px solid #e0e0e0;
              font-weight: bold;
              display: flex;
              justify-content: space-between;
            "
          >
            <!--  -->
            <!-- <span style="color: #3370ff; cursor: pointer;"
              >文件名： {{ item.document_name }}</span
            > -->
            <span style="color: #3370ff; cursor: pointer"
              >{{ index + 1 }}. 文件名： {{ item.document_name }}</span
            >
            <!-- 相似度区域 -->
            <span>相似度：{{ formattedSimilarity(item.similarity) }}</span>
          </div>
          <div class="result-content">
            {{ item.content }}
          </div>
          <div
            style="
              padding: 10px 15px;
              border-bottom: 1px solid #e0e0e0;
              font-weight: bold;
              display: flex;
              justify-content: space-between;
            "
          >
            <!-- <span style="color: #3370ff; cursor: pointer;margin-left: auto;"
              >文件名： {{ item.document_name }}</span
            > -->
            <!-- <span>知识库名： {{ item.dataset_name }}</span> -->
          </div>
        </div>
        <!-- 这里可以根据item的内容填充div -->
        <!-- {{ item }} -->
      </div>
    </el-dialog>
  </div>
</template>
<script>
import HeadNavigation from "../components/HeadNavigation.vue"; // 引入头部导航组件

import pdf from "vue-pdf";
import { VueEditor } from "vue2-editor";
import {
  getOcr,
  testcheck,
  getOcrtxt,
  summary_string,
  llmChat,
  getKbList,
  kbChat,
  delete_his,
  getChatMsg,
} from "../api/home.js"; // 接口请求
import { mapState, mapActions } from "vuex";
import store from "../store/index";
// import { md } from "./js/markdown.js";
// import axios from "axios";
// import { Diff } from "diff";
// import { ElMessage, ROOT_PICKER_INJECTION_KEY } from "element-plus";
export default {
  data() {
    return {
      applyId: "",
      chatId: "",
      messageId: "",
      responseArray: [],
      // paragraph_list
      chatResponses: [], // 存储所有对话响应对象的数组，清除历史记录时清空，刷新时清空，或者用vuex存储在每次登录成功后清除，还有对话的历史记录也应该用vuex存储
      chatBox: null,
      text1: "",
      // lydia: true,
      lydia: false,
      con_loading: false,
      jiaoyanjc: false,
      jydis: false,
      activeNames: [],
      correctionList: [],
      navShow: true,
      fileList: [],
      file: "",
      filename: "",
      input: "",
      imgURl: "",
      imgURlss: "",
      imgURlcw: "",
      loading: false,
      text: "",
      imgListRes: [],
      imgpd: "",
      type: "",
      filePdfUrl: "",
      filePdfUrlcw: "",
      reverse: true,
      numPages: 0, // 初始化页数
      thirdChunkContent: "", // 用于存储第三个数据块的内容
      thirdChunkContent_2: "", // 用于存储第三个数据块的内容
      zsk_name: [],
      zsk_name1: [],
      zsk_name2: [],
      kbname: "",
      pblx: "",
      messages: [], // 定义消息列表，存储用户和 AI 的消息
      newMessage: "", // 定义输入框的内容
      selectValue: "", // 定义选择框的内容
      contentInner: "", // 定义输入框的内容
      controller: null, // 用于存储 AbortController 实例
      isKnowledgeBaseVisible: false, // 定义是否显示知识库选择框
      isKnowledgeBaseVisible1: true, // 定义是否显示知识库选择框
      lsVisible: false, // 定义是否显示轮数计数器
      showLogin: false, // 定义是否显示登录界面
      roundCount: 0,
      centerDialogVisible: false, // 定义弹窗是否可见
      button: "",
      firstData: "",

      isPasswordVisible: false,
      selectSffs: "",
      selectSffsValue: "",
      selectOptions: [],
      shuju: "",
      num: 0,
      activities: [
        {
          content: "活动按期开始",
          timestamp: "2018-04-15",
        },
        {
          content: "通过审核",
          timestamp: "2018-04-13",
        },
        {
          content: "通过审核",
          timestamp: "2018-04-13",
        },
        {
          content: "创建成功",
          timestamp: "2018-04-11",
        },
      ],
    };
  },
  watch: {
    messages: {
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      },
      deep: true,
    },
    isKnowledgeBaseVisible(newVal) {
      if (!newVal) {
        this.selectValue = "";
      }
    },
  },
  components: {
    VueEditor,
    pdf,
    HeadNavigation,
  },
  mounted() {
    this.fetchKbList();
  },
  computed: {
    formattedSimilarity() {
      return (similarity) => {
        return parseFloat(similarity).toFixed(2);
      };
    },
    ...mapState(["id"]), // 映射 username 和 id
    id() {
      return this.$store.state.id;
    },
    ...mapState(["imgList"]),
    username() {
      return this.$store.state.username;
    },
    lastAiMessage() {
      return this.messages.filter((message) => message.role === "ai").pop();
    },
  },
  methods: {
    // async ctrlCopy() {
    //   try {
    //     // 找到最后一条AI消息
    //     const lastAiMessage = this.messages
    //       .filter((message) => message.role === "ai")
    //       .pop();
    //     console.log(lastAiMessage, "复制");
    //     if (lastAiMessage) {
    //       let contentToCopy = lastAiMessage.content;
    //       console.log(contentToCopy, "复制1");
    //       // 使用正则表达式删除 <think> 标签及其内容
    //       const thinkRegex = /<think>[\s\S]*?<\/think>/gi;
    //       contentToCopy = contentToCopy.replace(thinkRegex, ""); // 使用正则表达式删除其他 HTML 标签

    //       const htmlRegex = /<\/?[^>]+(>|$)/g;
    //       contentToCopy = contentToCopy.replace(htmlRegex, ""); // 使用正则表达式删除多余的空白字符

    //       contentToCopy = contentToCopy.trim();

    //       if (contentToCopy) {
    //         await navigator.clipboard.writeText(contentToCopy);

    //         this.$message.success("内容已复制到剪贴板");
    //         // 注意：window.Application.ActiveDocument.TrackRevisions = false 这行代码可能与Vue无关，确保它在Vue环境中有意义
    //       } else {
    //         this.$message.warning("没有可复制的非 <think> 标签内容");
    //       }
    //     } else {
    //       this.$message.warning("没有可复制的AI消息");
    //     }
    //   } catch (error) {
    //     this.$message.error("复制失败: " + error);
    //   }
    // },
    async ctrlCopy() {
      try {
        // 找到最后一条AI消息
        const lastAiMessage = this.messages
          .filter((message) => message.role === "ai")
          .pop();
        console.log(lastAiMessage, "复制");
        if (lastAiMessage) {
          let contentToCopy = lastAiMessage.rawContent; // 使用 rawContent 获取原始 Markdown 内容
          console.log(contentToCopy, "复制1");

          // 使用正则表达式删除 <think> 标签及其内容
          const thinkRegex = /<think>[\s\S]*?<\/think>/gi;
          contentToCopy = contentToCopy.replace(thinkRegex, "");

          // 如果需要进一步处理 Markdown 内容，可以在这里添加逻辑
          // 例如，删除不必要的空行或特定的标记等

          contentToCopy = contentToCopy.trim();

          if (contentToCopy) {
            if (navigator.clipboard && navigator.clipboard.writeText) {
              await navigator.clipboard.writeText(contentToCopy);
            } else {
              // 如果 clipboard API 不可用，使用旧版方法
              const textArea = document.createElement("textarea");
              textArea.value = contentToCopy;
              document.body.appendChild(textArea);
              textArea.select();
              document.execCommand("copy");
              document.body.removeChild(textArea);
            }

            this.$message.success("内容已复制到剪贴板");
          } else {
            this.$message.warning("没有可复制的非 <think> 标签内容");
          }
        } else {
          this.$message.warning("没有可复制的AI消息");
        }
      } catch (error) {
        this.$message.error("复制失败: " + error);
      }
    },

    async fetchKbList() {
      let formData = new FormData(); // 确保 formData 在使用前被定义
      formData.append("userId", this.id);
      try {
        const response = await getKbList(formData);
        const data1 = await response.json();
        const data = data1.data;

        console.log(data, "知识库列表");

        if (Array.isArray(data) && data.length > 0) {
          // 将知识库列表数据转换为下拉框需要的格式
          const knowledgeBaseList = data.map((item) => ({
            value: item, // 确保这里使用的是知识库的唯一标识 id
            // label: item.kb_name // 确保这里使用的是知识库的名称 kb_name
          }));

          console.log(knowledgeBaseList);
          this.selectOptions = knowledgeBaseList; // 更新下拉框选项
        }
      } catch (error) {
        console.error("获取知识库列表失败:", error);
      }
    },
    regenerateAnswer(message, index) {
      // 找到对应的用户问题消息
      console.log(this.messages, "重新生成message");
      console.log(index, "重新生成index");

      const userMessageIndex = this.messages.findIndex(
        (msg, idx) =>
          msg.role === "user" &&
          msg.value == this.selectSffsValue &&
          idx < this.messages.indexOf(message)
      );

      if (userMessageIndex !== -1) {
        const userMessage = this.messages[index - 1];
        console.log("userMessage content:", userMessage.content); // 调试信息

        // 根据 selectSffsValue 的值调用不同的方法
        if (this.selectSffsValue === "xd") {
          // 修订
          this.contentInner = userMessage.content.replace("请修订: ", "");
          let checkTimes = 0;
          console.log(checkTimes++, "checkTimes333");
          this.check(this.contentInner);
        } else if (this.selectSffsValue === "wenzhangxuxie") {
          // 续写
          this.contentInner = userMessage.content.replace("请续写: ", "");
          this.checkXuxie(this.contentInner);
        } else if (this.selectSffsValue === "fs") {
          // 发送消息
          this.newMessage = userMessage.content.replace("你: ", "");
          this.sendMessage(this.newMessage);
        }
      } else {
        this.$message.warning("未找到对应的用户问题，无法重新生成答案"); // 使用 Vue2 的 $message
      }
    },
    clickpb() {
      this.$router.push("/pb_manage");
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    toxg() {
      this.$router.push("/grzx4");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    ...mapActions(["addItemAction"]),
    clickTopAi() {
      this.$router.push("/index");
    },
    clickTopLg() {
      this.$router.push("/lgsc");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopsfw() {
      this.$router.push("/sfw");
    },
    togglePasswordVisibility() {
      this.isPasswordVisible = !this.isPasswordVisible;
    },
    handleCommand(command) {
      if (command === "clearHistory") {
        this.deletehis();
        this.messages = [];
      }
    },
    async deletehis() {
      let formData = new FormData();
      formData.append("userId", this.id);

      const response = await delete_his(formData);
      const data = await response.json();
      if (data.status_code === 200) {
        this.$message.success("历史记录已清除");
      } else {
        this.$message.error("清除历史记录失败");
      }
    },

    marked(markdown) {
      if (!markdown) return "";

      // 先处理代码块
      const codeBlockRegex = /```([\s\S]*?)```/g;
      markdown = markdown.replace(codeBlockRegex, (match, code) => {
        return `<pre><code>${code}</code></pre>`;
      });

      // 然后处理其他 Markdown 语法
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;

      lines.forEach((line) => {
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          if (level > 0 && level <= 6) {
            const content = line.slice(level + 1);
            html += `<h${level}>${content}</h${level}>`;
            return;
          }
        }

        // 处理无序列表
        if (/^[-*+]\s/.test(line)) {
          if (!inList) {
            html += "<ul>";
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
          return;
        }

        // 处理有序列表
        if (/^\d+\.\s/.test(line)) {
          if (!inList) {
            html += "<ol>";
            inList = true;
          }
          const content = line.slice(line.indexOf(" ") + 1);
          html += `<li>${content}</li>`;
          return;
        }

        // 处理空行
        if (line.trim() === "") {
          if (inList) {
            html += inList ? "</ul>" : "</ol>";
            inList = false;
          }
          return;
        }

        // 处理普通段落
        if (inList) {
          html += inList ? "</ul>" : "</ol>";
          inList = false;
        }

        // 处理行内格式
        let processedLine = line
          .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>") // 粗体
          .replace(/\*(.*?)\*/g, "<em>$1</em>") // 斜体
          .replace(/`(.*?)`/g, "<code>$1</code>") // 行内代码
          .replace(/!\[(.*?)\]\((.*?)\)/g, '<img alt="$1" src="$2">') // 图片
          .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>'); // 链接

        html += `<p>${processedLine}</p>`;
      });

      // 关闭未结束的列表
      if (inList) {
        html += inList ? "</ul>" : "</ol>";
      }

      return html;
    },
    //   根据消息ID获取完整的响应对象
    getResponseById(id) {
      // alert('进来了')
      console.log(this.chatResponses, "获取完整的响应对象");
      console.log(id, "获取完整的响应对象id");
      // return this.chatResponses.find(item => item.id === id);
      //  id 的对象
      return this.chatResponses.find((item) => item.id === id);
    },
    // 显示知识来源
    showknowledges(message) {
      console.log(message.id, "显示");
      const response = this.getResponseById(message.id);

      console.log(response, "显示1");
      if (response.paragraph_list.length > 0) {
        this.responseArray = response.paragraph_list;
        this.lydia = true;
      } else {
        this.$message.warning("该回答没有关联知识库");
      }
    },

    async sendMessage(value1) {
      this.loading = true;
      this.selectSffsValue = "fs";
      this.controller = new AbortController();
      const signal = this.controller.signal;

      let formData = new FormData();
      formData.append("userId", this.id);

      if (this.newMessage.trim() !== "") {
        // 添加用户消息
        this.$set(this.messages, this.messages.length, {
          role: "user",
          content: this.newMessage,
          selectSffs: "fs",
          value: "fs",
        });

        // 准备请求数据
        formData.append("text", value1 || this.newMessage);

        try {
          let response;
          if (this.selectValue) {
            formData.append("knowledge_base_name", this.selectValue);
            response = await kbChat(formData, signal);
            // 调用知识库对话接口的时候调用接口查回出处
            // this.showkb=true;
            // 将完整响应对象存入chatResponses数组
          } else {
            response = await llmChat(formData, signal);
          }

          this.newMessage = "";

          // 添加 AI 的初始消息
          const aiMessage = {
            role: "ai",
            content: "思考中...",
            answerReady: false,
            selectSffs: "fs", // 确保设置了这个属性
            rawContent: "",
            hasShownThink: false,
            id: "",
          };

          this.$set(this.messages, this.messages.length, aiMessage);
          const aiMessageIndex = this.messages.length - 1;

          const reader = response.body.getReader();
          const decoder = new TextDecoder("utf-8");
          let buffer = "";
          let result = "";
          let thinkHtml = "思考中...";
          let skipCount = 0; // 添加一个计数器来追踪已经处理的数据块数量

          const processStream = async () => {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = decoder.decode(value, { stream: true });
              buffer += chunk;

              const lines = buffer.split("\n");
              buffer = lines.pop() || "";

              lines.forEach((line) => {
                if (line.startsWith("data: ")) {
                  try {
                    const rawData = line.slice(6).trim();
                    const content = JSON.parse(rawData);

                    if (skipCount < 3) {
                      // 如果skipCount小于3，增加计数并保存当前内容块到相应的字段
                      skipCount++;
                      switch (skipCount) {
                        case 1:
                          this.applyId = content;
                          // this.messages[aiMessageIndex].applyId = content;
                          break;
                        case 2:
                          this.chatId = content;
                          // this.messages[aiMessageIndex].chatId = content;
                          break;
                        case 3:
                          this.messageId = content;
                          this.messages[aiMessageIndex].id = this.messageId;
                          break;
                      }
                      return;
                    }

                    result += content;
                    this.messages[aiMessageIndex].rawContent = result;

                    let position = result.indexOf("</think>");
                    let displayContent = result;

                    if (position !== -1) {
                      const thinkContent = result.substring(0, position + 8);
                      displayContent = result.substring(position + 8);

                      if (!this.messages[aiMessageIndex].hasShownThink) {
                        thinkHtml = `\
                      <span style="color: #ccc;">${thinkContent.replace(
                        /\n/g,
                        "<br>"
                      )}</span>\
                    `;
                        this.messages[aiMessageIndex].hasShownThink = true;
                      }
                    }

                    const answerHtml = this.marked(displayContent);

                    this.$set(this.messages, aiMessageIndex, {
                      ...this.messages[aiMessageIndex],
                      content: thinkHtml + answerHtml,
                      selectSffs: "fs", // 确保保留这个属性
                    });
                  } catch (error) {
                    console.error("解析流数据错误:", error);
                  }
                }
              });

              this.$forceUpdate();
              this.scrollToBottom();
            }

            // 流处理完成后
            this.$set(this.messages, aiMessageIndex, {
              ...this.messages[aiMessageIndex],
              answerReady: true, // 确保设置这个属性以显示按钮
              selectSffs: "fs", // 确保设置这个属性以显示按钮
            });
          };

          await processStream();

          // 如果你需要在其他地方使用这些字段，可以在这里使用
          console.log("applyId:", this.applyId);
          console.log("chatId:", this.chatId);
          console.log("messageId:", this.messageId);
          this.ChatMsg1(this.applyId, this.chatId, this.messageId);
        } catch (error) {
          if (error.name === "AbortError") {
            console.log("请求已中止");
          } else {
            console.error("请求出错:", error);
            const errorIndex = this.messages.findIndex(
              (msg) => msg.role === "ai" && !msg.answerReady
            );
            if (errorIndex !== -1) {
              this.$set(this.messages, errorIndex, {
                ...this.messages[errorIndex],
                content: "AI回答时出现错误",
                answerReady: true,
                selectSffs: "fs",
              });
            }
          }
        } finally {
          this.loading = false;
          this.scrollToBottom();
          this.controller = null;
        }
      }
    },

    async scrollToBottom() {
      if (this.$refs.chatBox) {
        this.$refs.chatBox.scrollTop = this.$refs.chatBox.scrollHeight;
      } else {
        // console.error('chatBox is null or undefined');
      }
    },

    async zskClick(val, lx) {
      if (lx === "zsk") {
        if (val === 1) {
          this.selectValue = "";
          this.isKnowledgeBaseVisible = true;
          this.isKnowledgeBaseVisible1 = false;
        } else {
          this.selectValue = "";
          this.isKnowledgeBaseVisible = false;
          this.isKnowledgeBaseVisible1 = true;
        }
      } else if (lx === "llm") {
        if (val === 1) {
          this.selectValue = "";
          this.isKnowledgeBaseVisible1 = true;
          this.isKnowledgeBaseVisible = false;
        } else {
          this.selectValue = "";
          this.isKnowledgeBaseVisible1 = false;
          this.isKnowledgeBaseVisible = true;
        }
      }
    },
    //
    async ChatMsg1(c1, c2, c3) {
      let formData = new FormData();
      formData.append("userId", this.id);
      formData.append("applyId", c1);
      formData.append("chatId", c2);
      formData.append("messageId", c3);
      try {
        let response = await getChatMsg(formData);
        let res = await response.json();
        if (res.code === 200) {
          console.log(res.data);
          this.chatResponses.push(res.data);
          // res.data.id
          console.log(this.chatResponses[0], "是啥？？？？？？");
          // chatResponses: [], // 存储所有对话响应对象的数组，清除历史记录时清空，刷新时清空，或者用vuex存储在每次登录成功后清除，还有对话的历史记录也应该用vuex存储
        }
      } catch (error) {
        console.error("请求出错:", error);
      }
    },

    stopClick() {
      if (this.controller) {
        this.controller.abort(); // 中止请求
        this.loading = false;

        // 找到最后一条AI消息
        const lastAiMessageIndex = this.messages.findIndex(
          (message) => message.role === "ai" && message.answerReady
        );
        if (lastAiMessageIndex !== -1) {
          this.messages[lastAiMessageIndex].answerReady = true;

          if (
            this.selectSffsValue === "xd" ||
            this.selectSffsValue === "kx" ||
            this.selectSffsValue === "sx" ||
            this.selectSffsValue === "rs"
          ) {
            this.messages[lastAiMessageIndex].selectSffs = "xd";
          } else if (this.selectSffsValue === "wenzhangxuxie") {
            this.messages[lastAiMessageIndex].selectSffs = "wenzhangxuxie";
          } else if (
            this.selectSffsValue === "fs" ||
            this.selectSffsValue === "zj"
          ) {
            console.log(
              "Last AI message1111111111111:",
              this.messages[lastAiMessageIndex]
            ); // 调试信息
            this.messages[lastAiMessageIndex].selectSffs = "fs";
          } else if (this.selectSffsValue === "wafs") {
            this.messages[lastAiMessageIndex].selectSffs = "wafs";
          }
          // 上传词库
          else if (this.selectSffsValue === "upsy") {
            this.messages[lastAiMessageIndex].selectSffs = "upsy";
          }
          // 上传文件
          else if (this.selectSffsValue === "scwj") {
            this.messages[lastAiMessageIndex].selectSffs = "scwj";
          }
          console.log("Last AI message:", this.messages[lastAiMessageIndex]); // 调试信息
        }
        this.$message.success("对话已停止"); // 使用 ElMessage 的 Vue 2 版本
      }
    },
  },
};
</script>
<style lang="less" scoped>
.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 120px;
    height: 48px;
    position: absolute;
  }

  p {
    border: 1.54px solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vh * 40 / 1080);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: 1px solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vh * 130 / 1080);
            color: #000;
            font-size: calc(100vh * 14 / 1080);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vh * 14 / 1080);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vh * 16 / 1080);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vh * 14 / 1080);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}
.el-main {
  // background-color: #f8f9fd;
  // background: linear-gradient(180deg, #edeef1, #c4cce9);
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: 20px;
  // overflow: hidden;
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw * 14 / 1920);
  letter-spacing: 0;
  line-height: 16px;
  overflow: hidden;
  height: 20px;
  .fir-kuai {
    width: 6px;
    height: 16px;
    margin-right: 8px;
    float: left;
    // margin-top: 2px;
    background: #4081ff;
    border-radius: 1.5px;
  }
  .fir-title-p {
    color: #222;
    font-weight: 500;
    font-size: calc(100vw * 14 / 1920);
    letter-spacing: 0;
    line-height: 16px;
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}
.xxjs {
  width: 902px;
  height: calc(100vh * 40 / 1080);
  word-wrap: break-word;
  text-align: left;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #999999;
  letter-spacing: 0;
  // font-weight: 600;
  margin-top: 12px;
}
.flexLd {
  display: flex;
  justify-content: space-between;
  height: auto;
}
.btnLeft {
  width: 332px;
  height: auto;
  .el-button {
    width: 104px;
    height: 30px;
    line-height: 15px;
    padding: 5px;
    font-size: calc(100vw * 14 / 1920);
    margin-left: 0;
    margin-right: 9px;
    margin-top: 10px;
    background: #f5f8fc;
    border: 1px solid rgba(31, 82, 176, 1);
    border-radius: 4px;
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw * 14 / 1920);
    color: #1f52b0;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;

    span {
      font-family: SourceHanSansSC-Medium;
      font-size: calc(100vw * 16 / 1920);
      // color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }
  }
}

::v-deep(.el-button) {
  span {
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw * 16 / 1920);
    // color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
}

.con {
  width: 100%;
  height: auto;
  margin-top: 20px;
  background: #ffffff;
  padding: 20px;

  .leftTp {
    width: 1030px;
    height: auto;

    .tpBG {
      width: 100%;
      height: 555px;
      margin-bottom: 20px;

      .zxTp {
        width: 162px;
        height: 100%;
        overflow-y: scroll;

        .xTpk {
          width: 100%;
          height: 136px;
          background: #dcdfe5;
          margin-bottom: 8px;

          &:hover {
            border: 1.35px solid rgba(26, 102, 255, 1);
          }

          .tpbj {
            width: 100%;
            height: 110px;
            background-color: #ffffff;

            img {
              width: 140px;
              height: 110px;
              object-fit: contain;
            }
          }

          &:active {
            border: 1.35px solid rgba(26, 102, 255, 1);
            width: 100%;
            height: 136px;
            background: #dcdfe5;
            margin-bottom: 8px;

            .tpbj {
              width: 140px;
              height: 110px;
              background-color: #ffffff;

              img {
                width: 140px;
                height: 110px;
              }
            }
          }
        }

        .activeImg {
          border: 1.35px solid rgba(26, 102, 255, 1);
          width: 100%;
          height: 136px;
          background: #dcdfe5;
          margin-bottom: 8px;

          .tpbj {
            width: 140px;
            height: 110px;
            background-color: #ffffff;

            img {
              width: 140px;
              height: 110px;
            }
          }
        }
      }

      .dxTp {
        width: 848px;
        height: 100%;
        background: #dcdfe5;

        .tpbj {
          width: 100%;
          height: 100%;
          background-color: #ffffff;

          img {
            // width: 350px;
            height: 450px;
            object-fit: contain;
          }
        }
      }

      .zwTp {
        background: url(../img/bga.png) no-repeat center;
        width: 88%;
        height: 100%;
        background-size: 50%;
      }
    }

    .tpUpload {
      width: 70%;
      height: 82px;
      padding-left: 18px;
      align-items: center;

      .upBtn {
        // width: 100%;
        height: calc(100vh * 40 / 1080);

        ::v-deep(.el-button) {
          background: #1a66ff;
          border: 1px solid rgba(31, 82, 176, 1);
          border-radius: 4px;
          width: calc(100vw * 160 / 1920);
          height: calc(100vh * 40 / 1080);
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw * 16 / 1920);
          // color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }

        .sizeColor {
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
          margin: 10px;
        }

        ::v-deep(.el-input__inner) {
          background: #ffffff;
          border: 1px solid rgba(186, 203, 228, 1);
          border-radius: 4px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw * 14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
        }
      }

      .zcSize {
        font-family: PingFangSC-Semibold;
        font-size: calc(100vw * 14 / 1920);
        color: #999999;
        letter-spacing: 0;
        // font-weight: 600;
        height: auto;
        text-align: left;
        // margin-top: 18px;
      }
    }
  }

  .rightJson {
    width: 781px;
    height: 700px;
    background: #f2f4f9;
    padding: 20px;
    position: relative;
    .fir-textarea {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #f8f9fd;
      border: 1px solid rgba(229, 232, 245, 1);
      border-radius: 4px;
      margin-top: 20px;
      margin-bottom: 0;
      height: 158px;

      ::v-deep(.el-textarea__inner) {
        font-size: 14px !important;
        background-color: #f8f9fd !important;
        height: 100% !important;
        font-family: PingFangSC-Regular;
        padding: 13px 18px 33px 16px;
      }
    }

    .fir-textarea-max {
      height: 41% !important;
    }

    .jsTitle {
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 16 / 1920);
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
      height: auto;
      text-align: left;
      margin-bottom: 5px;
    }

    .jsbor {
      border: 1px solid rgba(150, 171, 214, 1);
      height: auto;
      margin-bottom: 14px;
    }

    .jsConNr {
      width: 100%;
      height: calc(100% - 84px);
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 16 / 1920);
      color: #333333;
      letter-spacing: 0;
      line-height: 35px;
      font-weight: 400;
      overflow-y: scroll;
      text-align: left;
    }
  }
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: 10px;
  height: 10px;
}

::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
  display: none !important;
}

::v-deep(.ql-blank) {
  display: none !important;
}

::v-deep(.ql-container.ql-snow) {
  border: 0;
}

.bmaiContainer {
  height: 100%;
  width: 100%;
}

.bmaiAiIframe {
  height: 100%;
  width: 100%;
}

.user-menu-content {
  width: calc(100vw * 246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;
    // font-size: calc(100vw *  15 / 1920);

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw * 16 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw * 12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.clbutton1 {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}

.clbutton3 {
  // height: ;
  // position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  float: right;
  margin: 10px 10px 0 0;
  // top: 300px;
  // left: 160px;
}

.clbutton2 {
  left: 39%;
}
.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 48px;

  .elcol-title-text {
    // float: left;
    padding-left: 10px;
    text-align: left;
    width: 40%;
    height: 100%;
  }

  .elcol-title-text2 {
    font-size: calc(100vw * 14 / 1920);
    color: #303133;
    // float: left;
    width: 75px;
  }

  .elcol-input {
    float: left;
    width: calc(60% - 75px);
    border: none !important;
  }
}

.fir-timeline {
  width: 100%;
  height: 158px;
  margin-top: 20px;
  margin-bottom: 0;
  overflow-y: scroll;
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: 20px;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

::v-deep(.el-textarea__inner) {
  padding: 16px;
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: 20px;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

::v-deep(.el-collapse) {
  border: 0px solid #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: 1px solid #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40 / 1080);
  border-radius: 4px;
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: 4px;
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: 4px;
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: -10px;
    top: 2px;
    width: 4px;
    height: 15px;
    background: #5585f0;
    border-radius: 2px;
  }
}

.elcol-title-left {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 20px;
  margin-left: 10px;
}

.highlight {
  background-color: yellow !important;
  cursor: pointer;
}
//
.global {
  font-size: calc(100vw * 15 / 1920);
  min-height: 80%;
}
.chat-container {
  font-family: "Microsoft YaHei", sans-serif; /* 添加字体 */
  width: 100%;
  background-color: transparent !important;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 85vh;
  width: 55%;
  margin-left: calc(50% - 27.5%);
  text-align: left; /* 文字左对齐 */
  line-height: 1.8; /* 增加行间距 */
  padding: 10px; /* 增加内边距 */
}
.chat-box {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  max-height: 725px;
  scroll-behavior: smooth;
  // box-shadow: 0 5px 20px #edeef1, 0 6px 6px #c4cce9;
}
.message {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  width: fit-content;
  // max-width: 80%;
}

.user-message {
  // width: 85%;
  max-width: 85%;
  color: #fff;
  margin-left: auto;
  border-radius: 14px;
  background: linear-gradient(90deg, #4d71f5, #2a4fec);
}

.ai-message {
  width: 85%;
  border-radius: 14px;
  margin-right: auto;
  background-color: #fff;
}

.input-container {
  width: 97%;
  height: 120px;
  margin: 0 auto;
  background-color: #fff;
  // background-color: rgb(245, 245, 245);
  margin-bottom: 5px;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.message-input {
  width: 100%;
  height: 60px;
  text-align: left;
  padding: 10px;
  border: none;
  // border-radius: 4px;
  margin-right: 10px;
}

.send-button {
  float: right;
  padding: 10px;
  /* background-color: #007bff; */
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 20px;
  margin-top: 3px;
}

.select-container {
  float: left;
  margin-top: 5px;
  margin-left: 10px;
  height: 32px;
}

.send-button_new {
  width: 25px;
  height: 25px;
  padding: 5px 5px;
  /* background-color: #007bff; */
  color: #fff;
  border: none;
  /* border-radius: 4px; */
  cursor: pointer;
}

.cancel_button {
  padding: 5px 5px;
  background-color: #f70f0f;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 5px;
}

.avatar {
  width: 40px;
  /* 设置头像的宽度 */
  height: calc(100vh * 40 / 1080);
  /* 设置头像的高度 */
  border-radius: 50%;
  /* 使头像成为圆形 */
  margin-right: 10px;
  /* 设置头像与消息内容之间的间距 */
  vertical-align: middle;
  /* 垂直居中对齐 */
}
::v-deep(.el-loading-mask) {
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.7);
}

::v-deep(.el-loading-spinner) {
  display: none;
}

.tzdh-container {
  text-align: right;
  height: 30px;
  line-height: 30px;
  position: fixed;
  z-index: 9999;
  right: 500px;
}

.fgx-container {
  height: 5px;
  margin-bottom: 10px;
}

@keyframes underlineAnimation {
  from {
    width: 0;
    left: 50%;
  }

  to {
    width: 100%;
    left: 0;
  }
}

.inputbox__line {
  height: 3px;
  width: 100%;
  position: absolute;
  /* background: linear-gradient(90deg, #ffc590 2.48%, #fd82d6 25.22%, #b89cf8 68.77%, #66c0ff 95.38%); */
  background: linear-gradient(
    90deg,
    #87ceeb 2.48%,
    #1e90ff 25.22%,
    #00bfff 68.77%,
    #0000ff 95.38%
  );
  bottom: 0;
  left: 0;
  animation: underlineAnimation 0.3s forwards;
  /* 使用定义的动画 */
}

.message-buttons {
  margin-top: 25px;
  margin-left: 78px;
}

.ai-message-content p {
  margin-top: 20px;
}

.ai-message-content table {
  border: 1px solid #ddd !important;
  /* 边框 */
}

table,
th,
td {
  border: 1px solid black;
  /* 边框样式 */
}

th,
td {
  padding: 8px;
  /* 单元格内边距 */
  text-align: left;
  /* 文本对齐方式 */
}

::v-deep(.el-select__placeholder) {
  font-size: calc(100vw * 12 / 1920);
}
.breathing-bar {
  background: linear-gradient(
    90deg,
    #87ceeb 2.48%,
    #1e90ff 25.22%,
    #00bfff 68.77%,
    #0000ff 95.38%
  );

  background-size: 200% 100%; /* 背景大小，用于动画 */
  animation: breathe 3s infinite ease-in-out; /* 动画 */
}

@keyframes breathe {
  0% {
    background-position: 100% 0; /* 初始背景位置 */
    opacity: 0.5; /* 初始透明度 */
  }
  50% {
    background-position: 0% 0; /* 中间背景位置 */
    opacity: 1; /* 最大透明度 */
  }
  100% {
    background-position: 100% 0; /* 回到初始背景位置 */
    opacity: 0.5; /* 回到初始透明度 */
  }
}
.switch-and-counter {
  margin-top: 2px;
  margin-left: 12px;
  display: flex;
  align-items: center;
}
.sdskwddj {
  width: 100%;
  height: 28px;
  background: #ffffff;
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-family: SourceHanSansSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #666666;
  letter-spacing: 0;
  font-weight: 400;
  padding: 0 5px;
}
.sdsk {
  margin-right: 10px;
  width: 100%;
  height: 28px;
  background: rgba(69, 105, 255, 0.1);
  border: 1px solid rgba(69, 105, 255, 1);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-family: SourceHanSansSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #4569ff;
  letter-spacing: 0;
  font-weight: 400;
  padding: 0 5px;
}

.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  background-image: url("./img/bg-img_2.png");
  background-repeat: no-repeat;
  background-size: cover;
  /* margin-top: -5px; */
}

.title {
  margin-top: -60px;
  color: #333;
  margin-bottom: 3rem;
  font-size: calc(100vw * 26 / 1920);
}

.input-group {
  margin-bottom: 1.5rem;
  width: 300px;
}

.input-field:focus {
  outline: none;
  border-color: #1f3d7a;
}

.login-btn {
  width: 350px;
  padding: 12px;
  background-color: #2b50eb;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: calc(100vw * 15 / 1920);
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 2rem;
  margin-left: 3rem;
}

.login-btn:hover {
  background-color: #1f3d7a;
}
.input-group {
  position: relative;
  display: inline-block;
  border: none;
}

.input-icon {
  position: absolute;
  left: 5px;
  top: 50%;
  transform: translateY(-50%);
  height: calc(100% - 15px);
  pointer-events: none; /* 确保图片不会干扰输入框的点击事件 */
}

.input-field {
  border: none;
  padding-left: 40px;
  width: 350px;
  height: calc(100vh * 40 / 1080);
}
/* 新增眼睛图标样式 */
.password-eye {
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #666;
  font-size: calc(100vw * 18 / 1920);
}

.input-field {
  padding-left: 40px;
  /* padding-right: 35px;  */
  width: 350px;
  height: calc(100vh * 40 / 1080);
}
.message-input {
  ::v-deep .el-textarea__inner {
    background-color: transparent;
    box-shadow: none;
    border: none;
  }
}
.chat-dropdown {
  position: absolute;
  top: 8px;
  // right: 20px;
  right: 60px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  &:hover {
    background: rgba(255, 255, 255, 1);
  }
}
.el-icon--right {
  margin-left: 0px;
}
.ai-message-content {
  /* Markdown 基本样式 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 0.5em 0;
    font-weight: bold;
  }

  h1 {
    font-size: 1.8em;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.3em;
  }
  h2 {
    font-size: 1.5em;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.3em;
  }
  h3 {
    font-size: 1.3em;
  }

  p {
    margin: 0.5em 0;
    line-height: 1.6;
  }

  ul,
  ol {
    padding-left: 2em;
    margin: 0.5em 0;
  }

  li {
    margin: 0.25em 0;
  }

  pre {
    background: #f5f5f5;
    padding: 1em;
    border-radius: 4px;
    overflow-x: auto;
  }

  code {
    font-family: monospace;
    background: #f5f5f5;
    padding: 0.2em 0.4em;
    border-radius: 3px;
  }

  a {
    color: #0066cc;
    text-decoration: underline;
  }

  blockquote {
    border-left: 3px solid #ddd;
    padding-left: 1em;
    margin-left: 0;
    color: #666;
  }

  img {
    max-width: 100%;
  }
}
.result-content {
  padding: 15px;
  height: calc(100vh - 700px); /* 视口高度减去一些固定高度 */
  overflow: scroll;
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: 6px;
  /* 滚动条的宽度 */
}

.result-content::-webkit-scrollbar-track {
  background: #f5f5f5; /* 浅灰色 */
  /* 滚动条的背景色 */
}

.result-content::-webkit-scrollbar-thumb {
  background: #616161; /* 深灰色 */
  /* 滚动条的滑块颜色 */
}


::v-deep .custom-dialog .el-dialog__body {
  // padding-top: calc(100vh * 120 / 960); /* 调整这个值以达到你想要的移动距离 */
  height: 600px;
  overflow: scroll;
}
::v-deep .custom-dialog .el-dialog__body::-webkit-scrollbar {
  width: 6px;
  /* 滚动条的宽度 */
}

::v-deep .custom-dialog .el-dialog__body::-webkit-scrollbar-track {
  background: #f5f5f5; /* 浅灰色 */
  /* 滚动条的背景色 */
}

::v-deep .custom-dialog .el-dialog__body::-webkit-scrollbar-thumb {
  background: #616161; /* 深灰色 */
  /* 滚动条的滑块颜色 */
}
.result-content {
  padding: 15px;
  height: calc(100vh - 700px); /* 视口高度减去一些固定高度 */
  overflow: scroll;
  /* 默认隐藏滚动条 */
  scrollbar-width: none; /* 对于 Firefox */
}

/* 隐藏默认滚动条 (Chrome/Safari) */
.result-content::-webkit-scrollbar {
  width: 6px;
  background: transparent; /* 初始透明 */
}

.result-content::-webkit-scrollbar-track {
  background: transparent; /* 初始透明 */
}

.result-content::-webkit-scrollbar-thumb {
  background: transparent; /* 初始透明 */
  border-radius: 3px;
}

/* 鼠标悬停时显示滚动条 */
.result-content:hover::-webkit-scrollbar-thumb {
  background: #616161; /* 深灰色 */
}

.result-content:hover::-webkit-scrollbar-track {
  background: #f5f5f5; /* 浅灰色 */
}

/* 对于 Firefox */
.result-content:hover {
  scrollbar-width: thin;
  scrollbar-color: #616161 #f5f5f5;
}

/* 对话框部分的样式 */
::v-deep .custom-dialog .el-dialog__body {
  height: 600px;
  overflow: scroll;
  scrollbar-width: none; /* 对于 Firefox */
}

/* 隐藏默认滚动条 (Chrome/Safari) */
::v-deep .custom-dialog .el-dialog__body::-webkit-scrollbar {
  width: 6px;
  background: transparent; /* 初始透明 */
}

::v-deep .custom-dialog .el-dialog__body::-webkit-scrollbar-track {
  background: transparent; /* 初始透明 */
}

::v-deep .custom-dialog .el-dialog__body::-webkit-scrollbar-thumb {
  background: transparent; /* 初始透明 */
  border-radius: 3px;
}

/* 鼠标悬停时显示滚动条 */
::v-deep .custom-dialog .el-dialog__body:hover::-webkit-scrollbar-thumb {
  background: #616161; /* 深灰色 */
}

::v-deep .custom-dialog .el-dialog__body:hover::-webkit-scrollbar-track {
  background: #f5f5f5; /* 浅灰色 */
}

/* 对于 Firefox */
::v-deep .custom-dialog .el-dialog__body:hover {
  scrollbar-width: thin;
  scrollbar-color: #616161 #f5f5f5;
}
</style>