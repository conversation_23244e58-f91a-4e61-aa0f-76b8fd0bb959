<!-- ai想 -->
<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle">
          <!-- <img src="../assets/lo.png" alt="" />
        <p v-if="navShow">思盒</p> -->
          <!-- <img src="../img/logo05.png" width="100%" alt="" /> -->
          <!-- <img src="../assets/lo1.png" width="100%" alt="" /> -->
        </div>
        <div class="ai-header">
          <div class="ai-bar">
            <HeadNavigation />
          </div>
        </div>
      </el-header>
      <el-main>
        <div class="LgscCon">
          <div class="article"></div>
          <!-- <div class="flex">
            <el-input
              v-model="inputval"
              placeholder="搜索素材，多个搜索词用空格隔开"
            ></el-input>
            <div class="button flex" @click="se()">
              <img src="../assets/sousuo-5.png" alt="" />
              <p>搜索</p>
            </div>
          </div> -->
          <div class="lgfl">
            <div class="flex mgr1">分类</div>
            <div
              @click="flTitle(item.name, index, this)"
              class="flex mgr"
              :class="[flpd == item.name ? 'activeFl' : 'mgr']"
              v-for="(item, index) in this.flArr"
              :key="index"
            >
              {{ item.name }}
            </div>
          </div>
          <div class="lgfl lgfl1" v-if="this.ztArr.length > 0">
            <div class="flex mgr1">主题</div>
            <div
              @click="ztBody(item.value)"
              class="flex mgr"
              :class="[ztpd == item.value ? 'activeFl' : 'mgr']"
              v-for="(item, index) in this.ztArr"
              :key="index"
            >
              {{ item.value }}
            </div>
          </div>
          <div class="nrCon">
            <div class="nrConLeft">
              <div
                class="nrConLeftBk"
                v-for="(item, index) in this.nrArr"
                :key="index"
                v-if="index % 2 == 0"
              >
                <div class="bkTitle">
                  {{ item.t1 }}
                </div>
                <div class="bkCon">
                  {{ item.t2 }}
                </div>
                <div class="bkBtn flexLd">
                  <div class="bkBtnLeft flexcz">
                    <div class="xztg flex">
                      {{ item.t3 }}
                    </div>
                    <div class="qt flex" v-if="item.t4 != ''">
                      {{ item.t4 }}
                    </div>
                  </div>
                  <div class="bkBtnright flexLd">
                    <div
                      class="fz flex"
                      v-clipboard:copy="item.t2"
                      v-clipboard:success="onCopySuccess"
                      v-clipboard:error="onCopyError"
                    >
                      <img src="../assets/icon-30.png" alt="" />
                      <p>复制</p>
                    </div>
                    <!-- <div class="sc flex">
                      <img src="../assets/icon-311.png" alt="" />
                      <p>收藏</p>
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
            <div class="nrConRight floatLeft">
              <div
                class="nrConLeftBk"
                v-for="(item, index) in this.nrArr"
                :key="index"
                v-if="index % 2 != 0"
              >
                <div class="bkTitle">
                  {{ item.t1 }}
                </div>
                <div class="bkCon">
                  {{ item.t2 }}
                </div>
                <div class="bkBtn flexLd">
                  <div class="bkBtnLeft flexcz">
                    <div class="xztg flex">
                      <!-- 写作提纲 -->
                      {{ item.t3 }}
                    </div>
                    <div class="qt flex" v-if="item.t4 != ''">
                      <!-- 其他 -->
                      {{ item.t4 }}
                    </div>
                  </div>
                  <div
                    class="bkBtnright flexLd"
                    v-clipboard:copy="item.t2"
                    v-clipboard:success="onCopySuccess"
                    v-clipboard:error="onCopyError"
                  >
                    <div class="fz flex">
                      <img src="../assets/icon-30.png" alt="" />
                      <p>复制</p>
                    </div>
                    <!-- <div class="sc flex">
                      <img src="../assets/icon-311.png" alt="" />
                      <p>收藏</p>
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import HeadNavigation from "../components/HeadNavigation.vue"; // 引入头部导航组件

import { getTitle, getTitle2, getBody } from "../api/home.js"; // 接口请求
import { search } from "../api/home3.js"; // 接口请求
import store from "../store/index";

export default {
  computed: {
    username() {
      return this.$store.state.username;
    },
  },
  data() {
    return {
      array2: "",
      inputval: "",
      navShow: true,
      flpd: "",
      ztpd: "",
      input: "",
      flArr: [],
      ztArr: [],
      nrArr: [],
      flmc: "",
      ztmc: "",
    };
  },
  components: {

    HeadNavigation,

  },
  mounted() {
    this.getTitleStr();
  },
      methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
       clickTophy(){
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    toxg() {
      this.$router.push("/grzx4");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    se() {
      this.array2 = this.nrArr;
      if (this.inputval == "") {
        // this.$message({
        //     type: 'info',
        //     message: '请输入正确信息'
        // });
        this.nrArr = this.array2;
      } else {
        this.nrArr = this.array2.filter(
          (item) => item.t2.includes(this.inputval),
          this.inputval == ""
        );
      }
    },
    //   async se(){
    //     // alert(ok!),
    //     console.log(this.inputval);
    //     let params = {
    //       nr: this.inputval,
    //   }
    //   let res=await search(params);
    //   console.log(res);
    // },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    onCopySuccess() {
      this.$message({
        message: "内容已复制到剪贴板！",
        type: "success",
      });
    },
    onCopyError() {
      this.$message({
        message: "复制失败，请手动复制!",
        type: "warning",
      });
    },
    clickTopsfw() {
      this.$router.push("/sfw");
    },
    async getTitleStr() {
      let data = await getTitle();
      console.log(data);
      this.flArr = data.data;
      this.flTitle(this.flArr[0].name);
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickTopAi() {
      this.$router.push("/index");
    },
    clickTopJq() {
      this.$router.push("62.234.166.176");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    async flTitle(val, index) {
      this.flpd = val;
      this.flmc = val;
      let params = {
        name: val,
      };
      let data = await getTitle2(params);
      console.log(data);
      this.ztArr = data.data;
      if (val == this.flArr[1].name) {
        this.ztBody(this.ztArr[0].value);
      }
      if (data.data.length > 0) {
        this.getNr1(this.flmc, data.data[0].value);
      } else {
        this.getNr();
      }
    },
    async ztBody(val) {
      console.log(val);
      this.ztpd = val;
      this.ztmc = val;
      this.getNr();
    },
    async getNr() {
      let params = {
        title1: this.flmc,
        title2: this.ztmc,
      };
      let data = await getBody(params);
      this.nrArr = data.data.data;
      console.log(this.nrArr);
      this.ztmc = "";
    },
    async getNr1(t1, t2) {
      console.log(t1, t2);
      let params = {
        title1: t1,
        title2: t2,
      };
      let data = await getBody(params);
      this.nrArr = data.data.data;

      console.log(this.nrArr);
      this.ztmc = "";
    },
  },
};
</script>
<style lang="less" scoped>
.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;
    // font-size: calc(100vw *  15 / 1920);

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}
.highlight {
  background-color: yellow;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1000);
  width: calc(100vw * 300 / 1920);
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: linear-gradient(76deg, #07389c 0%, #3d86d1 0%, #3448b3 100%);

  img {
    width: calc(100vw * 120 / 1920);
    height: calc(100vh * 48 / 1000);
  }

  p {
    border: calc(100vh * 1.54 / 1000) solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vh * 24 / 1000);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: calc(100vh * 5 / 1000);
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0 calc(100vh * 2 / 1000) calc(100vh * 4 / 1000) 0
    rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: 100%;

    .ai-bar {
      width: calc(100% - calc(100vw * 300 / 1920));
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      // height: calc(100vh * 80 / 1000);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vw * 40 / 1920);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: calc(100vw * 1 / 1920) solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vw * 130 / 1920);
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vw * 16 / 1920);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vw * 14 / 1920);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
                rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }
          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0
              rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }
      ::v-deep(.el-input__inner) {
        height: 58px;
        border-radius: 0;
        font-family: PingFangSC-Regular;
        font-size: calc(100vh * 14 / 1000);
        color: #999999;
        letter-spacing: 0;
        font-weight: 400;
      }
      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: calc(100vh * 93 / 1000);
        height: calc(100vh * 80 / 1000);

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vh * 16 / 1000);
            height: calc(100vh * 16 / 1000);
          }
        }

        .btn {
          margin-left: calc(100vh * 30 / 1000);
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vh * 12 / 1000);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vh * 28 / 1000);
            height: calc(100vh * 28 / 1000);
            float: left;
          }

          p {
            float: left;
            margin-left: calc(100vh * 22 / 1000);
            margin-top: calc(100vh * -2 / 1000);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vh * 14 / 1000);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: calc(100vh * 20 / 1000) calc(100vh * 10 / 1000);
}

.LgscCon {
  width: calc(100vw * 1002 / 1920);
  height: auto;
  margin: 0 auto;

  .button {
    width: calc(100vw * 108 / 1920);
    height: calc(100vh * 50 / 1000);
    background: #4c91ff;
    border-radius: 0 calc(100vh * 100 / 1000) calc(100vh * 100 / 1000) 0;
    cursor: pointer;
  }

  img {
    width: calc(100vh * 18 / 1000);
    height: calc(100vh * 18 / 1000);
    margin-right: calc(100vh * 8 / 1000);
  }

  p {
    line-height: calc(100vh * 50 / 1000);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vh * 14 / 1000);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flexcz {
  display: flex;
  align-items: center;
}

.flexNoCz {
  display: flex;
  justify-content: center;
}

.floatLeft {
  float: left;
}

.lgfl {
  width: 100%;
  height: calc(100vh * 40 / 1000);
  margin-top: calc(100vh * 42 / 1000);
  font-family: PingFangSC-Semibold;
  font-size: calc(100vh * 14 / 1000);
  color: #999999;
  letter-spacing: 0;
  display: flex;
  align-items: center;
}

.lgfl1 {
  margin-top: calc(100vh * 10 / 1000);
}

.mgr1 {
  margin-right: calc(100vh * 26 / 1000);
  font-family: PingFangSC-Semibold;
  font-size: calc(100vh * 14 / 1000);
  color: #999999;
  letter-spacing: 0;
  text-align: center;
}

.mgr {
  font-family: PingFangSC-Semibold;
  font-size: calc(100vh * 14 / 1000);
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  width: calc(100vw * 96 / 1920);
  margin-right: calc(100vh * 26 / 1000);
  cursor: pointer;

  &:hover {
    width: calc(100vw * 96 / 1920);
    height: 100%;
    background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
    border-radius: calc(100vh * 20 / 1000);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vh * 14 / 1000);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }

  &:active {
    width: calc(100vw * 96 / 1920);
    height: 100%;
    background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
    border-radius: calc(100vh * 20 / 1000);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vh * 14 / 1000);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}
.flexLd {
  display: flex;
  justify-content: space-between;
}
::v-deep(.el-input__inner) {
  // height: 54px;
  height: calc(100vh * 54 / 1080);
  border-radius: 0;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}

.activeFl {
  width: calc(100vw * 96 / 1920);
  height: 100%;
  background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
  border-radius: calc(100vh * 20 / 1000);
  font-family: PingFangSC-Semibold;
  font-size: calc(100vh * 14 / 1000);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
}

.nrCon {
  margin-top: calc(100vh * 30 / 1000);
  width: 100%;
  height: calc(100vh * 700 / 1000);
  overflow-y: scroll;

  .nrConLeft {
    width: calc(50% - calc(100vh * 10 / 1000));
    height: auto;
    float: left;
  }

  .nrConRight {
    width: calc(50% - calc(100vh * 10 / 1000));
    height: auto;
    float: right;
  }
}

.nrConLeftBk {
  width: auto;
  height: auto;
  background: #ffffff;
  box-shadow: 0 calc(100vh * 2 / 1000) calc(100vh * 9 / 1000) 0
    rgba(0, 0, 0, 0.13);
  border-radius: calc(100vh * 12 / 1000);
  padding: calc(100vh * 28 / 1000) calc(100vh * 40 / 1000);
  position: relative;
  margin-bottom: calc(100vh * 20 / 1000);

  .bkTitle {
    height: auto;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vh * 16 / 1000);
    color: #333333;
    letter-spacing: 0;
    text-align: left;
  }

  .bkCon {
    height: auto;
    margin-top: calc(100vh * 19 / 1000);
    font-family: PingFangSC-Regular;
    font-size: calc(100vh * 16 / 1000);
    color: #333333;
    text-align: left;
    letter-spacing: 0;
    line-height: calc(100vh * 24 / 1000);
    font-weight: 400;
  }

  .bkBtn {
    width: 100%;
    margin-top: calc(100vh * 16 / 1000);
    height: auto;
  }

  .bkBtnLeft {
    width: 50%;
    height: calc(100vh * 24 / 1000);
  }

  .xztg {
    width: auto;
    height: calc(100vh * 24 / 1000);
    background: #f5f8fc;
    border-radius: calc(100vh * 12 / 1000);
    padding: 0 calc(100vh * 10.5 / 1000);
    font-family: PingFangSC-Regular;
    font-size: calc(100vh * 14 / 1000);
    color: #7b7e9e;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    cursor: pointer;
  }

  .qt {
    width: auto;
    height: calc(100vh * 24 / 1000);
    background: #f5f8fc;
    border-radius: calc(100vh * 12 / 1000);
    padding: 0 calc(100vh * 10.5 / 1000);
    font-family: PingFangSC-Regular;
    font-size: calc(100vh * 14 / 1000);
    color: #7b7e9e;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    margin-left: calc(100vh * 10 / 1000);
    cursor: pointer;
  }

  .bkBtnright {
    width: calc(100vw * 54 / 1920);
    height: calc(100vh * 24 / 1000);

    .fz {
      width: calc(100vw * 53 / 1920);
      height: calc(100vh * 24 / 1000);
      cursor: pointer;

      img {
        width: calc(100vh * 16 / 1000);
        height: calc(100vh * 16 / 1000);
      }

      p {
        font-family: SourceHanSansSC-Medium;
        font-size: calc(100vh * 14 / 1000);
        color: #666666;
        letter-spacing: 0;
        line-height: calc(100vh * 24 / 1000);
        font-weight: 500;
      }
    }

    .sc {
      width: calc(100vw * 53 / 1920);
      height: calc(100vh * 24 / 1000);
      cursor: pointer;

      img {
        width: calc(100vh * 16 / 1000);
        height: calc(100vh * 16 / 1000);
      }

      p {
        font-family: SourceHanSansSC-Medium;
        font-size: calc(100vh * 14 / 1000);
        color: #666666;
        letter-spacing: 0;
        line-height: calc(100vh * 24 / 1000);
        font-weight: 500;
      }
    }
  }

  ::v-deep(.el-alert--success.is-light) {
    background: #ebf9f7 !important;
    font-family: PingFangSC-Regular;
    font-size: calc(100vh * 12 / 1000);
    color: #09873f;
    letter-spacing: 0;
    font-weight: 400;
  }

  ::v-deep(.el-loading-spinner) {
    background-image: url("../img/icegif-1259.gif");
    background-repeat: no-repeat;
    background-size: calc(100vh * 150 / 1000) calc(100vh * 150 / 1000);
    height: calc(100vh * 100 / 1000);
    width: 100%;
    background-position: center;
    top: 40%;
  }

  ::v-deep(.el-loading-spinner .circular) {
    display: none;
  }

  ::v-deep(.el-loading-spinner .el-loading-text) {
    margin: calc(100vh * 85 / 1000) 0px;
  }

  ::v-deep(.el-step.is-simple .el-step__arrow::before) {
    transform: rotate(-90deg) translateY(calc(-100vh * 20 / 1000))
      translateX(calc(-100vh * 16 / 1000));
    transform-origin: 0 0;
  }

  ::v-deep(.el-step.is-simple .el-step__arrow::after) {
    transform: rotate(60deg) translateY(0px);
    transform-origin: 100% 100%;
    content: "";
    display: inline-block;
    position: absolute;
    height: 0px;
    width: 0px;
    background: #c0c4cc;
  }

  ::v-deep(.el-step.is-simple .el-step__arrow::before) {
    content: "";
    display: inline-block;
    position: absolute;
    height: calc(100vh * 30 / 1000);
    width: 1px;
    background: #c0c4cc;
  }

  ::v-deep(.el-step.is-simple .el-step__title) {
    font-size: calc(100vh * 14 / 1000) !important;
  }

  ::v-deep(.el-step__title.is-success) {
    color: #1b2126;
  }

  ::v-deep(.el-step__head.is-success) {
    color: #1b2126;
    border-color: #1b2126;
  }

  ::v-deep(.el-step__title.is-process) {
    color: #bbc6d3;
  }

  ::v-deep(.el-step__head.is-process) {
    color: #bbc6d3;
    border-color: #bbc6d3;
  }

  ::v-deep(.el-steps--simple) {
    background: none !important;
  }
}
</style>
