<template>
  <div>
    <!-- <h4>上传文件</h4> -->
    <el-form>
      <div>
        <el-radio-group v-model="form.fileType" @change="radioChange">
          <el-radio-button label="txt">文本文件</el-radio-button>
          <el-radio-button label="table">表格</el-radio-button>
          <el-radio-button label="QA">QA 问答对</el-radio-button>
        </el-radio-group>
      </div>
      
      <!-- QA 问答对 -->
      <el-form-item v-if="form.fileType === 'QA'" prop="fileList">
        <div class="update-info">
          <div>
            <p>
              1、点击下载对应模版并完善信息：
              <el-button type="primary" link @click="downloadTemplate('1')">
                下载 Excel 模版
              </el-button>
              <el-button type="primary" link @click="downloadTemplate('2')">
                下载 CSV 模版
              </el-button>
            </p>
            <p>
              2、上传的表格文件中每个 sheet 会作为一个文档，sheet名称为文档名称
            </p>
            <p>3、每次最多上传 50 个文件，每个文件不超过 100MB</p>
          </div>
        </div>
        <el-upload>
          <div class="upload-area">
            <p>
              拖拽文件至此上传或
              <em @click.prevent="handlePreview(false)">选择文件</em>
              <em @click.prevent="handlePreview(true)">选择文件夹</em>
            </p>
            <div class="upload__decoration">
              <p>支持格式：EXCEL、CSV</p>
            </div>
          </div>
        </el-upload>
      </el-form-item>
      
      <!-- 表格 -->
      <el-form-item v-else-if="form.fileType === 'table'" prop="fileList">
        <div class="update-info">
          <div>
            <p>
              1、点击下载对应模版并完善信息：
              <el-button type="primary" link @click="downloadTableTemplate('1')">
                下载 Excel 模版
              </el-button>
              <el-button type="primary" link @click="downloadTableTemplate('2')">
                下载 CSV 模版
              </el-button>
            </p>
            <p>
              2、第一行必须是列标题，且列标题必须是有意义的术语，表中每条记录将作为一个分段
            </p>
            <p>
              3、上传的表格文件中每个 sheet 会作为一个文档，sheet名称为文档名称
            </p>
            <p>4、每次最多上传 50 个文件，每个文件不超过 100MB</p>
          </div>
        </div>
        <el-upload>
          <div class="upload-area">
            <p>
              拖拽文件至此上传或
              <em @click.prevent="handlePreview(false)">选择文件</em>
              <em @click.prevent="handlePreview(true)">选择文件夹</em>
            </p>
            <div class="upload__decoration">
              <p>支持格式：EXCEL 和 CSV</p>
            </div>
          </div>
        </el-upload>
      </el-form-item>
      
      <!-- 文本文件 -->
      <el-form-item v-else prop="fileList">
        <div class="update-info">
          <div>
            <p>1、文件上传前，建议规范文件的分段标识</p>
            <p>2、每次最多上传 50 个文件，每个文件不超过 100MB</p>
          </div>
        </div>
        <el-upload>
          <div class="upload-area">
            <p>
              拖拽文件至此上传或
              <em @click.prevent="handlePreview(false)">选择文件</em>
              <em @click.prevent="handlePreview(true)">选择文件夹</em>
            </p>
            <div class="upload__decoration">
              <p>支持格式：TXT、Markdown、PDF、DOCX、HTML</p>
            </div>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        fileType: "txt",  // 默认选中文本文件
        fileList: [],
      },
      rules: {
        fileList: [
          { required: true, message: "请上传文件", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    downloadTemplate(type) {
      console.log('下载模板', type);
      // 这里添加下载逻辑
    },
    downloadTableTemplate(type) {
      console.log('下载表格模板', type);
      // 这里添加下载逻辑
    },
    radioChange() {
      this.form.fileList = [];  // 切换时清空文件列表
    },
    handlePreview(bool) {
      console.log('选择文件/文件夹', bool);
      // 这里添加文件选择逻辑
    }
  }
};
</script>

<style scoped>
.update-info {
  background: #d6e2ff;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.upload-area {
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload__decoration {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

em {
  color: #409eff;
  font-style: normal;
  cursor: pointer;
  margin: 0 5px;
}

em:hover {
  text-decoration: underline;
}
</style>