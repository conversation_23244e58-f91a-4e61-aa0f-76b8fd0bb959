{"name": "BMAI", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.6.7", "core-js": "^3.8.3", "echarts": "^5.5.1", "echarts-wordcloud": "^2.1.0", "element-ui": "^2.15.14", "install": "^0.13.0", "less": "^4.2.0", "less-loader": "^11.1.3", "marked": "^13.0.2", "plyr": "^3.7.8", "video.js": "^7.21.6", "vue": "^2.7.16", "vue-clipboard2": "^0.3.3", "vue-pdf": "^4.3.0", "vue-router": "3.5.2", "vue2-editor": "^2.10.3", "vuex": "^3.6.2", "vuex-along": "^1.2.13"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-code-diff": "^1.2.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}