<template>
  <div ref="container" :style="{ width: '100%', height: '100%' }"></div>
</template>
<script>
import * as echarts from "echarts";
import "echarts-wordcloud";
import { cyt } from "../api/home.js"; // 接口请求

export default {
  computed: {
    // 词云数据
    datas() {
      return this.$store.state.datas;
    },
    username() {
      return this.$store.state.username;
    },
  },
  data() {
    return {
      wcdata: [],
    };
  },
  methods: {
    clickpb() {
      this.$router.push("/pb_manage");
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    togrzx() {
      // alert(1)
      this.$router.push("/grzx1");
    },
    drawWordCloud() {
      const option = {
        tooltip: {
          show: false,
          position: "top",
          textStyle: {
            fontSize: 30,
          },
        },
        series: [
          {
            type: "wordCloud",
            gridSize: 40,
            shape: "circle",
            sizeRange: [25, 35],
            rotationRange: [0, 0],
            left: "center",
            top: "center",
            width: "90%",
            height: "80%",
            drawOutOfBound: false,
            textStyle: {
              color: function () {
                return (
                  "rgb(" +
                  [
                    Math.round(Math.random() * 200 + 55),
                    Math.round(Math.random() * 200 + 55),
                    Math.round(Math.random() * 200 + 55),
                  ].join(",") +
                  ")"
                );
              },
              emphasis: {
                shadowBlur: 10,
              },
            },
            data: this.datas, // 使用数据数组
          },
        ],
      };

      const chart = echarts.init(this.$refs.container);
      chart.setOption(option);
    },
  },
  watch: {
    datas: {
      handler() {
        this.drawWordCloud();
      },
      immediate: true, // 立即执行一次
      deep: true, // 深度监听
    },
  },
  mounted() {
    this.drawWordCloud(); // 确保在数据获取后重新绘制词云
  },
  components: {},
};
</script>

<style>
</style>
