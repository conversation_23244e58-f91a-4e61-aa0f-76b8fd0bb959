<template>
  <div class="base-container">
    <!-- 查询条件以及操作按钮start -->
    <!-- <Form :model="params" class="base-form"> -->
      <!-- <Row>
        <Col class='search-wrap' :span="columns.length?columns.length:24" v-if="columns.length"> -->
          <template v-for="(item, index) in columns">
            <!-- 普通输入框start -->
            <template v-if="item.type == 'searchInput'">
              <!-- <FormItem :label="item.name" class="elFormLabel"> -->
                <div class="SearchHeaderItem">
                  <Input :style="item.style" v-model="params[item.value]" :size="item.size || 'default'" clearable
                    :placeholder="item.placeholder" />
                </div>
              <!-- </FormItem> -->
            </template>
            <!-- 级联选择框 -->
            <template v-if="item.type === 'cascader'">
              <div class="SearchHeaderItem">
                <span class="label" v-if="item.label">{{item.label}}</span>
                <Cascader
                  ref='cascader'
                  :size="item.size || 'default'"
                  :props="item.props"
                  :style="item.style"
                  v-model="params[item.value]"
                  :data="item.options"
                  :clearable='item.clearable||false'
                  @change="handleChangeCascader($event,item)"
                  :trigger="item.trigger || 'click'"
                  :disabled="item.disabled || false"
                ></Cascader>
              </div>
            </template>
            <!-- 多选下拉框 -->
            <template v-else-if="item.type === 'checkboxInput'">
              <div class="SearchHeaderItem">
                <span class="label" v-if="item.label">{{item.label}}</span>
                <Select
                  @change="handleSearch($event)"
                  :style="item.style"
                  class="t-input"
                  v-model="params[item.value]"
                  :size="item.size || 'default'"
                  multiple collapse-tags
                  :placeholder="item.placeholder">
                  <Option
                    v-for="items in item.options"
                    :key="items[item.optionValue || 'value']"
                    :label="items[item.optionLabel || 'label']"
                    :value="items[item.optionValue || 'value']">
                  </Option>
                </Select>
                <span v-if="item.suffix" class="suffix">{{item.suffix}}</span>
              </div>
            </template>
            <!-- 多选框 -->
            <template v-else-if="item.type === 'checked'">
              <div class="SearchHeaderItem">
                <Checkbox v-model="params[item.value]" @change="handleSearch($event)">{{item.label}}</Checkbox>
              </div>
            </template>
            <!-- 普通输入框end -->
            <!-- 时间范围选择器start -->
            <!-- <template v-else-if="item.type == 'dataRange'">
                <FormItem :label="item.name" class="elFormLabel">
                  <el-date-picker v-model:value="params[item.value]" type="daterange" :range-separator="item.rangeSeparator"
                    style="width:394px;" :start-placeholder="item.startPlaceholder" :end-placeholder="item.endPlaceholder"
                    :format="item.format" :value-format="item.format">
                  </el-date-picker>
                </FormItem>
              </template> -->
            <!-- 时间范围选择器end -->
            <!-- 下拉框 -->
            <template v-else-if="item.type === 'select'">
              <div class="SearchHeaderItem">
                <span class="label" v-if="item.label">{{ item.label }}</span>
                <Select ref="select" :size="item.size || 'default'" :style="item.style" :placeholder='item.placeholder || "请选择"'
                  v-model="params[item.value]">
                  <Option v-for="option in item.options || []" :key="option[item.optionValue || 'id']"
                    :value="option[item.optionValue || 'value']">
                    {{ option[item.optionLabel || 'name'] }}
                  </Option>
                </Select>
              </div>
            </template>
            <!-- 时间选择器 -->
            <template v-else-if="item.type === 'timePicker'">
              <div class="SearchHeaderItem">
                <span class="label" v-if="item.label">{{item.label}}</span>
                <div class="block">
                  <DatePicker
                    :style="item.style"
                    :size="item.size || 'default'"
                    :type="item.dateType||'datetimerange'"
                    clearable
                    @change="handleSearch($event)"
                    v-model="params[item.value]"
                    :options="item.pickerOptions"
                    :format="item.valueFormat||'yyyy-MM-dd HH:mm:ss'"
                    :placeholder="item.placeholder||'选择时间'"
                    :separator="item.separator||'-'"
                    :disabled="item.disabled"
                    >
                  </DatePicker>
                </div>
              </div>
            </template>
            <!-- 部门选择器cascader----start -->
            <!-- <template v-else-if="item.type == 'cascader'">
                <FormItem :label="item.name" class="elFormLabel">
                  <Cascader v-model:value="params[item.value]" :options="selectOptions" :props="regionParams" filterable clearable
                ref="cascaderArr" @change="bmSelectChange"></Cascader>
                </FormItem>
              </template> -->
            <!-- 部门选择器cascader----end -->
            <!-- 操作按钮start -->
            <template v-else-if="item.type == 'button'">
              <!-- <FormItem class="elFormLabel"> -->
                <Button class="SearchHeaderItem" :style="item.style" @click="handleEvent(item)" :size="item.size || 'default'" :type="item.mold"
                  :disabled="item.disabled">
                  <!-- <template #icon v-if="item.icon" v-html="`< ${item.icon} />`"></template> -->
                  {{ item.name }}
                </Button>
              <!-- </FormItem> -->
            </template>
            <!-- 操作按钮end -->
          </template>
        <!-- </Col>
      </Row> -->
    <!-- </Form> -->
    <div style="clear: both;"></div>
    <!-- 查询条件以及操作按钮end -->
  </div>
</template>
<script>
// import { Col } from 'ant-design-vue'

export default {
  // components: { Col },
  props: {
    params: {
      type: Object,
      require: false,
      default: {}
    },
    columns: {
      type: Array,
      require: false,
      default: []
    },
    // selectOptions: {
    //   type: Array,
    //   require: false,
    //   default: []
    // },
    // regionParams: {
    //   type: Object,
    //   require: false,
    //   default: {}
    // }



  },
  data() {
    return {

    }
  },
  computed: {},
  mounted() {

  },
         methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
     togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    handleEvent(item) {
      console.log(item)
      let params = JSON.parse(JSON.stringify(this.params))
      console.log(params)
      this.$emit(`handle${item.typeName}`, params, item)
    }

  },
  watch: {

  }
}
</script>
  <!-- <script setup>
  import { getCurrentInstance } from 'vue'
  const props = defineProps({
    params: {
        type: Object,
        default: {}
    },
    columns: {
        type: Array,
        default: []
    }
  });
  const { emit } = getCurrentInstance()
  const handleEvent = (item) => {
    let params = JSON.parse(JSON.stringify(props.params))
    emit(`handle${item.typeName}`, params,item)
  }
  </script> -->
<style scoped lang="less">
.SearchHeaderItem {
  float: left;
  margin-left: 10px;
  .label {
    float: left;
  }
  .block {
    float: left;
  }
}
.base-container {
  .base-form {
    overflow: hidden;

    .elFormLabel {
      font-weight: 700;
      float: left;
    }
  }
}
</style>