<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle"></div>
        <div class="ai-header">
          <div class="ai-bar">
            <div class="ai-left-bar">
              <ul class="ai-left-bar-ul">
                <li @click="clickTopsfw" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />文
                </li>
                <!-- <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
                <li @click="clickTopTy" class="ai-left-bar-li">
                  <img src="../assets/icon15-h.png" alt="" />Ai读
                </li>
                <li class="ai-left-bar-li" @click="clickTopAi">
                  <img src="../assets/icon101.png" alt="" />AI写
                </li>
                <!-- <li @click="clickTopNav" class="ai-left-bar-li">
                    <img src="../assets/icon102-h.png" alt="" />
                    AI校对润色
                  </li>
                  <li @click="clickTopNav" class="ai-left-bar-li">
                    <img src="../assets/icon12-h.png" alt="" />笔墨文库
                  </li> -->
                <li @click="clickTopLg" class="ai-left-bar-li">
                  <img src="../assets/icon13-h.png" alt="" />Ai想
                </li>
                <li @click="clickTopJq" class="ai-left-bar-li">
                  <img src="../assets/icon14-h.png" alt="" />Ai听
                </li>
               <!-- <li @click="clickrw" class="ai-left-bar-li">
                  <img src="../assets/check_blue.png" alt="" />核稿
                </li>  -->
              <li @click="clickTophy" class="ai-left-bar-li">
                  <img src="../assets/icon202a.png" alt="" />会议记录
                </li> 
                <!-- <li @click="clickTopFz" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />辅助定密
                </li> -->
                <li class="ai-left-bar-li actived">
                  <img src="../assets/icon204b.png" alt="" />助手
                </li>
                <!-- <li @click="clickTopsfw" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />收文
                </li>
               <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
              </ul>
            </div>
            <div class="ai-right-bar">
              <!-- <div class="top-button" @click="clickTopNav">
               
                <img src="../assets/icon16.png" alt="" />
              </div> -->
              <!-- <div class="top-button btn" @click="clickTopNav">
               
                <img src="../assets/icon17.png" alt="" />
              </div> -->
              <el-dropdown :hide-on-click="false" trigger="click">
                <div class="ai-avant btn">
                  <img src="../assets/icon18.png" alt="" />
                  <p>{{ username }}</p>
                </div>
                <el-dropdown-menu
                  slot="dropdown"
                  style="height: 50%; width: 20%; margin-right: -50px"
                >
                  <div class="user-menu-content">
                    <div class="user-info">
                      <div class="avatar-wrapper">
                        <img src="../assets/icon_tx.png" alt="" />
                      </div>
                      <div class="name-wrapper">
                        <div class="name">{{ username }}</div>
                        <div class="id">ID：001</div>
                      </div>
                    </div>
                    <div class="divider"></div>
                    <div class="options">
                      <div
                        rel="opener"
                        class="option"
                        href=""
                        target="_blank"
                        @click="togrzx()"
                      >
                        <div class="icon personal-center"></div>
                        <div class="text">个人中心</div>
                      </div>

                      <div
                        rel="opener"
                        class="option1"
                        href=""
                        target="_blank"
                        @click="toxg()"
                      >
                        <div class="el-icon-edit"></div>
                        <div class="text">修改密码</div>
                      </div>
                      <div
                        rel="opener"
                        class="option"
                        href=""
                        target="_blank"
                        @click="wdwd()"
                      >
                        <div class="icon my-document"></div>
                        <div class="text">我的文档</div>
                      </div>
                      <!-- <a rel="opener" class="option" href="" target="_blank">
                        <div class="icon my-favourite"></div>
                        <div class="text">我的收藏</div>
                      </a> -->
                      <a class="option" @click="logout()">
                        <div class="icon logout"></div>
                        <div class="text">退出登录</div>
                      </a>
                    </div>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </el-header>
      <el-main
        v-loading="loading"
        element-loading-text="文字识别中，请稍后..."
        element-loading-background="rgba(255, 255, 255, 0.7)"
      >
        <div class="bmaiContainer">
          <!-- 公网地址 -->
          <!-- <iframe class="bmaiAiIframe" style="width: 100%;" src="http://221.212.111.71:28501/">
          </iframe> -->
          <!-- 线上服务器、168-->
          <!-- <iframe class="bmaiAiIframe" style="width: 100%;" src="http://**************:8051/">
          </iframe> -->
          <!-- 小黑盒、**************-->
          <iframe
            class="bmaiAiIframe"
            style="width: 100%"
            src="http://**************:8501/"
          >
          </iframe>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import pdf from "vue-pdf";
import { VueEditor } from "vue2-editor";
import { getOcr, testcheck, getOcrtxt, summary_string } from "../api/home.js"; // 接口请求
import { mapState, mapActions } from "vuex";
import store from "../store/index";

export default {
  data() {
    return {
      text1: "",
      con_loading: false,
      jiaoyanjc: false,
      jydis: false,
      activeNames: [],
      correctionList: [],
      navShow: true,
      fileList: [],
      file: "",
      filename: "",
      input: "",
      imgURl: "",
      imgURlss: "",
      imgURlcw: "",
      loading: false,
      text: "",
      imgListRes: [],
      imgpd: "",
      type: "",
      filePdfUrl: "",
      filePdfUrlcw: "",
      reverse: true,
      numPages: 0, // 初始化页数
      activities: [
        {
          content: "活动按期开始",
          timestamp: "2018-04-15",
        },
        {
          content: "通过审核",
          timestamp: "2018-04-13",
        },
        {
          content: "通过审核",
          timestamp: "2018-04-13",
        },
        {
          content: "创建成功",
          timestamp: "2018-04-11",
        },
      ],
    };
  },
  components: {
    VueEditor,
    pdf,
  },
  mounted() {
    this.imgListRes = JSON.parse(JSON.stringify(this.imgList));
    // 使用数组的length属性减1来获取最后一个元素的索引
    let lastIndex = this.imgListRes.length - 1;
    // 通过索引获取最后一条数据
    let lastItem = this.imgListRes[lastIndex];
    this.changeBtn(lastItem);
  },
  computed: {
    ...mapState(["imgList"]),
    username() {
      return this.$store.state.username;
    },
  },
      methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
    clickTophy() {
      this.$router.push("/hyjl_sz");
    },
    clickrw() {
      this.$router.push("/check");
    },
    clickmb() {
      this.$router.push("/mb_manage");
    },
    toxg() {
      this.$router.push("/grzx4");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    async getSummary() {
      console.log(this.text);
      let params = {
        text: this.text,
      };
      let data = await summary_string(params);
      this.text1 = data;
      console.log(data, "315");
    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ["#ff6403", "#fbff00", "#01fffe", "#e958ea"];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    highlightError(error, word) {
      this.text = this.removetext(this.text);
      let a = error.replace(
        word,
        `<span class="highlight" style="background-color:yellow!important;">${word}</span>`
      );
      this.text = this.text.replace(error, a);
      console.log(this.text);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector("div.ql-editor");
      console.log(
        this.$refs.editor.$el.querySelector("div.ql-editor"),
        "editorIframe"
      );
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector("span.highlight");
        console.log(highlightedElement, "highlightedElement");
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    },
    ignore() {
      this.text = this.removetext(this.text);
      this.activeNames = [];
    },
    highlightChange(text1, text2) {
      console.log(this.text);
      let changeData = text2;
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.text = this.text.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.text = this.removetext(this.text);
      this.text = this.text.replace(text1, changeData);
    },
    save() {
      // this.text = this.removetext(this.text)
      // this.activeNames = []
      this.jiaoyanjc = false;
    },
    removetext(text) {
      let str = text;
      let reg1 = new RegExp(
        `<span class="highlight" style="background-color:yellow!important;">`,
        "g"
      );
      let a1 = str.replace(reg1, "");
      let reg2 = new RegExp(`</span>`, "g");
      text = a1.replace(reg2, "");
      return text;
    },
    async jiaoyan() {
      console.log(this.type, "type");
      console.log(this.imgURl, "imgURl");
      this.con_loading = true;

      this.text = this.removetext(this.text);
      testcheck({
        test_all: this.text,
      }).then(async (data) => {
        this.correctionList = data.data;

        if (this.correctionList.length == 0) {
          this.jiaoyanjc = false;
          this.$message({
            message: "不存在文字错误",
            type: "warning",
          });
        } else {
          if (this.type == "img") {
            let fd = {
              imgs_name: "1.png",
              base_string: this.imgURlss,
            };
            getOcrtxt(fd).then(async (data) => {
              console.log(data, "362");
              this.imgURlcw = data.data;
              this.con_loading = false;
            });
          } else {
            this.con_loading = false;
          }
          console.log(data, "619");
          this.jiaoyanjc = true;
        }
      });
    },
    ...mapActions(["addItemAction"]),
    changeBtn(val) {
      // console.log(val.imgs_base);
      if (val) {
        this.imgpd = "data:image/jpeg;base64," + val.re_imgs_base;
        // this.imgURl = val.imgs_base;
        this.imgURl = val.re_imgs_base;
        this.imgURlss = val.base_string;
        // this.text1 = '习近平在中央政治局第十一次集体学习中强调，发展新质生产力是推动高质量发展的关键。总结新时代高质量发展的成就，并分析当前的突出矛盾和问题，提出改进措施以推动新的突破。高质量发展已经成为全党共识，取得了显著成效，但仍存在诸多制约因素，如关键技术受制于人、城乡和收入分配差距较大等。习近平指出，必须全面贯彻新发展理念，推进现代化经济体系建设和科技自立，深化改革开放，确保高质量发展基础。习近平提出的新质生产力，强调创新为主导，摆脱传统增长模式，具有高科技、高效能、高质量特征。要通过科技创新、产业创新、发展方式创新、体制机制创新和人才机制创新，推动新质生产力的发展。具体措施包括加强原创性科技创新，促进科技成果转化为现实生产力，推动绿色低碳转型，深化改革形成新型生产关系，以及完善人才培养和激励机制。';
        if (val.type == "pdf" || val.type == "ofd") {
          this.filePdfUrl = "data:application/pdf;base64," + val.re_imgs_base;
          const loadingTask = pdf.createLoadingTask(this.filePdfUrl);
          loadingTask.promise
            .then((pdf) => {
              this.numPages = pdf.numPages;
            })
            .catch((err) => {
              console.error("Error while loading the PDF", err);
            });
        }
        this.type = val.type;
        this.text = val.txt;
        this.$nextTick(() => {
          this.$refs.pdfContainer.scrollTop = 0;
        });
      }
    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickTopAi() {
      this.$router.push("/index");
    },
    clickTopLg() {
      this.$router.push("/lgsc");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopsfw() {
      this.$router.push("/sfw");
    },
    beforeAvatarUpload(file) {
      console.log(file.type);
      const isJPG = file.type === "image/jpeg";
      const isPNG = file.type === "image/png";
      const isPDF = file.type === "application/pdf";
      if (!isJPG && !isPNG && !isPDF) {
        this.$message.error("上传缩略图只能是 JPG/PNG/PDF 格式!");
      }
      return isJPG || isPNG || isPDF;
    },

    uploadFile(item) {
      let file;
      this.filename = item.file.name;

      this.blobToBase64(item.file, (dataurl) => {
        file = dataurl.split(",")[1];
        this.file = JSON.parse(JSON.stringify(file));
        this.uploadMp(this.file);
      });
    },
    async uploadMp(val) {
      console.log(val);
      console.log(this.filename);
      let that = this;
      this.loading = true;
      let fd = new FormData();
      fd.append("imgs_name", this.filename);
      fd.append("base_string", val);
      let resData = await getOcr(fd);
      this.$refs.upload.clearFiles();
      if (resData.data.status_code == 200) {
        that.addItemAction(resData.data.data);
        this.imgListRes = JSON.parse(JSON.stringify(this.imgList));
        // 使用数组的length属性减1来获取最后一个元素的索引
        let lastIndex = this.imgListRes.length - 1;
        // 通过索引获取最后一条数据
        let lastItem = this.imgListRes[lastIndex];
        this.getSummary();
        this.changeBtn(lastItem);

        // that.$store.commit("addItem", resData.data.data);
        this.$message({
          showClose: true,
          message: resData.data.message,
          type: "success",
        });
        this.loading = false;
        // this.imgURl = resData.data.data.re_imgs_base;
        // this.text = resData.data.data.txt;
        // this.text1 = '习近平在中央政治局第十一次集体学习中强调，发展新质生产力是推动高质量发展的关键。总结新时代高质量发展的成就，并分析当前的突出矛盾和问题，提出改进措施以推动新的突破。高质量发展已经成为全党共识，取得了显著成效，但仍存在诸多制约因素，如关键技术受制于人、城乡和收入分配差距较大等。习近平指出，必须全面贯彻新发展理念，推进现代化经济体系建设和科技自立，深化改革开放，确保高质量发展基础。习近平提出的新质生产力，强调创新为主导，摆脱传统增长模式，具有高科技、高效能、高质量特征。要通过科技创新、产业创新、发展方式创新、体制机制创新和人才机制创新，推动新质生产力的发展。具体措施包括加强原创性科技创新，促进科技成果转化为现实生产力，推动绿色低碳转型，深化改革形成新型生产关系，以及完善人才培养和激励机制。';
        this.jydis = true;
        // let zpxx = this.zpzm(resData.data.imgs_base);
        // this.imgURl = zpxx;
      } else if (resData.data.status_code == 400) {
        this.$message({
          showClose: true,
          message: resData.data.message,
          type: "error",
        });
      } else if (resData.data.status_code == 500) {
        this.$message({
          showClose: true,
          message: resData.data.message,
          type: "error",
        });
      }
    },
    blobToBase64(blob, callback) {
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        callback(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    },
    zpzm(zp) {
      const iamgeBase64 = "data:image/jpeg;base64," + zp;
      let zpxx;
      if (typeof iamgeBase64 === "string") {
        // 复制某条消息
        if (!iamgeBase64) return; // console.log(data.string) // 正则表达式判断data是否是base64
        function validDataUrl(s) {
          return validDataUrl.regex.test(s);
        }
        validDataUrl.regex =
          /^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i; // 如果是base64转换成图片预览
        if (validDataUrl(iamgeBase64)) {
          // debugger;
          // let that = this;

          function previwImg(item) {
            zpxx = item;
          }
          previwImg(iamgeBase64);
        }
      }
      return zpxx;
    },
  },
};
</script>
<style lang="less" scoped>
.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: linear-gradient(
  //   76deg,
  //   #07389c 0%,
  //   #3d86d1 0%,
  //   #3448b3 100%
  // );

  img {
    width: 120px;
    height: 48px;
    position: absolute;
  }

  p {
    border: 1.54px solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vh * 40 / 1080);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: 1px solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vh * 130 / 1080);
            color: #000;
            font-size: calc(100vh * 14 / 1080);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vh * 14 / 1080);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vh * 16 / 1080);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vh * 14 / 1080);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw *  12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 /1080);
  padding: 20px;
  // overflow: hidden;
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.fir-title {
  color: #222;
  font-weight: 500;
  font-size: calc(100vw *  14 / 1920);
  letter-spacing: 0;
  line-height: 16px;
  overflow: hidden;
  height: 20px;

  .fir-kuai {
    width: 6px;
    height: 16px;
    margin-right: 8px;
    float: left;
    // margin-top: 2px;
    background: #4081ff;
    border-radius: 1.5px;
  }

  .fir-title-p {
    color: #222;
    font-weight: 500;
    font-size: calc(100vw *  14 / 1920);
    letter-spacing: 0;
    line-height: 16px;
    float: left;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}

.xxjs {
  width: 902px;
  height: calc(100vh * 40/ 1080);
  word-wrap: break-word;
  text-align: left;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  // font-weight: 600;
  margin-top: 12px;
}

.flexLd {
  display: flex;
  justify-content: space-between;
  height: auto;
}

.btnLeft {
  width: 332px;
  height: auto;

  .el-button {
    width: 104px;
    height: 30px;
    line-height: 15px;
    padding: 5px;
    font-size: calc(100vw *  14 / 1920);
    margin-left: 0;
    margin-right: 9px;
    margin-top: 10px;
    background: #f5f8fc;
    border: 1px solid rgba(31, 82, 176, 1);
    border-radius: 4px;
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw *  14 / 1920);
    color: #1f52b0;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;

    span {
      font-family: SourceHanSansSC-Medium;
      font-size: calc(100vw *  16 / 1920);
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }
  }
}

::v-deep(.el-button) {
  span {
    font-family: SourceHanSansSC-Medium;
    font-size: calc(100vw *  16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
}

.con {
  width: 100%;
  height: auto;
  margin-top: 20px;
  background: #ffffff;
  padding: 20px;

  .leftTp {
    width: 1030px;
    height: auto;

    .tpBG {
      width: 100%;
      height: 555px;
      margin-bottom: 20px;

      .zxTp {
        width: 162px;
        height: 100%;
        overflow-y: scroll;

        .xTpk {
          width: 100%;
          height: 136px;
          background: #dcdfe5;
          margin-bottom: 8px;

          &:hover {
            border: 1.35px solid rgba(26, 102, 255, 1);
          }

          .tpbj {
            width: 100%;
            height: 110px;
            background-color: #ffffff;

            img {
              width: 140px;
              height: 110px;
              object-fit: contain;
            }
          }

          &:active {
            border: 1.35px solid rgba(26, 102, 255, 1);
            width: 100%;
            height: 136px;
            background: #dcdfe5;
            margin-bottom: 8px;

            .tpbj {
              width: 140px;
              height: 110px;
              background-color: #ffffff;

              img {
                width: 140px;
                height: 110px;
              }
            }
          }
        }

        .activeImg {
          border: 1.35px solid rgba(26, 102, 255, 1);
          width: 100%;
          height: 136px;
          background: #dcdfe5;
          margin-bottom: 8px;

          .tpbj {
            width: 140px;
            height: 110px;
            background-color: #ffffff;

            img {
              width: 140px;
              height: 110px;
            }
          }
        }
      }

      .dxTp {
        width: 848px;
        height: 100%;
        background: #dcdfe5;

        .tpbj {
          width: 100%;
          height: 100%;
          background-color: #ffffff;

          img {
            // width: 350px;
            height: 450px;
            object-fit: contain;
          }
        }
      }

      .zwTp {
        background: url(../img/bga.png) no-repeat center;
        width: 88%;
        height: 100%;
        background-size: 50%;
      }
    }

    .tpUpload {
      width: 70%;
      height: 82px;
      padding-left: 18px;
      align-items: center;

      .upBtn {
        // width: 100%;
        height: calc(100vh * 40/ 1080);

        ::v-deep(.el-button) {
          background: #1a66ff;
          border: 1px solid rgba(31, 82, 176, 1);
          border-radius: 4px;
          width: calc(100vw * 160 / 1920);
          height: calc(100vh * 40/ 1080);
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw *  16 / 1920);
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }

        .sizeColor {
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
          margin: 10px;
        }

        ::v-deep(.el-input__inner) {
          background: #ffffff;
          border: 1px solid rgba(186, 203, 228, 1);
          border-radius: 4px;
          font-family: PingFangSC-Semibold;
          font-size: calc(100vw *  14 / 1920);
          color: #999999;
          letter-spacing: 0;
          // font-weight: 600;
        }
      }

      .zcSize {
        font-family: PingFangSC-Semibold;
        font-size: calc(100vw *  14 / 1920);
        color: #999999;
        letter-spacing: 0;
        // font-weight: 600;
        height: auto;
        text-align: left;
        // margin-top: 18px;
      }
    }
  }

  .rightJson {
    width: 781px;
    height: 700px;
    background: #f2f4f9;
    padding: 20px;
    position: relative;
    // .fir-textarea {
    //   height: calc(100% - 85px);

    //   ::v-deep(.el-textarea__inner) {
    //     height: 100%;
    //   }
    // }
    .fir-textarea {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #f8f9fd;
      border: 1px solid rgba(229, 232, 245, 1);
      border-radius: 4px;
      margin-top: 20px;
      margin-bottom: 0;
      height: 158px;

      ::v-deep(.el-textarea__inner) {
        font-size: 14px !important;
        background-color: #f8f9fd !important;
        height: 100% !important;
        font-family: PingFangSC-Regular;
        padding: 13px 18px 33px 16px;
      }
    }

    .fir-textarea-max {
      height: 41% !important;
    }

    .jsTitle {
      font-family: PingFangSC-Regular;
      font-size: calc(100vw *  16 / 1920);
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
      height: auto;
      text-align: left;
      margin-bottom: 5px;
    }

    .jsbor {
      border: 1px solid rgba(150, 171, 214, 1);
      height: auto;
      margin-bottom: 14px;
    }

    .jsConNr {
      width: 100%;
      height: calc(100% - 84px);
      font-family: PingFangSC-Regular;
      font-size: calc(100vw *  16 / 1920);
      color: #333333;
      letter-spacing: 0;
      line-height: 35px;
      font-weight: 400;
      overflow-y: scroll;
      text-align: left;
    }
  }
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw *  12 / 1920);
  right: 10px;
  height: 10px;
}

::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
  display: none !important;
}

::v-deep(.ql-blank) {
  display: none !important;
}

::v-deep(.ql-container.ql-snow) {
  border: 0;
}

.bmaiContainer {
  height: 100%;
  width: 100%;
}

.bmaiAiIframe {
  height: 100%;
  width: 100%;
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;
    // font-size: calc(100vw *  15 / 1920);

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.clbutton1 {
  // height: ;
  position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: 40px;
  left: 160px;
}

.clbutton3 {
  // height: ;
  // position: absolute;
  height: 30px;
  width: 100px;
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  float: right;
  margin: 10px 10px 0 0;
  // top: 300px;
  // left: 160px;
}

.clbutton2 {
  left: 39%;
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: 6px;
  /* 滚动条的宽度 */
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
  /* 滚动条的背景色 */
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
  /* 滚动条的滑块颜色 */
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 48px;

  .elcol-title-text {
    // float: left;
    padding-left: 10px;
    text-align: left;
    width: 40%;
    height: 100%;
  }

  .elcol-title-text2 {
    font-size: calc(100vw *  14 / 1920);
    color: #303133;
    // float: left;
    width: 75px;
  }

  .elcol-input {
    float: left;
    width: calc(60% - 75px);
    border: none !important;
  }
}

.fir-timeline {
  width: 100%;
  height: 158px;
  margin-top: 20px;
  margin-bottom: 0;
  overflow-y: scroll;
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: 20px;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

::v-deep(.el-textarea__inner) {
  padding: 16px;
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  background-color: #f9fafe;
  height: 20px;
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /* 不换行 */
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

::v-deep(.el-collapse) {
  border: 0px solid #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: 1px solid #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40/ 1080);
  border-radius: 4px;
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: 4px;
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: 4px;
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: "";
    left: -10px;
    top: 2px;
    width: 4px;
    height: 15px;
    background: #5585f0;
    border-radius: 2px;
  }
}

.elcol-title-left {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 20px;
  margin-left: 10px;
}

.highlight {
  background-color: yellow !important;
  cursor: pointer;
}
</style>