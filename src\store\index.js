import Vue from 'vue'
import Vuex from 'vuex'
import createVuexAlong from "vuex-along";
Vue.use(Vuex)
export default new Vuex.Store({
    state: {
        imgList: [],
        token: '',
        username: '',
        id: '',
        id1: '',
        datas: [],
        xb: '',
        admin: '',
        handle_name: '',
        check_id: '',
        selectedNavItem: '',
        selectedtopItem: '',
        grzxsedItem: '',
    },
    mutations: {
        setSelectedNavItem(state, item) {
            state.selectedNavItem = item;
        },
        setSelectedtopItem(state, item) {
            state.selectedtopItem = item;
        },
        sethandle_name(state, handle_name) {
            state.handle_name = handle_name;
        },
        setcheck_id(state, check_id) {
            state.check_id = check_id;
        },
        setUsername(state, username) {
            state.username = username;
        },
        setadmin(state, admin) {
            state.admin = admin;
        },
        setxb(state, xb) {
            state.xb = xb;
        },
        setcy(state, datas) {
            state.datas = datas;
        },
        addItem(state, value, index) { // img图片数量
            if (state.imgList.length >= 5) {
                state.imgList.shift(); // 删除数组中的第一个元素
            }
            state.imgList.push(value)
        },
        addNewToken(state, data) {
            state.token = data;
        },
        setId(state, id) { // 添加 setId mutation
            state.id = id;
        },
        setId1(state, id1) { // 添加 setId mutation
            state.id1 = id1;
        },
        // resetImgList(state) {
        //     state.imgList = [];
        // }
    },
    actions: {
        updatehandle_name({ commit }, handle_name) {
            commit('sethandle_name', handle_name);
        },
        //
        updateSelectedNavItem({ commit }, selectedNavItem) {
            commit('setSelectedNavItem', selectedNavItem);
        },
        // 
        updateSelectedtopItem({ commit }, selectedtopItem) {
            commit('setSelectedtopItem', selectedtopItem);
        },
        // 
        updateUsername({ commit }, username) {
            commit('setUsername', username);
        },
        updatecheck_id({ commit }, check_id) {
            commit('setcheck_id', check_id);
        },
        updateadmin({ commit }, data) {
            commit('setadmin', data);
        },
        updatexb({ commit }, xb) {
            commit('setxb', xb);
        },
        addItemAction({ commit }, item) {
            commit('addItem', item);
        },
        updateId({ commit }, id) { // 添加 setId action
            commit('setId', id);
        },
        updateId1({ commit }, id1) { // 添加 setId action
            commit('setId1', id1);
        },
        updatecy({ commit }, datas) { // 添加 setId action
            commit('setcy', datas);
        },
    },
    plugins: [
        // createVuexAlong({
        //     name: "hello-vuex-along", // 设置保存的集合名字，避免同站点下的多项目数据冲突
        //     local: {
        //         list: [""],
        //         isFilter: true // 过滤模块 ma 数据， 将其他的存入 localStorage
        //     },
        //     session: {
        //         list: ["count", "ma.a1"] // 保存 count 和模块 ma 中的 a1 到 sessionStorage
        //     }
        //     //如果对于sessionstorage不进行任何操作，也可将session设为false
        // })

        createVuexAlong({
            name: "hello-vuex-along",
            local: {
                list: ["token", "username", "id", "id1", "datas", "xb", "admin", "handle_name", "check_id", "selectedNavItem", "selectedtopItem"],
                isFilter: false
            },
            session: false
        })
    ],
    modules: {}
})