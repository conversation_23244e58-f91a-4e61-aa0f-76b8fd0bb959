<!-- 我的文档 -->
<template>
  <div class="work-container">
    <el-container>
      <el-header>
        <div class="ejdhlTitle">
          <!-- <img src="../img/logo05.png" width="100%" alt="" /> -->
          <!-- <img src="../assets/lo1.png" width="100%" alt="" /> -->
        </div>
        <div class="ai-header">
          <div class="ai-bar">
            <!-- <div class="ai-left-bar">
              <ul class="ai-left-bar-ul">
                <li class="ai-left-bar-li" @click="sfw111()">
                  <img src="../assets/icon-jj.png" alt="" />文
                </li>
                <li @click="clickTopTy" class="ai-left-bar-li">
                  <img src="../assets/icon15-h.png" alt="" />Ai读
                </li>
                <li class="ai-left-bar-li" @click="clickTopAi">
                  <img src="../assets/icon101.png" alt="" />AI写
                </li>
                <li @click="clickTopLg" class="ai-left-bar-li">
                  <img src="../assets/icon13-h.png" alt="" />Ai想
                </li>
                    <li @click="clickTopJq" class="ai-left-bar-li">
                  <img src="../assets/icon14-h.png" alt="" />Ai听
                </li>
                <li @click="clickTophy" class="ai-left-bar-li">
                  <img src="../assets/icon202a.png" alt="" />会议记录
                </li>
                <li @click="clickTopbmai" class="ai-left-bar-li">
                  <img src="../assets/icon204a.png" alt="" />助手
                </li>
              </ul>
            </div> -->
            <HeadNavigation />

   
          </div>
        </div>
      </el-header>
      <el-main>
        <div class="LgscCon">
          <div>
            <el-tabs class="centered-tabs" v-model="activeName">
              <el-tab-pane class="tab-item" name="a">
                <template #label>
                  <span class="custom-label">述职报告</span>
                </template>
                <!-- 表格 -->
                <el-table
                  :data="tableData1"
                  :header-cell-style="{
                    background: '#ebf2fb',
                  }"
                >
                  <el-table-column prop="userName" label="创建人" width="201">
                  </el-table-column>
                  <el-table-column prop="cjsj" label="创建时间" width="201">
                  </el-table-column>
                  <el-table-column
                    class="ellipsis"
                    label="文章内容"
                    width="400"
                  >
                    <template v-slot:default="scope">
                      <div
                        @click="dowload(scope.row)"
                        style="
                          max-height: calc(100vh *  50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        <div v-html="marked1(scope.row)"></div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop=""
                    label="操作"
                    width="200"
                    align="center"
                  >
                    <template v-slot="aa">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="showdia(aa.row)"
                        >查看文章详情</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-dialog
                title="文章详情"
                :visible.sync="dialogVisible"
                width="30%"
              >
                <div class="dialog-content" v-html="currentWznr"></div>
                <span slot="footer" class="dialog-footer">
                  <el-button @click="dialogVisible = false">关闭</el-button>
                </span>
              </el-dialog>
              <el-tab-pane class="tab-item" name="b">
                <template #label>
                  <span class="custom-label">心得体会</span>
                </template>
                <el-table
                  :data="tableData2"
                  :header-cell-style="{
                    background: '#ebf2fb',
                    // width: '1700px',
                  }"
                >
                  <el-table-column prop="userName" label="创建人" width="201">
                  </el-table-column>
                  <el-table-column prop="cjsj" label="创建时间" width="201">
                  </el-table-column>
                  <el-table-column
                    class="ellipsis"
                    label="文章内容"
                    width="400"
                  >
                    <template v-slot:default="scope">
                      <div
                        @click="dowload(scope.row)"
                        style="
                          max-height: calc(100vh *  50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        <div v-html="marked1(scope.row)"></div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop=""
                    label="操作"
                    width="200"
                    align="center"
                  >
                    <template v-slot="a">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="showdia(a.row)"
                        >查看文章详情</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane class="tab-item" name="c">
                <template #label>
                  <span class="custom-label">领导讲话</span>
                </template>
                <el-table
                  :data="tableData3"
                  :header-cell-style="{
                    background: '#ebf2fb',
                    // width: '1700px',
                  }"
                >
                  <el-table-column prop="userName" label="创建人" width="201">
                  </el-table-column>
                  <el-table-column prop="cjsj" label="创建时间" width="201">
                  </el-table-column>
                  <el-table-column
                    class="ellipsis"
                    label="文章内容"
                    width="400"
                  >
                    <template v-slot:default="scope">
                      <div
                        @click="dowload(scope.row)"
                        style="
                          max-height: calc(100vh *  50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        <div v-html="marked1(scope.row)"></div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop=""
                    label="操作"
                    width="200"
                    align="center"
                  >
                    <template v-slot="a">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="showdia(a.row)"
                        >查看文章详情</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane class="tab-item" name="d">
                <template #label>
                  <span class="custom-label">工作方案</span>
                </template>
                <el-table
                  :data="tableData4"
                  :header-cell-style="{
                    background: '#ebf2fb',
                    // width: '1700px',
                  }"
                >
                  <el-table-column prop="userName" label="创建人" width="201">
                  </el-table-column>
                  <el-table-column prop="cjsj" label="创建时间" width="201">
                  </el-table-column>
                  <el-table-column
                    class="ellipsis"
                    label="文章内容"
                    width="400"
                  >
                    <template v-slot:default="scope">
                      <div
                        @click="dowload(scope.row)"
                        style="
                          max-height: calc(100vh *  50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        <div v-html="marked1(scope.row)"></div>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop=""
                    label="操作"
                    width="200"
                    align="center"
                  >
                    <template v-slot="a">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="showdia(a.row)"
                        >查看文章详情</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane class="tab-item" name="e">
                <template #label>
                  <span class="custom-label">调研报告</span>
                </template>
                <el-table
                  :data="tableData5"
                  :header-cell-style="{
                    background: '#ebf2fb',
                    // width: '1700px',
                  }"
                >
                  <el-table-column prop="userName" label="创建人" width="201">
                  </el-table-column>
                  <el-table-column prop="cjsj" label="创建时间" width="201">
                  </el-table-column>
                  <el-table-column
                    class="ellipsis"
                    label="文章内容"
                    width="400"
                  >
                    <template v-slot:default="scope">
                      <div
                        @click="dowload(scope.row)"
                        style="
                          max-height: calc(100vh *  50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        <div v-html="marked1(scope.row)"></div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop=""
                    label="操作"
                    width="200"
                    align="center"
                  >
                    <template v-slot="a">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="showdia(a.row)"
                        >查看文章详情</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane class="tab-item" name="f">
                <template #label>
                  <span class="custom-label">宣传材料</span>
                </template>
                <el-table
                  :data="tableData6"
                  :header-cell-style="{
                    background: '#ebf2fb',
                    // width: '1700px',
                  }"
                >
                  <el-table-column prop="userName" label="创建人" width="201">
                  </el-table-column>
                  <el-table-column prop="cjsj" label="创建时间" width="201">
                  </el-table-column>
                  <el-table-column
                    class="ellipsis"
                    label="文章内容"
                    width="400"
                  >
                    <template v-slot:default="scope">
                      <div
                        @click="dowload(scope.row)"
                        style="
                          max-height: calc(100vh *  50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        <div v-html="marked1(scope.row)"></div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop=""
                    label="操作"
                    width="200"
                    align="center"
                  >
                    <template v-slot="a">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="showdia(a.row)"
                        >查看文章详情</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
               <el-tab-pane class="tab-item" name="g">
                <template #label>
                  <span class="custom-label">公文写作</span>
                </template>
                <el-table
                  :data="tableData7"
                  :header-cell-style="{
                    background: '#ebf2fb',
                    // width: '1700px',
                  }"
                >
                  <el-table-column prop="userName" label="创建人" width="201">
                  </el-table-column>
                  <el-table-column prop="cjsj" label="创建时间" width="201">
                  </el-table-column>
                  <el-table-column
                    class="ellipsis"
                    label="文章内容"
                    width="400"
                  >
                    <template v-slot:default="scope">
                      <div
                        @click="dowload(scope.row)"
                        style="
                          max-height: calc(100vh *  50 / 1080);
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        <div v-html="marked1(scope.row)"></div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop=""
                    label="操作"
                    width="200"
                    align="center"
                  >
                    <template v-slot="a">
                      <el-button
                        class="el-icon-view"
                        type="text"
                        size="medium"
                        @click="showdia(a.row)"
                        >查看文章详情</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>

          <div class="nrCon">
            <div class="nrConLeft">
              <div
                class="nrConLeftBk"
                v-for="(item, index) in this.nrArr"
                :key="index"
                v-if="index % 2 == 0"
              >
                <div class="bkTitle">
                  {{ item.t1 }}
                </div>
                <div class="bkCon">
                  {{ item.t2 }}
                </div>
                <div class="bkBtn flexLd">
                  <div class="bkBtnLeft flexcz">
                    <div class="xztg flex">
                      {{ item.t3 }}
                    </div>
                    <div class="qt flex" v-if="item.t4 != ''">
                      {{ item.t4 }}
                    </div>
                  </div>
                  <div class="bkBtnright flexLd">
                    <div
                      class="fz flex"
                      v-clipboard:copy="item.t2"
                      v-clipboard:success="onCopySuccess"
                      v-clipboard:error="onCopyError"
                    >
                      <img src="../assets/icon-30.png" alt="" />
                      <p>复制</p>
                    </div>
                    <!-- <div class="sc flex">
                      <img src="../assets/icon-311.png" alt="" />
                      <p>收藏</p>
                    </div> -->
                  </div>
                </div>
              </div>
            </div>

            <div class="nrConRight floatLeft">
              <div
                class="nrConLeftBk"
                v-for="(item, index) in this.nrArr"
                :key="index"
                v-if="index % 2 != 0"
              >
                <div class="bkTitle">
                  {{ item.t1 }}
                </div>
                <div class="bkCon">
                  {{ item.t2 }}
                </div>
                <div class="bkBtn flexLd">
                  <div class="bkBtnLeft flexcz">
                    <div class="xztg flex">
                      <!-- 写作提纲 -->
                      {{ item.t3 }}
                    </div>
                    <div class="qt flex" v-if="item.t4 != ''">
                      <!-- 其他 -->
                      {{ item.t4 }}
                    </div>
                  </div>
                  <div
                    class="bkBtnright flexLd"
                    v-clipboard:copy="item.t2"
                    v-clipboard:success="onCopySuccess"
                    v-clipboard:error="onCopyError"
                  >
                    <div class="fz flex">
                      <img src="../assets/icon-30.png" alt="" />
                      <p>复制</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import HeadNavigation from "../components/HeadNavigation.vue"; // 引入头部导航组件

import { getTitle, getTitle2, getBody, getwz, d_w } from "../api/home.js"; // 接口请求
import { search } from "../api/home3.js"; // 接口请求
import store from "../store/index";
import { VueEditor } from "vue2-editor";
// import { marked } from 'marked';
import { EventBus } from '../eventBus'; // 引入事件总线
import { mapState } from "vuex";

 export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id

    username() {
      return this.$store.state.username;
      
    },
  },
  data() {
    return {
      currentWznr: "",
      dialogVisible: false,
      params1: {
        userName: "",
        wzlx: "述职报告",
        cjrid: '',
      },
      params2: {
        userName: "",
        wzlx: "心得体会",
        cjrid: '',

      },
      params3: {
        userName: "",
        wzlx: "领导讲话",
        cjrid: '',

      },
      params4: {
        userName: "",
        wzlx: "工作方案",
        cjrid: '',

      },
      params5: {
        userName: "",
        wzlx: "调研报告",
        cjrid: '',

      },
      params6: {
        userName: "",
        wzlx: "宣传材料",
        cjrid: '',

      },
            params7: {
        userName: "",
        wzlx: "公文写作",
        cjrid: '',

      },
      tableData1: [],
      tableData2: [],
      tableData3: [],
      tableData4: [],
      tableData5: [],
      tableData6: [],
      tableData7: [],

      activeName: "a",
      array2: "",
      inputval: "",
      navShow: true,
      flpd: "",
      ztpd: "",
      input: "",
      flArr: [],
      ztArr: [],
      nrArr: [],
      flmc: "",
      ztmc: "",
    };
  },
    components: {
    // App,
    // 注册组件
    // CustomSteps,
    // VueEditor,
    // Wordcloud,
    HeadNavigation,

  },
   watch: {
    '$route'(to, from) {
      // 路由变化时调用获取数据的方法
      this.getwz1();
      this.getwz2();
      this.getwz3();
      this.getwz4();
      this.getwz5();
      this.getwz6();
        //  this.getwz6();
    this.getwz7();


    },

   },
   created() {
    EventBus.$on('updateDocument', () => {
      this.getwz1(); 
      this.getwz2(); 
       this.getwz3();
        this.getwz4();
         this.getwz5();
            this.getwz6();
    this.getwz7();

    });
  },
  beforeDestroy() {

  },
  mounted() {
    this.setParams();
    this.getwz1();
    this.getwz2();
    this.getwz3();
    this.getwz4();
    this.getwz5();
    this.getwz6();
    this.getwz7();

  },
        methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
       toxg() {
      // alert(1)
      this.$router.push("/grzx4");
    },
    togrzx() {
      this.$router.push("/grzx1");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
    sfw111() {
      this.$router.push("/sfw");
    },
    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach((line) => {
        // 处理标题
        // 处理标题
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith("-")) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "111111111111111111111111111111111111");
      this.currentWznr = html;
      // return html;
    },
    marked1(c1) {
      const lines = c1.wznr.split("\n");
      let html = "";
      let inList = false;
      let isFirstHeading = true;
      lines.forEach((line) => {
        if (line.startsWith("#")) {
          const level = line.indexOf(" ");
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${content}</h${level}>`;
            isFirstHeading = false;
          } else {
            html += `<h${level} style="text-align: left; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${content}</h${level}>`;
          }
        } else if (line.startsWith("-")) {
          if (!inList) {
            html +=
              '<ul style="text-align: left; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li style="max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${content}</li>`;
        } else {
          if (inList) {
            html += "</ul>";
            inList = false;
          }
          html += `<p style="text-align: left; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${line}</p>`;
        }
      });
      if (inList) {
        html += "</ul>";
      }
      console.log(html, "222222222222");
      return html;
    },

    dowload(c1) {
      const markdownContent = c1.wznr;
      const blob = new Blob([markdownContent], {
        type: "text/markdown;charset=utf-8",
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "文章内容.md"; // 设置默认文件名
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      this.dialogVisible = false;
    },

    showdia(c1) {
      this.currentWznr = c1.wznr;
      this.marked(this.currentWznr);
      this.dialogVisible = true;
    },
    clickTopLg() {
      this.$router.push("/Lgsc");
    },
    clickTopNav() {
      this.$router.push("/Lgsc");
    },
    clickTopAi() {
      this.$router.push("/index");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    setParams() {
      this.params1.userName = this.username;
      this.params1.cjrid = this.id;
      this.params1.userName = this.username;

      this.params2.userName = this.username;
      this.params2.cjrid = this.id;

      this.params3.userName = this.username;
        this.params3.cjrid = this.id;
      this.params4.userName = this.username;
      this.params4.cjrid = this.id;
      this.params5.userName = this.username;
         this.params5.cjrid = this.id;
      this.params6.userName = this.username;
        this.params6.cjrid = this.id;
              this.params7.userName = this.username;
        this.params7.cjrid = this.id;
    },
    async getwz1() {

      let res1 = await getwz(this.params1);

      if (res1.data.status_code == 200) {
        this.tableData1 = res1.data.data;

        // 弹出提示
        // this.$message({
        //   message: res1.data.message,
        //   type: "success",
        // });
      } 
      // else if (res1.data.status_code == 500) {
      //   this.$message({
      //     message: res1.data.message,
      //     type: "error",
      //   });
      // }
    },
    async getwz2() {
      let res2 = await getwz(this.params2);
      // if (res2.data.status_code == 200) {
      // 弹出提示
      this.tableData2 = res2.data.data;
      //   this.$message({
      //     message: res2.data.message,
      //     type: 'success'
      //   });

      // }
      // else if (res2.data.status_code == 500) {

      //   this.$message({
      //     message: res2.data.message,
      //     type: 'error'
      //   })
      // }
    },
    async getwz3() {
      let res3 = await getwz(this.params3);
      // if (res3.data.status_code == 200) {
      this.tableData3 = res3.data.data;
      //   this.$message({
      //     message: res3.data.message,
      //     type: 'success'
      //   });

      // }

      // }
    },
    async getwz4() {
      let res4 = await getwz(this.params4);
      this.tableData4 = res4.data.data;
    },
    async getwz5() {
      let res5 = await getwz(this.params5);
      this.tableData5 = res5.data.data;
    },
    async getwz6() {
      let res6 = await getwz(this.params6);
      this.tableData6 = res6.data.data;
    },
       async getwz7() {
      let res7 = await getwz(this.params7);
      this.tableData7 = res7.data.data;
    },
    // wdwd() {
    //     // alert(1)
    //     this.$router.push("/wdwd");
    //   },
    logout() {
      clearVuexAlong();
      store.commit("addNewToken", "");
      this.$router.push("/login");
    },
    // 路由跳转时强制刷新界面
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        // 强制刷新页面
        vm.$forceUpdate();
      });
    },
  },
};
</script>



<style lang="less" scoped>
.ellipsis-container {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-content {
  display: inline;
}

.ellipsis .cell {
  height: calc(100vh *  50 / 1080);
  /* 你可以根据需要调整高度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 移除悬停展示的样式 */
.ellipsis .cell:hover {
  /* 移除或覆盖悬停展示的样式 */
  text-overflow: ellipsis;
  /* 确保悬停时不展示完整内容 */
}

.custom-label {
  margin-left: 50px;
  font-weight: 600;
  font-size: calc(100vw *  18 / 1920);
  letter-spacing: 0px;
  line-height: 16px;
  //  active时的样式
  // :active{
  // color:#fff;
  // background-color:#3a6bc6;
  // border-radius:20px;
  // }
}

.tableCon {
  .ellipsis {
    height: 200px;
  }
}

.highlight {
  background-color: yellow;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-image: linear-gradient(76deg,
  //     #07389c 0%,
  //     #3d86d1 0%,
  //     #3448b3 100%);

  img {
    width: 120px;
    height: 48px;
  }

  p {
    border: 1.54px solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}

.el-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  padding: 0;

  .ai-header {
    width: calc(100% - 300px);

    .ai-bar {
      width: calc(100% - 300px);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vh * 40 / 1080);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: 1px solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vh * 130 / 1080);
            color: #000;
            font-size: calc(100vh * 14 / 1080);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vh * 14 / 1080);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vh * 16 / 1080);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vh * 14 / 1080);
            }

            &:hover {
              background-image: linear-gradient(
                107deg,
                #3a6bc6 0%,
                #488aff 100%
              );
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }

          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: 93px;
        height: calc(100vh * 80 / 1080);
        // column-gap: 16px;
        // height: 100%;
        // margin-left: 30px;

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
          }
        }

        .btn {
          margin-left: 30px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw *  12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            float: left;
          }

          p {
            float: left;
            margin-left: 22px;
            margin-top: -2px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw *  14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  background-color: #f8f9fd;
  background: url(../assets/img-bg.png) no-repeat center;
  background-size: 100% 100%;
  color: #333;
  text-align: center;
  // line-height: 160px;
  width: 100%;
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 /1080);
  padding: 20px 10px;
  // overflow: hidden;
}

.LgscCon {
  width: 1002px;
  height: auto;
  margin: 0 auto;

  .button {
    width: 108px;
    height: 58px;
    background: #4c91ff;
    border-radius: 0px 100px 100px 0px;
    cursor: pointer;
  }

  img {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }

  p {
    line-height: 58px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flexcz {
  display: flex;
  align-items: center;
}

.flexNoCz {
  display: flex;
  justify-content: center;
}

.floatLeft {
  float: left;
}

.lgfl {
  width: 100%;
  height: calc(100vh * 40/ 1080);
  // background: #000;
  margin-top: 42px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  // font-weight: 600;
  display: flex;
  align-items: center;
}

.lgfl1 {
  margin-top: 10px;
}

.mgr1 {
  margin-right: 26px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  // font-weight: 600;
  // cursor: pointer;
}

.mgr {
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  // font-weight: 600;
  width: 96px;
  margin-right: 26px;
  cursor: pointer;

  &:hover {
    width: 96px;
    height: 100%;
    background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
    border-radius: 20px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    // font-weight: 600;
  }

  &:active {
    width: 96px;
    height: 100%;
    background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
    border-radius: 20px;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.activeFl {
  width: 96px;
  height: 100%;
  background-image: linear-gradient(113deg, #3a6bc6 0%, #488aff 100%);
  border-radius: 20px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw *  14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  // font-weight: 600;
}

.nrCon {
  margin-top: 30px;
  width: 100%;
  height: calc(100vh * 750 / 1080);
  overflow-y: scroll;

  .nrConLeft {
    width: calc(50% - 10px);
    height: auto;
    float: left;
    // display: flex;
    // flex-flow: row wrap;
    // // align-items: flex-start;
    // // flex-basis: auto;
    // align-items: flex-start;
    // margin-right: 20px;
  }

  .nrConRight {
    width: calc(50% - 10px);
    height: auto;
    float: right;
  }
}

.nrConLeftBk {
  width: auto;
  height: auto;
  background: #ffffff;
  box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.13);
  border-radius: 12px;
  padding: 28px 40px;
  position: relative;
  margin-bottom: 20px;

  .bkTitle {
    height: auto;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    letter-spacing: 0;
    text-align: left;
    // font-weight: 600;
  }

  .bkCon {
    height: auto;
    margin-top: 19px;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw *  16 / 1920);
    color: #333333;
    text-align: left;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
  }

  .bkBtn {
    width: 100%;
    margin-top: 16px;
    height: auto;

    // background: #000;
    .bkBtnLeft {
      width: 50%;
      height: 24px;

      .xztg {
        width: auto;
        height: 24px;
        background: #f5f8fc;
        border-radius: 12px;
        padding: 0 10.5px;
        font-family: PingFangSC-Regular;
        font-size: calc(100vw *  14 / 1920);
        color: #7b7e9e;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        cursor: pointer;
      }

      .qt {
        width: auto;
        height: 24px;
        background: #f5f8fc;
        border-radius: 12px;
        padding: 0 10.5px;
        font-family: PingFangSC-Regular;
        font-size: calc(100vw *  14 / 1920);
        color: #7b7e9e;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        margin-left: 10px;
        cursor: pointer;
      }
    }

    .bkBtnright {
      // width: 131px; //复制加收藏
      width: 54px;
      height: 24px;

      .fz {
        width: 53px;
        height: 24px;
        cursor: pointer;

        img {
          width: 16px;
          height: 16px;
        }

        p {
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw *  14 / 1920);
          color: #666666;
          letter-spacing: 0;
          line-height: 24px;
          font-weight: 500;
        }
      }

      .sc {
        width: 53px;
        height: 24px;
        cursor: pointer;

        img {
          width: 16px;
          height: 16px;
        }

        p {
          font-family: SourceHanSansSC-Medium;
          font-size: calc(100vw *  14 / 1920);
          color: #666666;
          letter-spacing: 0;
          line-height: 24px;
          font-weight: 500;
        }
      }
    }
  }
}

.flexLd {
  display: flex;
  justify-content: space-between;
}

::v-deep(.el-input__inner) {
  height: 58px;
  border-radius: 0;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  14 / 1920);
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}

.user-menu-content {
  width: calc(100vw *  246 / 1920);
  height: 24px;
  padding: 0 20px;
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 17px;

    .avatar-wrapper {
      position: relative;
      width: 52px;
      height: 52px;
      border: 1px solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: 52px;
        height: 52px;
      }
    }

    .name-wrapper {
      width: 300px;
      display: flex;
      flex-direction: column;
      margin-left: 12px;

      .name {
        width: 300px;
        color: #222;
        font-weight: 600;
        // font-size: calc(100vw *  16 / 1920);
        font-size: calc(100vw *  15 / 1920);
        letter-spacing: 0px;
        line-height: 16px;
      }

      .id {
        margin-top: 7px;
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw *  12 / 1920);
        letter-spacing: 0px;
        line-height: 12px;
      }
    }
  }

  .divider {
    width: 100%;
    margin: 20px 0 18px;
    border-bottom: 1px solid #f0f3fa;
  }

  .options {
    .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: 20px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 6px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw *  12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-loading-spinner) {
  /*这个是自己想设置的 gif 加载动图*/
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: 150px 150px;
  height: 100px;
  width: 100%;
  background-position: center;
  /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  /*为了使得文字在loading图下面*/
  margin: 85px 0px;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(-20px) translateX(-16px);
  transform-origin: 0 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(-0px);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0px;
  width: 0px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: 30px;
  width: 1px;
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: 14px !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
  // ::v-deep(.el-step__icon) {
  //   background-color: #1B2126 !important;
  // }
  // border-color: #1B2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.dialog-content {
  height: 400px;
  overflow-y: scroll;
}
</style>