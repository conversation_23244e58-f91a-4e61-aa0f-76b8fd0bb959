<template>
	<div id="app">
	  <baseLabel  v-on:focus.native="onFocus"/>
	</div>
  </template>
  
  <script>
  import baseLabel from './com/HelloWorld.vue'
  
  export default {
	name: 'app',
	components: {
	  baseLabel
	},
	      methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
	  onFocus() {
		console.log('lalalalal');
	  }
	}
  }
  </script>
  
  