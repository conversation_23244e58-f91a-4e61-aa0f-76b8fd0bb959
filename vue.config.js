const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
    transpileDependencies: true,
    lintOnSave: false,
    devServer: {
        allowedHosts: 'all',
        port: 3000,  // 启动端口号
        proxy: {
            '/api': { // 请求接口中要替换的标识吗
                // target: 'http://**********:180/new_api/',
                target: 'http://*************:5020',
                // target: 'http://**************:5010',
                changeOrigin: true, // 是否允许跨域
                pathRewrite: {
                    '^/api': '' // 这里理解成用‘/api’代替target里面的地址，后面组件中我们掉接口时直接用api代替 比如我要调用'http://*************:3002/user/add'，直接写‘/api/user/add’即可
                }
            },
            '/api2': { // 请求接口中要替换的标识吗
                target: 'http://**********:180/officeAI/',
                changeOrigin: true, // 是否允许跨域
                pathRewrite: {
                    '^/api2': '' // 这里理解成用‘/api’代替target里面的地址，后面组件中我们掉接口时直接用api代替 比如我要调用'http://*************:3002/user/add'，直接写‘/api/user/add’即可
                }
            },
        }
    }
})
