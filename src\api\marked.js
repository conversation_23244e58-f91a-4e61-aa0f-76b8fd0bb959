const marked = require('marked');
// 简化版的 marked.js
//  marked(markdown) {
//     // 将 Markdown 文本按行分割
//     const lines = markdown.split('\n');
//     let html = '';
//     let inList = false;

//     lines.forEach(line => {
//         // 处理标题
//         if (line.startsWith('#')) {
//             const level = line.indexOf(' ');
//             const content = line.slice(level + 1);
//             html += `<h${level}>${content}</h${level}>`;
//         }
//         // 处理无序列表
//         else if (line.startsWith('-')) {
//             if (!inList) {
//                 html += '<ul>';
//                 inList = true;
//             }
//             const content = line.slice(2);
//             html += `<li>${content}</li>`;
//         }
//         // 处理段落
//         else {
//             if (inList) {
//                 html += '</ul>';
//                 inList = false;
//             }
//             html += `<p>${line}</p>`;
//         }
//     });

//     // 如果列表没有关闭，关闭它
//     if (inList) {
//         html += '</ul>';
//     }

//     return html;
// }

// 示例用法
const markdown = `
  # Heading 1
  
  ## Heading 2
  
  This is a paragraph.
  
  - List item 1
  - List item 2
  - List item 3
  
  **Bold Text**
  `;

const html = marked(markdown);
console.log(html);


module.exports = marked;