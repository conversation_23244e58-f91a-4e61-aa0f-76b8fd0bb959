<!-- ai写1 -->
<template>
 <div class="work-container" v-loading="con_loading">
    <el-drawer title="" :visible.sync="drawer" :direction="direction" style="width: 80%;"
      :before-close="handleClosedrawer">
      <div style="display: flex; height: 94%;">
        <div style="    width: 50%;
    height: calc(100% - 30px);
    margin-top: 20px;">
        </div>
        <div class="result-container" style="width: calc(50% - 40px);
            height: calc(100% - 30px);
            margin: 20px;padding-top:16px">
          <div class="result-title" style="text-align: left;font-size: calc(100vw *  16 / 1920);margin-bottom: 10px;font-weight: bold;">
            全部校对结果</div>
          <div style="height: 96%; overflow: overlay;" class="result-content">
            <div v-for="(item, index) in correctionList">
              <el-collapse v-model="activeNames" @change="highlightError(item.source, index)"
                style="margin-bottom: 10px;" accordion>
                <el-collapse-item :name="index">
                  <template slot="title">
                    <div style="width: 96%;">
                      <div class="elcol-title">
                        <div class="elcol-title-left" :style="{ backgroundColor: getBackgroundColor(index) }"></div>
                        <!-- <p class="elcol-title-text">{{ item.source }}</p> -->
                        <el-input class="elcol-input" v-model="item.source" placeholder="请输入内容"></el-input>
                        <p class="elcol-title-text2">建议替换</p>
                        <!-- <p class="elcol-title-text">{{ item.target }}</p> -->
                        <el-input class="elcol-input" v-model="item.target" placeholder="请输入内容"></el-input>
                      </div>
                    </div>
                  </template>
                  <div
                    style="height: calc(100vh * 40/ 1080);text-align: left;padding-left: 20px;line-height: calc(100vh * 40/ 1080);border-bottom: 1px solid #e1e7f3;">
                    拼写：政治语素错误</div>
                  <!-- <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div> -->
                  <div style="height: 8px;margin-top: 6px;">
                    <span @click="ignore()"
                      style="float: right;margin-right: 10px;color: red;cursor: pointer;">忽略</span>
                    <span @click="highlightChange(item.source, item.target)"
                      style="float: right;margin-right: 10px;color: #66b1ff;cursor: pointer;">替换</span>
                  </div>

                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div type="primary" class="fr clbutton1" style="margin-right: 20px;cursor: pointer;" @click="save">关 闭</div>
        <!-- <el-button type="danger" @click="fanhui"></el-button> -->
      </div>
    </el-drawer>
    <div :style="navShow ? 'width: 300px' : 'width: 136px'" class="ejdhl">
      <!-- <el-button @click="drawer = true" type="primary" style="margin-left: 16px;">
        点我打开
      </el-button> -->
      <div class="ejdhlTitle">
        <!-- <img src="../assets/lo.png" alt="" />
        <p v-if="navShow">思盒</p> -->
        <img src="../img/logo05.png" width="100%" alt="" />
                <!-- <img src="../assets/lo1.png" width="100%" alt="" /> -->

      </div>
      <div class="ai-nav" :style="navShow ? 'width: 300px' : 'width: 64px'">
        <p class="title" v-if="navShow">请选择公文文种</p>
        <div class="nav-list">
          <div class="nav-item" @click="clickTopInd" :style="navShow ? 'width: 272px' : 'width: 50px'">
            <img src="../assets/icon01.png" alt="" />
            <p v-if="navShow">述职报告</p>
          </div>
          <!-- <div class="nav-item" @click="clickTopGz" :style="navShow ? 'width: 272px' : 'width: 50px'">
            <img src="../assets/icon02.png" alt="" />
            <p v-if="navShow">工作总结</p>
          </div> -->
          <div class="nav-item" @click="clickTopxd" :style="navShow ? 'width: 272px' : 'width: 50px'">
            <img src="../assets/icon03.png" alt="" />
            <p v-if="navShow">心得体会</p>
          </div>
          <div class="nav-item" @click="clickTopLd" :style="navShow ? 'width: 272px' : 'width: 50px'">
            <img src="../assets/icon04.png" alt="" />
            <p v-if="navShow">领导讲话</p>
          </div>
          <div class="nav-item" @click="clickTopFa" :style="navShow ? 'width: 272px' : 'width: 50px'">
            <img src="../assets/icon05.png" alt="" />
            <p v-if="navShow">工作方案</p>
          </div>
          <div class="nav-item" @click="clickTopDy" :style="navShow ? 'width: 272px' : 'width: 50px'">
            <img src="../assets/icon06.png" alt="" />
            <p v-if="navShow">调研报告</p>
          </div>
          <div class="nav-item" @click="clickTopXc" :style="navShow ? 'width: 272px' : 'width: 50px'">
            <img src="../assets/icon07.png" alt="" />
            <p v-if="navShow">宣传材料</p>
          </div>
          <div class="nav-item choose" :style="navShow ? 'width: 272px' : 'width: 50px'">
            <img src="../assets/icon08.png" alt="" />
            <p v-if="navShow">+其他</p>
          </div>
        </div>
        <div :class="navShow ? 'showUp' : 'showUp1'" :style="navShow ? 'width: 170px' : 'width: 50px'" @click="upShow">
          <img v-if="navShow" src="../assets/icon09.png" alt="" />
          <img v-else src="../assets/img-00.png" alt="" />
          <p v-if="navShow">向左收起</p>
        </div>
      </div>
    </div>
    <el-container>
      <el-header style="transition: ease-out 0.4s" :style="navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
    ">
        <div class="ai-header">
          <div class="ai-bar">
            <div class="ai-left-bar">
              <ul class="ai-left-bar-ul">
                 <li @click="clickTopsfw" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />文
                </li>
               <!-- <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
                <li @click="clickTopTy" class="ai-left-bar-li">
                  <img src="../assets/icon15-h.png" alt="" />Ai读
                </li>
                <li class="ai-left-bar-li actived">
                  <img src="../assets/icon10.png" alt="" />AI写
                </li>
              
                <li @click="clickTopLg" class="ai-left-bar-li">
                  <img src="../assets/icon13-h.png" alt="" />Ai想
                </li>
                <li @click="clickTopJq" class="ai-left-bar-li">
                  <img src="../assets/icon14-h.png" alt="" />Ai听
                </li>

                <!-- <li @click="clickTopFz" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />辅助定密
                </li> -->
                <li @click="clickTopbmai" class="ai-left-bar-li"><img src="../assets/icon204a.png" alt="" />助手 </li>
                <!-- <li @click="clickTopsfw" class="ai-left-bar-li">
                  <img src="../assets/icon-jj.png" alt="" />收文
                </li>
               <li @click="clickTopfw" class="ai-left-bar-li">
                  <img src="../assets/icon201a.png" alt="" />发文
                </li> -->
              </ul>
            </div>
            <div class="ai-right-bar">
             <!-- <div class="top-button" @click="clickTopNav">
               
                <img src="../assets/icon16.png" alt="" />
              </div> -->
              <!-- <div class="top-button btn" @click="clickTopNav">
               
                <img src="../assets/icon17.png" alt="" />
              </div> -->
              <el-dropdown :hide-on-click="false" trigger="click">
                <div class="ai-avant btn">
                  <img src="../assets/icon18.png" alt="" />
                  <p>{{ username }}</p>
                </div>
                <el-dropdown-menu slot="dropdown" style="height: 50%;width:20%;margin-right: -50px;">

                  <div class="user-menu-content">
                    <div class="user-info">
                      <div class="avatar-wrapper">
                        <img src="../assets/icon_tx.png" alt="" />
                      </div>
                      <div class="name-wrapper">
                        <div class="name">{{ username }}</div>
                        <div class="id">ID：001</div>
                      </div>
                    </div>
                    <div class="divider"></div>
                     <div class="options">
                       <div rel="opener" class="option" href="" target="_blank" @click="togrzx()">
                        <div class="icon personal-center"></div>
                        <div class="text">个人中心</div>
                      </div>
                       <div rel="opener" class="option1" href="" target="_blank" @click="toxg()">
                        <div class="el-icon-edit"></div>
                        <div class="text">修改密码</div>
                      </div>
                      <div
                        rel="opener"
                        class="option"
                        href=""
                        target="_blank"
                        @click="wdwd()"
                      >
                        <div class="icon my-document"></div>
                        <div class="text">我的文档</div>
                      </div>
                      <!-- <a rel="opener" class="option" href="" target="_blank">
                        <div class="icon my-favourite"></div>
                        <div class="text">我的收藏</div>
                      </a> -->
                      <a class="option" @click="logout()">
                        <div class="icon logout"></div>
                        <div class="text">退出登录</div>
                      </a>
                    </div>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </el-header>
      <el-main style="transition: ease-out 0.4s" :style="navShow ? 'width: calc(100% - 300px)' : 'width: calc(100% - 64px);'
    ">
        <div class="ai-gai" v-loading="loading" element-loading-text="生成中，请稍候..."
          element-loading-background="rgba(255, 255, 255, 0.7)"
          :style="navShow ? 'width: 870px; margin-left:900px;height:500px;margin-bottom: 150px;' : 'width: 528px;'"
          style="transition: ease-out 0.4s" v-if="mask"></div>
        <div class="ai-body">
 <div class="ai-body-top" style="display: flex; align-items: center; flex-wrap: wrap;">
  <!-- 这个标签点击的时候调接口然后变颜色 -->
  <el-tag
  style="cursor: pointer;"
   :key="tag.genre_id" 
    v-for="tag in tags1"
    closable
    :disable-transitions="false"
    @close="handleClosetop(tag.genre_id)">
    {{
tag.genre_name
}}
  </el-tag>
  <el-input
    v-if="inputVisibletop"
    v-model="form.name"
    ref="saveTagInputtop"
    size="small"
    style="margin-left: 5px;width: 150px;margin-top: -5px;" 
    @keyup.enter.native="handleInputConfirmtop"
    @blur="handleInputConfirmtop"
  >
  </el-input>

  <el-button v-if="!inputVisibletop" size="small" style="margin-left: 5px;margin-top: -5px;" @click="showInput">+新增文种</el-button>
  <!-- <el-button  size="small" style="margin-left: 15px;margin-top: -5px;    background-image: linear-gradient(107deg, rgb(58, 107, 198) 0%, rgb(72, 138, 255) 100%);
    color: rgb(255, 255, 255);border-radius: 20px;" >保 存 修 改</el-button> -->
</div>
   <div class="ai-body-content">
          <div class="ai-body-left">
            <div class="ai-body-left-top">
              <div v-if="jydis">
                <custom-steps :steps="steps" :current-step="currentStep"></custom-steps>         
                <div class="ai-tab">
                  <div @click="clickTab('1')" class="tab-item" :class="{ activedTab: curIndex == 1 }">
                    基本信息
                  </div>
                </div>
                <div class="tab-item-fir" v-if="curIndex === '1'">
                  <div class="fir-title">
                    <div class="fir-kuai"></div>
                    <p class="fir-title-p">{{}}</p>
                  </div>
                  <el-input class="fir-textarea" type="textarea" placeholder="" v-model="textarea"
                    show-word-limit>
                  </el-input>
                </div>
              </div>
              <div class="tab-item-fir" v-else>
                <div style="height: 100%; overflow: overlay; margin-top: 20px" class="result-content">
                  <div v-for="(item, index) in correctionList">
                    <el-collapse v-model="activeNames" @change="highlightError(item.source, item.wordNo)"
                      style="margin-bottom: 10px;" accordion>
                      <el-collapse-item :name="index">
                        <template slot="title">
                          <div style="width: 96%;">
                            <div class="elcol-title" style="display: flex;">
                              <div class="elcol-title-left" :style="{ backgroundColor: getBackgroundColor(index) }">
                              </div>
                              <p class="elcol-title-text elcol-input">{{ item.wordNo }}</p>
                              <!-- <el-input :title="item.wordNo" class="elcol-input" v-model="item.wordNo"
                                placeholder="请输入内容" style="width: 40%;"></el-input> -->
                              <p class="elcol-title-text2">建议替换</p>
                              <!-- <p class="elcol-title-text">{{ item.target }}</p> -->
                              <el-input :title="item.wordYes" class="elcol-input" v-model="item.wordYes"
                                placeholder="请输入内容" style="width: 40%;"></el-input>
                            </div>
                          </div>
                        </template>
                        <div
                          style="height: calc(100vh * 40/ 1080);text-align: left;padding-left: 20px;line-height: calc(100vh * 40/ 1080);border-bottom: 1px solid #e1e7f3;">
                          {{ item.eq }}</div>
                        <!-- <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div> -->
                        <div style="height: 8px;margin-top: 6px;">
                          <span @click="ignore()"
                            style="float: right;margin-right: 10px;color: red;cursor: pointer;">忽略</span>
                          <span @click="highlightChange(item.source, item.target)"
                            style="float: right;margin-right: 10px;color: #66b1ff;cursor: pointer;">替换</span>
                        </div>

                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </div>
                <div type="primary" class="clbutton2 clbutton1" style="margin-right: 20px;cursor: pointer;"
                  @click="save">
                  关
                  闭</div>
              </div>
            </div>
            <div class="ai-body-left-bottom" v-if="jydis == true">
             <div v-if="curIndex == '1'" @click="firstNextStep" class="ai-body-left-bottom-button">
                下一步：生成文章
              </div>
              <div v-if="curIndex == '2'" class="ai-body-left-bottom2">
                <div class="repeat" @click="repeatStep">重新生成大纲</div>
                <div @click="success" class="ai-body-left-bottom-button">
                  <span v-if="buttonShow">下一步：生成文章</span>
                  <span v-else>重新生成文章</span>
                </div>
              </div>
            </div>
          </div>
        <div class="ai-body-right">
            <div class="ai-body-start" v-if="!artShow">
              <!-- <div class="ai-body-start" v-if="false"> -->
              <!-- <div class="pic_bkg">
                <div class="pic_font">欢迎使用AI写作</div>
                <div class="title_message">
                  采用AI大模型智能生成文章，仅需3步即可一键成文，快去试试吧~
                </div>
              </div>

              <div class="pic_step"></div> -->
              <div class="pic_bkg1"></div>
            </div>

            <div class="ai-body-art" v-else>
              <!-- <el-input class="fir-textarea fir-textarea-max" type="textarea" placeholder="" v-model="textarea4">
                <template #prepend>{{ textarea4 }}</template>

              </el-input> -->
              <vue-editor class="fir-textarea fir-textarea-max" v-model="textarea4" ref="editor"
                @contextmenu.native="showContextMenu"></vue-editor>
              <div v-if="contextMenuVisible" class="context-menu" :style="contextMenuStyle">
                <ul>

                  <!-- el-icon-edit -->
                  <li @click="sendToBackend()">
                    <i class="el-icon-edit"></i>

                    续写
                  </li>
                  <!-- el-icon-circle-plus-outline -->
                  <li @click="sendToBackend2()">
                    <i class="el-icon-circle-plus-outline"></i>

                    扩写
                  </li>
                  <!-- el-icon-remove-outline -->
                  <li @click="sendToBackend3()">
                    <i class="el-icon-remove-outline"></i>
                    缩写
                  </li>
                  <li @click="sendToBackend4()">
                    <i class="el-icon-magic-stick"></i>
                    润色
                  </li>
                </ul>
              </div>
              <el-dialog title="续写内容" :visible.sync="this.xxdialog" width="30%" :before-close="handleClose">
                <div>{{ this.xxcontent }}</div>
                <span slot="footer" class="dialog-footer">
                  <el-button @click="xxdialog = false">取 消</el-button>
                  <el-button type="primary" @click="xxdialog = false, insertText()">插入</el-button>
                </span>
              </el-dialog>
              <div style="cursor: pointer;" v-if="progressPercent == 100" class="clbutton"
                v-clipboard:copy="getPlainText(textarea4)" v-clipboard:success="onCopySuccess"
                v-clipboard:error="onCopyError">
                复制全文
              </div>

              <div style="cursor: pointer;" v-if="progressPercent == 100" class="clbutton1" @click="jiaoyan">
                校验
              </div>
              <div style="cursor: pointer;" v-if="progressPercent == 100" class="clbutton12" @click="baocun()">
                保存
              </div>
            </div>
          </div>
   </div>
          </div>
      </el-main>
      <el-dialog title="第一步-新增文种" :visible.sync="dialogFormVisible" @close='getall(),del(),dialogFormVisible = false'>
    <el-form :model="form">
      <el-form-item label="文种名称">
        <el-input v-model="form.name" autocomplete="off"  placeholder="请输入文种名称"></el-input>
      </el-form-item>
      <el-form-item>
        <label class="form-label">生文关键词</label> 
        <div>
          <div v-for="(input, index) in inputs" :key="index" class="input-group">
            <el-input v-model="inputs[index]" placeholder="请输入生文关键词" style="flex-grow: 1;"></el-input>
            <el-button size="small" @click="removeInput(index)" style="margin-left: 10px;">删除</el-button>
          </div>
          <el-button size="small" @click="addInput">增加</el-button>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="resetDialogInputs(),getall(),del(),dialogFormVisible = false">取  消</el-button>
      <el-button type="primary" @click="keywordcheck()">下一步：上传范文</el-button>
    </div>
  </el-dialog>
  <el-dialog title="第二步-上传范文" :visible.sync="diatwo" @close='resetDialogInputs(),getall(),del(),diatwo=false'>
     <el-form :model="form">
    <el-form-item label="上传范文（必传）：">
      <div style="margin-left: -120px">
        <el-upload
          class="upload-demo"
          multiple
          accept=".docx"
          :before-upload="beforeAvatarUpload"
          ref="upload"
          action="#"
          :show-file-list="false"
        >
          <el-button
            slot="trigger"
            size="small"
            type="primary"
            style="
              margin-left: -700px;
              border-radius: 20px;
              background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
              box-shadow: 0px 1px 7px 0px rgba(100, 120, 212, 1);
            "
          >
            上传范文
          </el-button>
        </el-upload>
      </div>
    </el-form-item>
<el-form-item v-if="files.length > 0">
  <span style="margin-left: -805px;">已上传文件列表：</span>
  <div v-for="(fileItem, fileIndex) in files" :key="fileIndex" class="file-item">
    <span class="file-name">文件名：{{ fileItem.fileName }}</span>
    <el-button type="text" @click="removeFile(fileIndex)" style="margin-left: 20px;"> 删除</el-button>
    <div v-for="(keyword, index) in fileItem.keywords" :key="index" class="keyword-input">
      <span>{{ keyword }}：</span>
    <input 
     class="custom-input"
    v-model="keywordInputs[fileItem.fileId][keyword]"  
    @input="handleKeywordInput(fileItem.fileId, keyword)"
    placeholder="请输入关键信息"/>
    </div>
  </div>
</el-form-item>

  </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="diatwo = false,del(),getall(),resetDialogInputs()">取  消</el-button>
      <el-button type="primary" @click="diatwo = false;dialogFormVisible = true">上一步：文种信息</el-button>
      <!-- 点生文的时候就调接口更新界面基本信息 -->
      <el-button type="primary" @click="dialogFormVisible = false,diatwo=false,upkey(),del()">保存</el-button>
    </div>
  </el-dialog>
    </el-container>
  </div>
</template>
<script>
import { VueEditor } from "vue2-editor";
import { EventBus } from '../eventBus'; // 引入事件总线

import CustomSteps from "../components/Step.vue";
import store from '../store/index'
// import store from '../store/index'
import { mapState } from 'vuex';
import Quill from 'quill';
let BlockEmbed = Quill.import('blots/block/embed');
class CustomBlock extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.innerHTML = value.html;
    node.setAttribute('style', 'padding: 20px; border: 1px solid #488aff; color:red; position: relative;');
    // 保存当前光标位置
    const selection = value.editorClass.getSelection();
    value.savedSelection = selection;
    // 创建插入按钮
    let insertButton = document.createElement('button');
    insertButton.innerHTML = '应用';
    insertButton.className = 'btn-insert';
    insertButton.style.position = 'absolute';
    // 按钮颜色
    insertButton.style.backgroundColor = '#488aff';
    // 字体为白色
    insertButton.style.color = 'white';
    // insertButton.style.top = '10px';
    insertButton.style.right = '70px';
    insertButton.addEventListener('click', () => {
      // console.log(value.editorClass, '编辑器')
      // console.log(value.newText, '续写内容！')
      // // 这里的位置，是替换的传position，是插入的传position加length
      // console.log(value.Position, '位置')
      // console.log(value.selectedTextlength, '长度？？？？？？？？')
      // console.log(value.editorClass.getSelection(), '光标位置？？？？')
      if (value.flag == 1) {
        value.editorClass.insertText(value.Position + value.selectedTextlength, value.newText);
        node.remove();
      } else {
        value.editorClass.deleteText(value.Position, value.selectedTextlength);
        value.editorClass.insertText(value.Position, value.newText);
        node.remove();
      }
    });
    // 创建取消按钮
    let cancelButton1 = document.createElement('button');
    cancelButton1.innerHTML = '取消';
    cancelButton1.className = 'btn-cancel';
    cancelButton1.style.position = 'absolute';
    // cancelButton.style.top = '10px';
    cancelButton1.style.backgroundColor = '#e64721';
    // 字体为白色
    cancelButton1.style.color = 'white';
    cancelButton1.style.right = '10px';
    cancelButton1.addEventListener('click', () => {
      node.remove();
      // alert('取消按钮点击');
      // // 这里可以添加取消按钮的逻辑
    });

    // 将按钮添加到node中
    node.appendChild(insertButton);
    node.appendChild(cancelButton1);

    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}
CustomBlock.blotName = 'customBlock';
CustomBlock.tagName = 'div';

Quill.register({
  'formats/customBlock': CustomBlock
});
import {
  getSecDtDatas,
  getSuccessInfo,
  getSuccessInfoItem,
  getLabel1,
  getLabel2,
  testcheck,
  reflections,
  bcwz,
  rewrite,
  insert_genre,
get_genre_list,
get_genre_info,
delete_genre,
upload_ModelfileTogenre,
insert_modelfile_key_information,
delete_genre_modelfile
} from "../api/home.js"; // 接口请求
 export default {
  computed: {
    ...mapState(["id"]), // 映射 username 和 id
    ...mapState(["id"]),
    username() {
      return this.$store.state.username;

    },
    contextMenuStyle() {
      return {
        top: `${this.contextMenuPosition.y - 80}px`,
        // left: `${this.contextMenuPosition.x-200}px`
        left: `${this.contextMenuPosition.x - 600}px`
      };
    }
  },
  data() {
    return {
      kw: '', 
      modelfile_id: "", // 上传成功范文的id
       fileName: '', // 存储文件名
      keywords: [] ,// 存储关键词数组
      keyword: '', // 存储单个关键词
      upid:'',
      dialogFormVisible:false,
      diatwo:false,
      inputs: [''], // 初始有一个输入框
       form: {
          name: '',
         
        },
         files: [] ,// 存储所有上传文件的数组
          keywordInputs: {}, // 用于存储每个文件 ID 对应的关键词输入信息
      dialogFormVisible:false,
        inputVisibletop: false,
      
      tags1: [],
      newText: "",
      newText1: "",
      newText2: "",
      newText3: "",
      contextMenuVisible: false,
      contextMenuPosition: { x: 100, y: 0 },
      selectedText: '',
      con_loading: false,
      correctionList: [],
      jydis: true,
      navShow: true,
      textarea: "",
      textarea2: [],
      textarea3: "",
      textarea4: "",
      dialogShow: false,
      dialog2Show: false,
      gzbj: [],
      stepActived: 1,
      loading: false,
      // loading: true,
      curIndex: "1",
      articles: "",
      artShow: false,
      mask: false,
      maskAll: false,
      /**
       * @description:  功能性按钮
       * @return {*}    传入组件->FunctionTable
       */
      // steps: [{ title: "基本信息" }, { title: "大纲" }, { title: "文章" }],
      steps: [{ title: "基本信息" }, { title: "文章" }],
      currentStep: 1, // 初始化当前步骤为第二个步骤
      secRecommend: [], // 第二步推荐
      dynamicTags: [],
      inputVisible: true,
       
      inputValue: "",
      inputValuetop: "",

      resDataItem: [],
      progressPercent: 0,
      fetchResPrams: [],
      buttonShow: true,
      drawer: false,
      direction: 'ltr',
      activeNames: []
      // asyncStatus: true
    };
  },
  components: {
    // 注册组件
    CustomSteps,
    VueEditor
  },
  mounted() {
     
this.getall();
  },
         methods: {
      clickpb(){
      this.$router.push("/pb_manage");
    },
          clickTophy() {
      this.$router.push("/hyjl_sz");
    },
          clickrw(){
            this.$router.push("/check");
          },
    clickmb(){
      this.$router.push("/mb_manage");
    },
     resetDialogInputs() {
        // 重置 form 对象
        this.form.name = ''; // 清空文种名称

        // 重置生文关键词
        this.inputs = ['']; // 重新设置为初始状态，确保至少有一个输入框

        // 重置上传文件相关输入
        this.files = []; // 清空已上传的文件列表
        this.keywordInputs = {}; // 清空关键词输入对象
    },
     handleKeywordInput(fileId, keyword) {
        console.log(this.keywordInputs[fileId][keyword]); // 打印当前输入的值
    },
async upkey() {
  // 构造传递给后端的参数，按照您的请求格式
  let params = {
    genre_id: this.upid,
    modelfile_key_information: {},
  };

  if (this.files.length > 0) {
    this.files.forEach(fileItem => {
      const fileId = fileItem.fileId; // 根据文件项获取 ID
      const keywordInfo = {};

      // 确保 keywords 字段存在并进行填充
      for (const keyword of fileItem.keywords) {
        keywordInfo[keyword] = this.keywordInputs[fileId]?.[keyword] || ""; // 获取用户输入的内容，未输入则默认为空
      }

      // 将每个 fileId 关键词信息存储到请求参数中
      params.modelfile_key_information[fileId] = keywordInfo;
    });

    // 发送请求到后端
    console.log(params, "构建的参数"); // 调试输出
    let res = await insert_modelfile_key_information(params);
    if (res.data.status_code == 200) {
      this.$message({
        message: '保存成功',
        type:'success'
      });
      this.getall();
      this.resetDialogInputs();
    } else {
      this.$message({
        message: '保存失败',
        type: 'error'
      });
      this.getall();
  }
  }
},
      beforeAvatarUpload(files) {
      this.text = false;
      const formData = new FormData();
      // 确保 files 是一个数组
      const fileArray = Array.isArray(files) ? files : [files];

      // 确保所有文件都读取完毕后再进行下一步操作
      Promise.all(
        fileArray.map((file) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              const binaryFileStream = event.target.result; // 获取二进制文件流
              formData.append(
                "model_files",
                new Blob([binaryFileStream], { type: file.type }),
                file.name
              ); // 二进制流和文件名
              resolve();
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file); // 读取文件为二进制数据
          });
        })
      )
        .then(() => {
          // formData.append("file_type", "述职报告"); // 添加额外字段
          formData.append("genre_id", this.upid); // 添加额外字段
          this.formData = formData; // 将 formData 存储在组件的属性中
          // console.log("FormData 内容:", this.formData.getAll("files[]")); // 调试输出
          this.scfw();
        })
        .catch((error) => {
          console.error("文件读取失败:", error);
        });
    },
async scfw() {
  if (!this.formData) {
    console.error("没有文件被上传");
    return;
  }
  try {
    let res = await upload_ModelfileTogenre(this.formData); // 直接传递 formData 进行上传
    if (res.data.status_code === 200) {
      this.$message({
        message: "上传成功",
        type: "success",
      });

      const fileName = res.data.filename; // 假设返回的数据中有文件名
      const fileId = res.data.modelfile_id; // 获取后端返回的文件 ID
      let keywordsString = res.data.keyword; // 获取关键词字符串并处理
      keywordsString = keywordsString.replace(/'/g, '"'); // 将单引号替换为双引号

      // 解析关键词数组
      const keywords = JSON.parse(keywordsString); // 将其转为 JSON 数组
      // 将文件信息存储在 files 数组中
     this.files.push({
    fileId: fileId, // 储存文件 ID
    fileName: fileName,
    keywords: keywords, // 存储为数组
});

// 初始化关键词输入对象
this.keywordInputs[fileId] = {}; // 为该 ID 初始化关键词输入
keywords.forEach(keyword => {
    this.keywordInputs[fileId][keyword] = ""; // 初始化每个关键词的输入为空字符串
});
      
      console.log(this.files, '当前的文件列表'); // 确认存储的数据
    } else {
      this.$message({
        message: "上传失败",
        type: "error",
      });
    }
  } catch (error) {
    console.error("上传失败:", error);
  }
},

   async getall(){
      let params={
        "cjrid": this.id,
        };
      let res = await get_genre_list(params);
      if (res.data.status_code == 200) {
        // this.gzbj = res.data.data;
        // this.$message({
          // message: '获取文种列表成功',
        //   type:'success'
        // });
        this.tags1 = res.data.data;
      } else {
        this.$message({
          message: '获取文种列表失败',
          type: 'error'
        });
      }
    },
    
    keywordcheck(){
       // 去掉前后空格并过滤掉空字符串
    const trimmedInputs = this.inputs.map(input => input.trim()).filter(input => input !== '');

    // 使用 Set 检查重复元素
    const uniqueInputs = new Set(trimmedInputs);
      if (uniqueInputs.size !== trimmedInputs.length) {
      // 存在重复元素
      this.$message({
        message: '输入的关键词中存在重复项，请检查！',
        type: 'warning'
      });
    }
    else{
      this.addwz();
    }
    },
   async addwz(){
let   params={
        "cjrid": this.id,
        genre_name: this.form.name,
        keyword:this.inputs,
        };
        let res = await insert_genre(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: '文种添加成功',
          type:'success'
        });
 this.dialogFormVisible=false;
    this.diatwo=true;
    this.upid=res.data.data.genre_id;
      } else {
        this.$message({
          message: '文种添加失败',
          type: 'error'
        });
      }
    },

     addInput() {
      this.inputs.push(''); // 添加一个新的空输入框
    },
    removeInput(index) {
      if (this.inputs.length > 1) {
        this.inputs.splice(index, 1); // 删除指定索引的输入框
      } else {
        this.inputs[0] = ''; // 保持至少有一个输入框
      }
    },
  // },
      showInput() {
        this.inputVisibletop= true;
        this.$nextTick(_ => {
          this.$refs.saveTagInputtop.$refs.input.focus();
        });
      },
    clickTopxd(){
      this.$router.push("/xdth");
      
    },
      togrzx() {
      this.$router.push("/grzx1");
    },
    toxg() {
      // alert(1)
      this.$router.push("/grzx4");
    },
    clickTopfw() {
      this.$router.push("/fw");
    },
     clickTopsfw() {
      this.$router.push("/sfw");
    },
    showContextMenu(event) {
      event.preventDefault();
      this.contextMenuPosition = { x: event.clientX - 400, y: event.clientY }; // 调整这里的值来向左移动菜单
      this.selectedText = window.getSelection().toString();
      if (this.selectedText) {
        this.contextMenuPosition = { x: event.clientX, y: event.clientY };
        this.contextMenuVisible = true;
        document.addEventListener('click', this.hideContextMenu);
      };

    },
    hideContextMenu() {
      this.contextMenuVisible = false;
      document.removeEventListener('click', this.hideContextMenu);
    },
    // 续写接口
    async sendToBackend() {
      this.loading = true;
      this.mask = true;
      let params1 = {
        "text": this.selectedText,
        "flag": "1"
      };
      let res = await rewrite(params1);
      this.newText = res.data.data;
      if (res.data.status_code == 200) {
        this.loading = false;
        this.mask = false;
        this.$message({
          message: '续写成功',
          type: 'success'
        });
        // 注册自定义样式
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();
        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);
        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(position + this.selectedText.length, 'customBlock', {
            html: this.newText,
            newText: this.newText,
            editorClass: this.$refs.editor.quill,
            Position: position,
            selectedTextlength: this.selectedText.length,
            flag: 1,
          }, Quill.sources.USER);
          // 将后端返回的续写文字插入到该位置后面
          // editor.insertText(position + this.selectedText.length, this.newText);
          // editor.formatText(position + this.selectedText.length, this.newText.length, 'color', 'red');
          // // 将光标移动到新插入的文本后面
          // editor.setSelection(position + this.selectedText.length + this.newText.length, 0);
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText);
          // 将新插入的文本格式化为红色
          console.log('Formatting text at position:', editor.getLength() - this.newText.length);
          console.log('Formatting text length:', this.newText.length);
          editor.formatText(editor.getLength() - this.newText.length, this.newText.length,);

          editor.setSelection(editor.getLength(), 0);
        }
      }
      else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: '续写失败',
          type: 'error'
        })
      };

    },

    // 2.扩写接口
    async sendToBackend2() {
      this.loading = true;
      this.mask = true;
      let params2 = {
        "text": this.selectedText,
        "flag": "2"
      };
      let res = await rewrite(params2);
      this.newText1 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading = false;
        this.mask = false;
        this.$message({
          message: '扩写成功',
          type: 'success'
        });
        // this.loading = false;
        //         // this.xxdialog=true;
        //         this.xxcontent=this.newText;
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          const position = allText.indexOf(this.selectedText);

          editor.insertEmbed(position + this.selectedText.length, 'customBlock', {
            html: this.newText1,
            newText: this.newText1,
            editorClass: this.$refs.editor.quill,
            Position: position,
            selectedTextlength: this.selectedText.length,
            flag: 2,
          }, Quill.sources.USER);
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText1);

          // 将新插入的文本格式化为红色
          console.log('Formatting text at position:', editor.getLength() - this.newText1.length);
          console.log('Formatting text length:', this.newText1.length);
          editor.formatText(editor.getLength() - this.newText1.length, this.newText1.length,);
          editor.setSelection(editor.getLength(), 0);
        }

      }
      else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: '扩写失败',
          type: 'error'
        })
      };
    },
    // 3.缩写接口
    async sendToBackend3() {
      this.loading = true;
      this.mask = true;
      let params3 = {
        "text": this.selectedText,
        "flag": "3"
      };
      let res = await rewrite(params3);
      this.newText2 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading = false;
        this.mask = false;
        this.$message({
          message: '缩写成功',
          type: 'success'
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(position + this.selectedText.length, 'customBlock', {
            html: this.newText2,
            newText: this.newText2,
            editorClass: this.$refs.editor.quill,
            Position: position,
            selectedTextlength: this.selectedText.length,
            flag: 3,
          }, Quill.sources.USER);
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText2);

          // 将新插入的文本格式化为红色
          console.log('Formatting text at position:', editor.getLength() - this.newText2.length);
          console.log('Formatting text length:', this.newText2.length);
          editor.formatText(editor.getLength() - this.newText2.length, this.newText2.length,);
          editor.setSelection(editor.getLength(), 0);
        }

      }
      else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: '缩写失败',
          type: 'error'
        });
      };

    },
    // 4.润色接口
    async sendToBackend4() {
      this.loading = true;
      this.mask = true;
      let params4 = {
        "text": this.selectedText,
        "flag": "4"
      };
      let res = await rewrite(params4);
      this.newText3 = res.data.data;
      if (res.data.status_code == 200) {
        this.loading = false;
        this.mask = false;
        this.$message({
          message: '润色成功',
          type: 'success'
        });
        const editor = this.$refs.editor.quill;
        // 获取编辑器中的所有文本
        const allText = editor.getText();

        // 找到你传给后端的那段文字的位置
        const position = allText.indexOf(this.selectedText);

        if (position !== -1) {
          const editor = this.$refs.editor.quill;
          editor.insertEmbed(position + this.selectedText.length, 'customBlock', {
            html: this.newText3,
            newText: this.newText3,
            editorClass: this.$refs.editor.quill,
            Position: position,
            selectedTextlength: this.selectedText.length,
            flag: 4,
          }, Quill.sources.USER);
        } else {
          // 如果没有找到你传给后端的那段文字，则在当前光标位置插入新的文本
          editor.insertText(editor.getLength(), this.newText3);

          // 将新插入的文本格式化为红色
          console.log('Formatting text at position:', editor.getLength() - this.newText3.length);
          console.log('Formatting text length:', this.newText3.length);
          editor.formatText(editor.getLength() - this.newText3.length, this.newText3.length,);

          editor.setSelection(editor.getLength(), 0);
        }

      }
      else {
        this.loading1 = false;
        this.mask = true;
        this.$message({
          message: '润色失败',
          type: 'error'
        })
      };
    },
    marked(markdown) {
      // 将 Markdown 文本按行分割
      const lines = markdown.split('\n');
      let html = '';
      let inList = false;
      let isFirstHeading = true; // 新增标志，用于判断是否是第一个标题
      lines.forEach(line => {
        // 处理标题
        // 处理标题
        if (line.startsWith('#')) {
          const level = line.indexOf(' ');
          const content = line.slice(level + 1);
          if (isFirstHeading) {
            html += `<h${level} style="text-align: center;">${content}</h${level}>`;
            isFirstHeading = false; // 设置标志为 false，表示第一个标题已处理
          } else {
            html += `<h${level} style="text-align: left;">${content}</h${level}>`;
          }
        }
        // 处理无序列表
        else if (line.startsWith('-')) {
          if (!inList) {
            html += '<ul style="text-align: left;">';
            inList = true;
          }
          const content = line.slice(2);
          html += `<li>${content}</li>`;
        }
        // 处理段落
        else {
          if (inList) {
            html += '</ul>';
            inList = false;
          }
          html += `<p style="text-align: left;">${line}</p>`;
        }
      });

      // 如果列表没有关闭，关闭它
      if (inList) {
        html += '</ul>';
      }
      console.log(html, '111111111111111111111111111111111111');
      this.textarea4 = html;
    
    },
    async baocun() {
      console.log(this.$route.query.userName)

      let params = {
        // wznr:this.resDataItem,
        // textarea4
        wznr: this.textarea4.replace('"', '/"'),
        wzlx: "心得体会",
        userName: this.username,
      };
      let resbcwz = await bcwz(params);
// EventBus.$emit('updateDocument'); // 发射更新事件

      console.log(resbcwz.data.status_code)
      resbcwz.data.status_code == 200 ? this.$message({
        message: '保存成功,请前往我的文档查看',
        type: 'success'
      }) : this.$message({
        message: '保存失败', type: 'error'
      })
    },
    getPlainText(html) {
      const div = document.createElement('div');
      div.innerHTML = html;
      return div.textContent || div.innerText || '';
    },
    onCopySuccess() {
      console.log('复制成功');
    },
    onCopyError() {
      console.log('复制失败');
    },
    wdwd() {
      // alert(1)
      this.$router.push("/wdwd");
    },
    logout() {
      clearVuexAlong()
      store.commit('addNewToken', '')
      this.$router.push("/login");

    },
    //按钮颜色
    getBackgroundColor(index) {
      const colors = ['#ff6403', '#fbff00', '#01fffe', '#e958ea'];
      // 使用取余运算符来循环数组中的颜色
      return colors[index % colors.length];
    },
    ignore() {
      this.textarea4 = this.removetext(this.textarea4)
      this.activeNames = []
    },
    highlightChange(text1, text2) {
      console.log(this.textarea4);
      let changeData = text2
      // 高亮显示文章中的错误文字
      // const regex = new RegExp(`<span class="highlight" style="background-color:yellow!important;">${text1}</span>`, 'g');
      // this.textarea4 = this.textarea4.replace(regex, '<span class="highlight">' + changeData + '</span>');
      this.textarea4 = this.removetext(this.textarea4)
      this.textarea4 = this.textarea4.replace(text1, changeData);
    },
    highlightError(error, word) {
      this.textarea4 = this.removetext(this.textarea4)
      let a = error.replace(word, `<span class="highlight" style="background-color:yellow!important;">${word}</span>`);
      this.textarea4 = this.textarea4.replace(error, a);
      console.log(this.textarea4);
      this.$nextTick(() => {
        // 确保 DOM 更新完成后，滚动到高亮文字
        this.scrollToHighlight();
      });
    },
    scrollToHighlight() {
      // 获取 vue-editor 中的 iframe
      const editorIframe = this.$refs.editor.$el.querySelector('div.ql-editor');
      console.log(this.$refs.editor.$el, 'editorIframe');
      if (editorIframe) {
        const highlightedElement = editorIframe.querySelector('span.highlight');
        console.log(highlightedElement, 'highlightedElement');
        if (highlightedElement) {
          // 滚动到第一个高亮文字
          highlightedElement.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      }
    },
    removetext(text) {
      let str = text
      let reg1 = new RegExp(`<span class="highlight" style="background-color:yellow!important;">`, 'g');
      let a1 = str.replace(reg1, '');
      let reg2 = new RegExp(`</span>`, 'g');
      text = a1.replace(reg2, '');
      return text
    },
    removetextp(text) {
      let str = text
      let reg1 = new RegExp(`<p>`, 'g');
      let a1 = str.replace(reg1, '');
      let reg2 = new RegExp(`</p>`, 'g');
      text = a1.replace(reg2, '');
      return text
    },
    onCopySuccess() {
      this.$message({
        message: "内容已复制到剪贴板！",
        type: "success",
      });
    },
    onCopyError() {
      this.$message({
        message: "复制失败，请手动复制!",
        type: "warning",
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push(inputValue);
        this.textarea2 = this.dynamicTags

      }
      // this.inputVisible = false;
      this.inputValue = "";
    },
          handleInputConfirmtop() {
      this.dialogFormVisible=true;

        // let inputValuetop = this.inputValuetop;
        // if (inputValuetop) {
        //   this.tags1.push(inputValuetop);
        // }
        // this.inputVisibletop = false;
        // this.inputValuetop = '';
      },
    async jiaoyan() {
      this.textarea4 = this.removetext(this.textarea4)
      this.con_loading = true;
      // let text = this.removetextp(this.textarea4)
      testcheck({
        test_all: this.textarea4,
      }).then(async (data) => {
        this.correctionList = data.data.data;
        this.con_loading = false;
        if (this.correctionList.length == 0) {
          this.jydis = true
          this.$message({
            message: '不存在文字错误',
            type: 'warning'
          });
        } else {
          console.log(data, "619");
          this.jydis = false
        }
      });

    },
    handleClosedrawer() {
      this.textarea4 = this.removetext(this.textarea4)
      this.activeNames = []
      this.drawer = false
    },

    save() {
      this.textarea4 = this.removetext(this.textarea4)
      this.activeNames = []
      this.jydis = true
    },
    fanhui() {
      this.textarea4 = this.removetext(this.textarea4)
      this.activeNames = []
      this.drawer = false
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    async handleClosetop(tag){
      this.tags1.splice(this.tags1.indexOf(tag), 1);
      console.log(tag,'w1knka1')
      let params = {
        genre_id: [tag],
        cjrid: this.id,
      };
      let res = await delete_genre(params);
      if (res.data.status_code == 200) {
        this.$message({
          message: '删除成功',
          type:'success'
        });
      this.getall();
      } else {
        this.$message({
          message: '删除失败',
          type: 'error'
        });
      }
    },

    handleStepClick(index) {
      this.lock++;
      this.activeIndex = index; // 更新activeIndex的值
    },
    upShow() {
      this.navShow = !this.navShow;
    },
    async textareaFocus() {
      let data = await getLabel1();
      this.gzbj = data.data;
      this.dialogShow = true;
    },
    closeDialog() {
      this.dialogShow = false;
    },
    textarea2Focus() {
      this.dialog2Show = true;
    },
    closeDialog2() {
      this.dialog2Show = false;
    },
    async getText(e) {
      console.log(e);
      let params = {
        label: e,
      };
      let data = await getLabel2(params);
      console.log(data, "583");
      this.secRecommend = data.data;
      this.textarea = e;
      this.textarea2 = [];
    },
    getText2(e) {
      if (!this.dynamicTags.includes(e)) {
        this.textarea2.push(e);
        this.dynamicTags.push(e);
        // this.textarea = e.srcElement.innerText;
        this.handleInputConfirm(e);
      } else {
        console.log(111);
      }

    },
    clickTopNav() {
      this.$notify({
        title: "提示",
        message: "暂未开发，敬请期待！",
        type: "warning",
      });
    },
    clickTopInd() {
      this.$router.push("/index");
    },
    clickTopLg() {
      this.$router.push("/lgsc");
    },
    clickTopJq() {
      this.$router.push("/jqkz");
    },
    clickTopTy() {
      this.$router.push("/tywzsb");
    },
    clickTopFz() {
      this.$router.push("/Fzdm");
    },
    clickTopbmai() {
      this.$router.push("/bmai");
    },
    // 工作总结
    clickTopGz() {
      this.$router.push("/gzzj");
    },
    // 领导讲话
    clickTopLd() {
      this.$router.push("/ldjh");

    },
    // 工作方案
    clickTopFa() {
      this.$router.push("/gzfa");

    },
    // 调研报告
    clickTopDy() {
      this.$router.push("/dybg");

    },
    // 宣传材料
    clickTopXc() {
      this.$router.push("/xccl");

    },
    firstNextStep() {
      // if (this.textarea == "") {
      //   this.$notify({
      //     title: "提示",
      //     message: "请填写关键词",
      //     type: "warning",
      //   });
      // } else if (this.textarea2.length == 0) {
      //   this.$notify({
      //     title: "提示",
      //     message: "请填写关键词",
      //     type: "warning",
      //   });
      // } else {
        this.mask = true;
        this.artShow = true;
        this.loading = true;
        this.progressPercent = 0;
        this.getSuccessInfo(this.textarea, this.dynamicTags);
      },

    async getInfo(c1, c2) {
      this.loading = true;
      let params = {
        test_1: c1,
        test_2: c2,
      };
      let res = await getSecDtDatas(params);
      if (res.status == 200) {
        this.textarea3 = res.data;
        // 这里放置需要执行的逻辑或调用其他方法
        this.buttonShow = true;
        // this.stepActived = 2;
        this.currentStep = 2;
        this.loading = false;
        this.mask = false;
        this.curIndex = "2";
      }
    },
    repeatStep() {
      this.loading = true;
      this.mask = true;
      if (this.mask == true) {
        this.getInfo(this.textarea, this.dynamicTags);
      }
    },
    success() {
      this.artShow = true;
      this.loading = true;
      // 这里放置需要执行的逻辑或调用其他方法
      if (this.textarea3 == "") {
        this.$notify({
          title: "提示",
          message: "大纲内容为空，无法生成",
          type: "warning",
        });
      } else {
        this.progressPercent = 0;
        this.getSuccessInfo(this.textarea3, this.textarea);
      }
    },
    // 依次请求接口
    async fetchResData(index, nextIndex) {
      if (index >= this.fetchResPrams.length) {
        this.fetchData(nextIndex);
        return;
      }
      console.log(this.fetchResPrams[index], "675");
      let pams = {
        string: this.fetchResPrams[index],
        id_name: this.textarea,
      };
      const response = await getSuccessInfoItem(pams);
      this.textarea4 += response.data;
      let totalLength = 0;
      let allSubArrays = this.resDataItem.filter(
        (item) => typeof item != "string"
      );
      allSubArrays.forEach((subArray) => {
        totalLength += subArray.length;
      });
      let kuai = 100 / totalLength;
      this.progressPercent += Math.floor(kuai);
      await this.fetchResData(index + 1, nextIndex);
    },
    // 一段式生成
    async fetchData(index) {
      if (index >= this.resDataItem.length) {
        this.progressPercent = 100;
        // this.maskAll = false;
        this.maskAll = true;
        this.buttonShow = false;
        return;
      }
      if (typeof this.resDataItem[index] == "string") {
        this.textarea4 += this.resDataItem[index];
        await this.fetchData(index + 1);
      } else {
        this.fetchResPrams = this.resDataItem[index];
        this.fetchResData(0, index + 1);
      }
      // this.stepActived = 3;
      this.currentStep = 3;
      this.loading = false;
    },
    async getSuccessInfo(c1, c2) {
      this.loading = true;
      this.maskAll = true;
      let params = {
        test_1: c1,
        test_2: c2,
        username: this.username,
      };
      let res = await reflections(params);

      if (res.data.status_code == 200) {
        this.$message({
          message: res.data.message,
          type: 'success'
        })
        this.textarea4 = "";
        // this.resDataItem = res.data.data;
        // this.resDataItem = res.data;
        this.marked(res.data.data)

        this.fetchData(0);
        this.currentStep = 3;
        // this.stepActived = 3;
        this.loading = false;
      }
      else if (res.data.status_code == 500) {

        this.$message({
          message: res.data.message,
          type: 'error'
        })
      }
      else if (res.data.status_code == 10001) {

        this.$message({
          message: res.data.message,
          type: 'error'
        })
      }
      else if (res.data.status_code == 10002) {

        this.$message({
          message: res.data.message,
          type: 'error'
        })
      }
    },
      del() {

     this.inputVisibletop = false;  // 隐藏输入框
    this.form.name = '';       // 清空输入框的值
    this.inputs = [''];             // 将输入框数组重置为只包含一个空输入框
  },
    clickTab(i) {
      if (this.currentStep != 2) {
        this.$notify({
          title: "提示",
          message: "请先输入工作背景和工作要点生成大纲",
          type: "warning",
        });
      } else {
        this.curIndex = i;
      }
    },
  },
  
}
</script>
<style lang="less" scoped>
.keyword-input {
  margin-top: 10px;
}
.input-group {
  margin-bottom: 10px; /* 每个输入框之间的间距 */
}
        // font-size: calc(100vw * 14 / 1920);
.custom-steps{
        font-size: calc(100vw * 14 / 1920);

}
.context-menu {
  margin-left: calc(-100vw * 200 / 1920);
  position: absolute;
  background: #fff;
  box-shadow: 0 calc(100vh * 2 / 1080) calc(100vh * 10 / 1080) rgba(0, 0, 0, 0.1);
  z-index: 1080;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.context-menu li {
  padding: calc(100vh * 8 / 1080) calc(100vw * 16 / 1920);
  cursor: pointer;
  color: #0b0b0b;
}

.context-menu li:hover {
  background: #f0f0f0;
}

.fr {
  float: right;
}

.work-container {
  height: 100%;

  .result-container {
    padding: calc(100vh * 30 / 1080);

    .elcol-title {
      display: flex;
      overflow: hidden;
      width: 100%;
      height: calc(100vh * 48 / 1080);

      .elcol-title-text {
        padding-left: calc(100vw * 10 / 1920);
        text-align: left;
        width: 40%;
        height: 100%;
      }

      .elcol-title-text2 {
        font-size: calc(100vw * 14 / 1920);
        width: calc(100vw * 75 / 1920);
      }

      .elcol-input {
        float: left;
        width: calc(60% - calc(100vw * 75 / 1920));
        border: none !important;
      }
    }
  }
}

.elcol-input ::v-deep(.el-input__inner) {
  border: none !important;
  // background-color: #f9fafe;
  height: calc(100vh * 20 / 1080);
}

.elcol-input::v-deep(.el-input__inner) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.elcol-title ::v-deep(.el-color-picker__icon, .el-input, .el-textarea) {
  width: 43% !important;
}

.ejdhl {
  height: 100%;
  background: url(../assets/img-left.png) no-repeat center;
  background-size: cover;
  background-position: center;
  transition: ease-out 0.4s;
}

.ejdhlTitle {
  height: calc(100vh * 80 / 1080);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  // background-color: #fff;
  background-image: linear-gradient(76deg, #07389c 0%, #3d86d1 0%, #3448b3 100%);

  img {
    width: calc(100vw * 120 / 1920);
    height: calc(100vh * 48 / 1080);
  }

  p {
    border: calc(100vw * 1.54 / 1920) solid rgba(0, 0, 0, 0);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 24 / 1920);
    color: #000000;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    margin-left: calc(100vw * 5 / 1920);
  }
}

.el-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: calc(100vh * 80 / 1080) !important;
  background: #ffffff;
  box-shadow: 0 calc(100vh * 2 / 1080) calc(100vh * 4 / 1080) 0 rgba(192, 208, 229, 0.43);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  width: calc(100% - calc(100vw * 300 / 1920));

  .ai-header {
    width: 100%;

    .ai-bar {
      width: calc(100% - calc(100vw * 300 / 1920));
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: calc(100vh * 80 / 1080);
      background-color: #fff;

      .ai-left-bar {
        display: flex;
        justify-content: center;
        align-items: center;
        column-gap: calc(100vw * 40 / 1920);
        height: calc(100vh * 40 / 1080);
        border-radius: calc(100vh * 20 / 1080);
        background: #f4f6f8;
        border: calc(100vw * 1 / 1920) solid #e6e6e6;

        .ai-left-bar-ul {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          height: 100%;

          .ai-left-bar-li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh * 42 / 1080);
            width: calc(100vw * 130 / 1920);
            color: #000;
            font-size: calc(100vw * 14 / 1920);
            line-height: calc(100vh * 16 / 1080);
            white-space: nowrap;
            cursor: pointer;
            z-index: 9999;
            font-family: PingFangSC-Semibold;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            text-align: center;

            img {
              width: calc(100vw * 16 / 1920);
              height: calc(100vh * 16 / 1080);
              margin-right: calc(100vw * 14 / 1920);
            }

            &:hover {
              background-image: linear-gradient(107deg,
                  #3a6bc6 0%,
                  #488aff 100%);
              box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
              border-radius: calc(100vh * 20 / 1080);
              color: #fff;
            }
          }
          .actived {
            background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
            box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
            border-radius: calc(100vh * 20 / 1080);
            color: #fff;
          }
        }
      }

      .ai-right-bar {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        position: fixed;
        right: calc(100vw * 93 / 1920);
        height: calc(100vh * 80 / 1080);

        .top-button {
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 16 / 1920);
            height: calc(100vh * 16 / 1080);
          }
        }

        .btn {
          margin-left: calc(100vw * 30 / 1920);
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #409eff;
        }

        .el-icon-arrow-down {
          font-size: calc(100vw * 12 / 1920);
        }

        .ai-avant {
          overflow: hidden;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: calc(100vw * 28 / 1920);
            height: calc(100vh * 28 / 1080);
            float: left;
          }

          p {
            float: left;
            margin-left: calc(100vw * 22 / 1920);
            margin-top: calc(-100vh * 2 / 1080);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: SourceHanSansSC-Regular;
            font-size: calc(100vw * 14 / 1920);
            color: #000000;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.el-main {
  // background-color: #f8f9fd;
  color: #333;
  text-align: center;
  width: calc(100% - calc(100vw * 300 / 1920));
  height: calc(100% - calc(100vh * 80 / 1080));
  position: absolute;
  right: 0;
  top: calc(100vh * 80 / 1080);
  padding: calc(100vh * 20 / 1080) calc(100vw * 10 / 1920);
}
  .ai-gai {
    position: fixed;
    left: 0;
    bottom: 0;
    height: 91%;
    // z-index: 999;
  }

  .ai-body {
    transition: ease-out 0.4s;
    width: 100%;
    height: 100%;
    float: left;
    overflow: hidden;
  }
  .ai-body-top {
  /* 确保 ai-body-top 的样式可以正常显示 */
  width: 100%; /* 设置宽度 */
  padding: 10px; /* 可选，根据需要调整内边距 */
}

.ai-body-content {
  display: flex; /* 水平方向排列 */
  flex-grow: 1; /* 占据剩余空间 */
}
    .ai-body-left {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      width: calc(100vw * 444 / 1920);
      height: 100%;
      background: #ffffff;
      border-radius: calc(100vh * 8 / 1080);
      overflow-x: hidden;
      overflow-y: auto;
      margin-left: calc(100vw * 10 / 1920);
      float: left;}

      .ai-body-left-top {
        width: 100%;
        height: calc(100% - calc(100vh * 80 / 1080));
        overflow-y: scroll;
      }

      .ai-body-left-bottom {
        width: 100%;
        height: calc(100vh * 90 / 1080);

        .ai-body-left-bottom-button {
          height: calc(100vh * 40 / 1080);
          font-size: calc(100vw * 14 / 1920);
          flex-grow: 1;
          color: #fff;
          margin: 0 calc(100vw * 25 / 1920);
          cursor: pointer;
          line-height: calc(100vh * 40 / 1080);
          margin-top: calc(100vh * 30 / 1080);
          background-image: linear-gradient(96deg, #3a6bc6 0%, #488aff 100%);
          box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
          border-radius: calc(100vh * 20 / 1080);
          font-family: PingFangSC-Regular;
        }
      }

      .ai-body-left-bottom2 {
        width: 100%;
        height: calc(100vh * 80 / 1080);
        padding-top: calc(100vh * 30 / 1080);

        .repeat {
          width: calc(100vw * 188 / 1920);
          float: left;
          height: calc(100vh * 40 / 1080);
          font-size: calc(100vw * 14 / 1920);
          flex-grow: 1;
          margin-left: calc(100vw * 25 / 1920);
          cursor: pointer;
          line-height: calc(100vh * 40 / 1080);
          // background: #f4f6f8;
          border: calc(100vw * 1 / 1920) solid rgba(230, 230, 230, 1);
          border-radius: calc(100vh * 20 / 1080);
          font-family: PingFangSC-Semibold;
          color: #666666;
          text-align: center;
        }

.ai-body-left-bottom-button {
  width: calc(100vw * 188 / 1920);
  float: left;
  height: calc(100vh * 40 / 1080);
  flex-grow: 1;
  margin-left: calc(100vw * 14 / 1920);
  cursor: pointer;
  line-height: calc(100vh * 40 / 1080);
  background-image: linear-gradient(102deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 20 / 1080);
  margin-top: 0px;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
}

.ai-step {
  margin-top: calc(100vh * 20 / 1080);
  width: calc(100% - calc(100vw * 40 / 1920));
  margin: 0 calc(100vw * 20 / 1920);
  margin-top: calc(100vh * 5 / 1080);
  height: calc(100vh * 40 / 1080);
}
      }
.ai-body-right {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: calc(100% - calc(100vw * 480 / 1920));
  background: #ffffff;
  border-radius: calc(100vh * 8 / 1080);
  overflow-x: hidden;
  overflow-y: auto;
  margin-left: calc(100vw * 20 / 1920);
  float: left;

  .ai-body-start {
    width: 100%;
    height: 100%;

    .pic_bkg1 {
      width: calc(100vw * 670 / 1920);
      height: calc(100vh * 590 / 1080);
      background: url(../assets/img-lct.png) no-repeat center;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: calc(100vh * 70 / 1080);
      position: relative;
    }

    .pic_bkg {
      width: calc(100vw * 528 / 1920);
      height: calc(100vh * 518 / 1080);
      background: url(../assets/img-ai.png) no-repeat center;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: calc(100vh * 118 / 1080);
      position: relative;
    }

    .pic_font {
      position: absolute;
      margin-left: auto;
      margin-right: auto;
      left: 0;
      right: 0;
      bottom: calc(100vh * 116 / 1080);
      width: calc(100vw * 255 / 1920);
      height: calc(100vh * 40 / 1080);
      border: calc(100vw * 1.54 / 1920) solid rgba(0, 0, 0, 0);
      font-family: PingFangSC-Semibold;
      font-size: calc(100vw * 28 / 1920);
      color: #000000;
      text-align: center;
      font-weight: 600;
    }

    .title_message {
      position: absolute;
      margin-left: auto;
      margin-right: auto;
      left: 0;
      right: 0;
      bottom: calc(100vh * 82 / 1080);
      text-align: center;
      line-height: calc(100vh * 16 / 1080);
      margin-top: calc(100vh * 10 / 1080);
      height: calc(100vh * 22 / 1080);
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 14 / 1920);
      color: #999999;
      letter-spacing: 0;
      font-weight: 400;
    }

    .pic_step {
      width: calc(100vw * 551 / 1920);
      height: calc(100vh * 142 / 1080);
      background: url("../assets/pic_step.png");
      background-size: contain;
      margin: auto;
    }
  }

  .ai-body-art {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;

    .over {
      overflow: auto;
    }

    .fir-textarea {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 96.5%;
      // background: #f8f9fd;
      border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
      border-radius: calc(100vh * 4 / 1080);
      margin: calc(100vh * 20 / 1080);
      margin-bottom: 0;
      height: calc(100vh * 158 / 1080);

      ::v-deep(.el-textarea__inner) {
        font-size: calc(100vw * 14 / 1920) !important;
        // background-color: #f8f9fd !important;
        height: 100% !important;
        font-family: PingFangSC-Regular;
        padding: calc(100vh * 13 / 1080) calc(100vw * 18 / 1920) calc(100vh * 33 / 1080) calc(100vw * 16 / 1920);
      }
    }

    .fir-textarea-max {
      height: 95% !important;
    }

    ::v-deep(.quillWrapper .ql-snow.ql-toolbar) {
      display: none !important;
    }

    ::v-deep(.ql-blank) {
      display: none !important;
    }

    ::v-deep(.ql-container.ql-snow) {
      border: 0;
    }
  }
}

.ai-tab {
  width: calc(100vh * 230 / 1080);
  height: calc(100vh * 40 / 1080);
  margin: 0 auto;
  margin-top: calc(100vh * 30 / 1080);
  background: #f4f6f8;
  border: 1px solid #eeeff0;
  border-radius: calc(100vh * 20 / 1080);

  .tab-item {
    width: 100%;
    height: calc(100vh * 40 / 1080);
    line-height: calc(100vh * 16 / 1080);
    float: left;
    line-height: calc(100vh * 40 / 1080);
    cursor: pointer;

    font-family: PingFangSC-Semibold;
    font-size: calc(100vh * 14 / 1080);
    color: #9094a5;
    letter-spacing: 0;
    text-align: center;
  }

  .activedTab {
    border-radius: calc(100vh * 20 / 1080);

    background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
    box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
    color: #ffffff;
  }
}

.tab-item-fir {
  width: 100%;
  height: calc(100vh * 536 / 1080);
  padding: 0 calc(100vw * 25 / 1920);
}

  .fir-title {
    color: #222;
    font-weight: 500;
    font-size: calc(100vw * 14 / 1920);
    letter-spacing: 0;
    line-height: calc(100vh * 16 / 1080);
    overflow: hidden;
    margin-top: calc(100vh * 20 / 1080);
    margin-bottom: calc(100vh * 20 / 1080);
    height: calc(100vh * 20 / 1080);

    .fir-kuai {
      width: calc(100vw * 6 / 1920);
      height: calc(100vh * 16 / 1080);
      margin-right: calc(100vw * 8 / 1920);
      float: left;
      background: #4081ff;
      border-radius: calc(100vh * 1.5 / 1080);
    }

    .fir-title-p {
      line-height: calc(100vh * 16 / 1080);
      float: left;
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 14 / 1920);
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
    }
  }

  .fir-alert {
    margin-top: calc(100vh * 10 / 1080);
    height: calc(100vh * 35 / 1080);
  }

  .ai-dialog {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    row-gap: calc(100vh * 1 / 1080);
    width: 100%;
    height: calc(100vh * 290 / 1080);
    max-height: calc(100vh * 294 / 1080);
    padding: calc(100vh * 7 / 1080);
    box-shadow: 0 calc(100vh * 20 / 1080) calc(100vh * 40 / 1080) calc(100vh * 4 / 1080) #e4e4e524;
    margin-top: calc(100vh * 10 / 1080);
    transition: ease-out 0.4s;
    background: #ace9ff;
    border: calc(100vw * 1 / 1920) solid rgba(90, 206, 255, 1);
    border-radius: calc(100vh * 4 / 1080);

    .ai-d-title {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: flex-start;
      width: 100%;
      height: fit-content;
      margin: 0;
      padding: calc(100vh * 1 / 1080) calc(100vw * 3 / 1920) calc(100vh * 2 / 1080) calc(100vw * 2 / 1920);
      font-family: PingFangSC-Regular;
      font-size: calc(100vw * 14 / 1920);
      color: #313733;
      letter-spacing: 0;
      font-weight: 400;

      .ai-d-title-p {
        flex-grow: 1;
        line-height: calc(100vh * 16 / 1080);
        text-align: left;
        font-family: PingFangSC-Regular;
        font-size: calc(100vw * 14 / 1920);
        color: #313733;
        letter-spacing: 0;
        font-weight: 400;
        margin-bottom: calc(100vh * 10 / 1080);
      }

      img {
        width: calc(100vw * 18 / 1920);
        height: calc(100vh * 18 / 1080);
        cursor: pointer;
      }
    }

    .ai-d-body {
      width: 100%;
      height: calc(100% - calc(100vh * 44 / 1080));
      overflow: hidden;
      background: #ffffff;
      border-radius: calc(100vh * 4 / 1080);

      .hints-control {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        padding: calc(100vh * 14 / 1080) calc(100vw * 14 / 1920) 0;
        height: calc(100vh * 30 / 1080);

        .hint-icon {
          flex-grow: 0;
          flex-shrink: 0;
          width: calc(100vw * 20 / 1920);
          height: calc(100vh * 20 / 1080);
          margin-right: calc(100vw * 6 / 1920);
          background-size: contain;
          background-image: url("../assets/icon_fire.png");
        }

        .hint-description {
          font-weight: 600;
          line-height: calc(100vh * 14 / 1080);
          font-family: SourceHanSansSC-Bold;
          font-size: calc(100vw * 14 / 1920);
          color: #313733;
          letter-spacing: 0;
          font-weight: 700;
        }
      }
    }
  }
.ai-tj-body {
  width: 100%;
  height: calc(100vh * 200 / 1080);
  overflow-y: auto;

  .ai-tj-item {
    padding: calc(100vh * 14 / 1080) calc(100vw * 14 / 1920) 0;
    line-height: calc(100vh * 12 / 1080);
    width: 50%;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: left;
    cursor: pointer;
    height: calc(100vh * 30 / 1080);
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #313733;
    letter-spacing: 0;
    font-weight: 400;
  }
}

// ***滚动条样式
.ai-tj-body::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
}

.ai-tj-body::-webkit-scrollbar-track {
  background: #fff;
}

.ai-tj-body::-webkit-scrollbar-thumb {
  background: #488aff;
}

.fir-textarea {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: calc(100vh * 14 / 1080);
  height: calc(100vh * 158 / 1080);
  // background: #f8f9fd;
  border-radius: calc(100vh * 4 / 1080);

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
  }
}

.fir-textarea-height {
  height: calc(100vh * 460 / 1080) !important;
}

::v-deep(.el-textarea__inner) {
  padding: calc(100vh * 16 / 1080);
}

::v-deep(.el-textarea .el-input__count) {
  color: #909399;
  background: none;
  position: absolute;
  font-size: calc(100vw * 12 / 1920);
  right: calc(100vw * 10 / 1920);
  height: calc(100vh * 10 / 1080);
}

.ai-nav {
  height: calc(100% - calc(100vh * 80 / 1080));
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;
  transition: ease-out 0.4s;

  .title {
    width: calc(100vw * 300 / 1920);
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    height: calc(100vh * 20 / 1080);
    line-height: calc(100vh * 20 / 1080);
    padding-top: calc(100vh * 20 / 1080);
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    row-gap: calc(100vh * 12 / 1080);
    width: 100%;
    padding-top: calc(100vh * 45 / 1080);
    height: calc(100% - calc(100vh * 80 / 1080));
  }
}

.user-menu-content {
  width: calc(100vw * 246 / 1920);
  height: calc(100vh * 24 / 1080);
  padding: 0 calc(100vw * 20 / 1920);
  background: #ffffff;

  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: calc(100vh * 17 / 1080);

    .avatar-wrapper {
      position: relative;
      width: calc(100vw * 52 / 1920);
      height: calc(100vh * 52 / 1080);
      border: calc(100vw * 1 / 1920) solid rgba(122, 151, 255, 0.6);
      border-radius: 50%;
      background-size: contain;
      background-repeat: no-repeat;

      img {
        width: calc(100vw * 52 / 1920);
        height: calc(100vh * 52 / 1080);
      }
    }

    .name-wrapper {
      width: calc(100vw * 300 / 1920);
      display: flex;
      flex-direction: column;
      margin-left: calc(100vw * 12 / 1920);

      .name {
        width: calc(100vw * 300 / 1920);
        color: #222;
        font-weight: 600;
        font-size: calc(100vw * 15 / 1920);
        letter-spacing: 0px;
        line-height: calc(100vh * 16 / 1080);
      }

      .id {
        margin-top: calc(100vh * 7 / 1080);
        color: #7c86ac;
        font-weight: 400;
        font-size: calc(100vw * 12 / 1920);
        letter-spacing: 0px;
        line-height: calc(100vh * 12 / 1080);
      }
    }
  }

  .divider {
    width: 100%;
    margin: calc(100vh * 20 / 1080) 0 calc(100vh * 18 / 1080);
    border-bottom: calc(100vw * 1 / 1920) solid #f0f3fa;
  }

  .options {
       .option1 {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-left: 4px;
      margin-right: 4px;

      :first-child {
        margin-top: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .text {
        margin-left: 10px;
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw *  14 / 1920);
        letter-spacing: 0;
        line-height: 22px;
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
    .option {
      display: flex;
      align-items: center;
      margin-top: calc(100vh * 20 / 1080);
      cursor: pointer;

      :first-child {
        margin-top: 0px;
      }

      .icon {
        width: calc(100vw * 24 / 1920);
        height: calc(100vh * 24 / 1080);
        background-size: contain;
      }

      .text {
        margin-left: calc(100vw * 6 / 1920);
        color: #000000d9;
        font-weight: 700;
        font-size: calc(100vw * 14 / 1920);
        letter-spacing: 0;
        line-height: calc(100vh * 22 / 1080);
        cursor: pointer;
      }

      .personal-center {
        background-image: url(../assets/icon_grzx.png);
      }

      .my-document {
        background-image: url(../assets/icon_wdwd.png);
      }

      .my-favourite {
        background-image: url(../assets/icon_wdsc.png);
      }

      .logout {
        background-image: url(../assets/icon_tc.png);
      }
    }
  }
}

::v-deep(.el-alert--success.is-light) {
  background: #ebf9f7 !important;
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

// 导航项
.nav-item {
  height: calc(100vh * 40 / 1080);
  background: transparent;
  line-height: calc(100vh * 20 / 1080);
  white-space: pre-wrap;
  background-image: linear-gradient(270deg, rgba(30, 75, 202, 0.39) 0%, rgba(59, 130, 234, 0.28) 100%);
  border: calc(100vw * 1 / 1920) solid rgba(255, 255, 255, 0.2);
  border-radius: calc(100vh * 4 / 1080);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
  cursor: pointer;
  margin: 0 auto;

  &:hover {
    background: #59bce1;
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 16 / 1920);
    color: #ffffff;
    letter-spacing: 0;
  }
}

.choose {
  border: calc(100vw * 1 / 1920) solid rgba(255, 255, 255, 0.2);
  border-radius: calc(100vh * 4 / 1080);
  background: #59bce1;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 600;
}

.clbutton {
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: calc(100vh * 40 / 1080);
  left: calc(100vw * 40 / 1920);
}

.clbutton1 {
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: calc(100vh * 40 / 1080);
  left: calc(100vw * 160 / 1920);
}

.clbutton2 {
  left: calc(100vw * 180 / 1920);
}

.clbutton12 {
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 100 / 1920);
  background-image: linear-gradient(107deg, #3a6bc6 0%, #488aff 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 4 / 1080);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  bottom: calc(100vh * 40 / 1080);
  left: calc(100vw * 280 / 1920);
}
.showUp {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 20 / 1080);
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: calc(100vh * 10 / 1080);
  left: calc(100vw * 60 / 1920);
  height: calc(100vh * 40 / 1080);
  bottom: calc(100vh * 20 / 1080);
  cursor: pointer;

  &:hover {
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

.showUp1 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-out 0.4s;
  background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  box-shadow: 0 calc(100vh * 1 / 1080) calc(100vh * 7 / 1080) 0 rgba(100, 120, 212, 1);
  border-radius: calc(100vh * 20 / 1080);
  position: absolute;
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  bottom: calc(100vh * 10 / 1080);
  left: calc(100vw * 6 / 1920);
  height: calc(100vh * 40 / 1080);
  bottom: calc(100vh * 20 / 1080);
  cursor: pointer;

  &:hover {
    background-image: linear-gradient(103deg, #3a70c6 0%, #354eb6 100%);
  }

  img {
    width: calc(100vw * 18 / 1920);
    height: calc(100vh * 18 / 1080);
    float: left;
  }

  p {
    float: left;
    margin-left: calc(100vw * 10 / 1920);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: calc(100vw * 14 / 1920);
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
  }
}

::v-deep(.el-loading-spinner) {
  background-image: url("../img/icegif-1259.gif");
  background-repeat: no-repeat;
  background-size: calc(100vw * 150 / 1920) calc(100vh * 130 / 1080);
  height: calc(100vh * 100 / 1080);
  width: 100%;
  background-position: center;
  top: 40%;
}

::v-deep(.el-loading-spinner .circular) {
  display: none;
}

::v-deep(.el-loading-spinner .el-loading-text) {
  margin: calc(100vh * 85 / 1080) 0;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  transform: rotate(-90deg) translateY(calc(-100vh * 20 / 1080)) translateX(calc(-100vw * 16 / 1920));
  transform-origin: 0 0;
}

::v-deep(.el-drawer__header) {
  display: none;
}

::v-deep(.el-step.is-simple .el-step__arrow::after) {
  transform: rotate(60deg) translateY(0);
  transform-origin: 100% 100%;
  content: "";
  display: inline-block;
  position: absolute;
  height: 0;
  width: 0;
  background: #c0c4cc;
}

::v-deep(.el-notification__group) {
  height: auto !important;
}

::v-deep(.el-step.is-simple .el-step__arrow::before) {
  content: "";
  display: inline-block;
  position: absolute;
  height: calc(100vh * 30 / 1080);
  width: calc(100vw * 1 / 1920);
  background: #c0c4cc;
}

::v-deep(.el-step.is-simple .el-step__title) {
  font-size: calc(100vw * 14 / 1920) !important;
}

::v-deep(.el-step__title.is-success) {
  color: #1b2126;
}

::v-deep(.el-step__head.is-success) {
  color: #1b2126;
  border-color: #1b2126;
}

::v-deep(.el-step__title.is-process) {
  color: #bbc6d3;
}

::v-deep(.el-step__head.is-process) {
  color: #bbc6d3;
  border-color: #bbc6d3;
}

::v-deep(.el-steps--simple) {
  background: none !important;
}

.progressClass {
  width: 100%;
  height: calc(100vh * 26 / 1080);
  position: fixed;
  bottom: calc(100vh * 48 / 1080);
  right: 0;
  z-index: 999;

  .progress {
    position: absolute;
    right: calc(100vw * 52 / 1920);
    transition: ease-out 0.4s;
  }
}

::v-deep(.el-progress-bar__innerText) {
  padding: calc(100vh * 7 / 1080) !important;
  color: #fff !important;
}

.menu_label {
  width: 100%;
  border-radius: calc(100vh * 4 / 1080);
  margin-top: calc(100vh * 10 / 1080);
  min-height: calc(100vh * 158 / 1080);
  height: auto;
  padding: calc(100vh * 15 / 1080) calc(100vw * 16 / 1920);
  background: #f8f9fd;
  border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
  border-radius: calc(100vh * 4 / 1080);
  padding: calc(100vh * 15 / 1080) 0;
}

.el-tag {
  height: auto;
  white-space: normal !important;
  max-width: calc(100vw * 383 / 1920);
  word-wrap: break-word;
  text-align: left;
  margin-left: calc(100vw * 5 / 1920);
  margin-bottom: calc(100vh * 5 / 1080);
  float: left;
}

.el-main .ai-body .tab-item-fir .menu_label:active {
  background: #fff;
  border: calc(100vw * 1 / 1920) solid #4170f6;
}

.pass_input {
  width: 100%;
  height: calc(100vh * 40 / 1080);

  & /deep/ .el-input__inner {
    border: none;
    background-color: rgba(122, 151, 255, 0) !important;
    font-family: PingFangSC-Regular;
    font-size: calc(100vw * 14 / 1920);
    color: #666666;
    letter-spacing: 0;
    font-weight: 400;
  }
}

::v-deep(.el-alert__title) {
  font-family: PingFangSC-Regular;
  font-size: calc(100vw * 12 / 1920);
  color: #09873f;
  letter-spacing: 0;
  font-weight: 400;
}

::v-deep(.el-drawer__open .el-drawer.ltr) {
  width: 100% !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0;
}

::v-deep(.el-alert) {
  padding: calc(100vh * 8 / 1080) calc(100vw * 8 / 1920);
}

.fir-textarea1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 96.5%;
  background: #f8f9fd;
  border: calc(100vw * 1 / 1920) solid rgba(229, 232, 245, 1);
  border-radius: calc(100vh * 4 / 1080);
  margin: calc(100vh * 20 / 1080);
  margin-bottom: 0;
  height: calc(100vh * 158 / 1080);

  ::v-deep(.el-textarea__inner) {
    font-size: calc(100vw * 14 / 1920) !important;
    background-color: #f8f9fd !important;
    height: 100% !important;
    font-family: PingFangSC-Regular;
    padding: calc(100vh * 13 / 1080) calc(100vw * 18 / 1920) calc(100vh * 33 / 1080) calc(100vw * 16 / 1920);
  }
}

::v-deep(.el-collapse) {
  border: 0 solid #ebeef5;
}

::v-deep(.el-collapse-item__header.is-active) {
  border-bottom: calc(100vw * 1 / 1920) solid #ebeef5;
}

::v-deep(.el-collapse-item__header) {
  background-color: #f9fafe;
  height: calc(100vh * 40 / 1080);
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__wrap) {
  border-radius: calc(100vh * 4 / 1080);
}

::v-deep(.el-collapse-item__content) {
  background-color: #f9fafe;
  border-radius: calc(100vh * 4 / 1080);
}

.fir-textarea-max1 {
  height: 95% !important;
}

.highlight {
  background-color: yellow;
}

.result-title {
  position: relative;

  &:before {
    position: absolute;
    content: '';
    left: calc(-100vw * 10 / 1920);
    top: calc(100vh * 2 / 1080);
    width: calc(100vw * 4 / 1920);
    height: calc(100vh * 15 / 1080);
    background: #5585F0;
    border-radius: calc(100vh * 2 / 1080);
  }
}

// ***滚动条样式
.result-content::-webkit-scrollbar {
  width: calc(100vw * 6 / 1920);
}

.result-content::-webkit-scrollbar-track {
  background: #fff;
}

.result-content::-webkit-scrollbar-thumb {
  background: #488aff;
}

.elcol-title-left {
  width: calc(100vw * 8 / 1920);
  height: calc(100vh * 8 / 1080);
  border-radius: 50%;
  margin-top: calc(100vh * 20 / 1080);
  margin-left: calc(100vw * 10 / 1920);
}

.elcol-title {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: calc(100vh * 48 / 1080);
}

.elcol-title-text {
  padding-left: calc(100vw * 10 / 1920);
  text-align: left;
  width: 40%;
  height: 100%;
}

.elcol-title-text2 {
  font-size: calc(100vw * 14 / 1920);
  color: #d1d7de;
  width: calc(100vw * 75 / 1920);
}

.elcol-input {
  float: left;
  width: calc(60% - calc(100vw * 75 / 1920));
  border: none !important;
}
  .el-tag + .el-tag {
    margin-left: 10px;
  }

  .input-new-tag-top {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
  .button-new-tag-top {
    margin-left: 20px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
}
.input-group {
  display: flex; /* 使用 flexbox 布局 */
  align-items: center; /* 垂直居中对齐 */
  margin-bottom: 10px; /* 输入框和按钮间的间距 */
}

.form-label {
  margin-bottom: 5px; /* 标签与输入框之间的间距 */
margin-left:-845px ;
}
.file-item {
  // 蓝色边框
  border: 1px solid #488aff;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  text-align: left; /* 将整个文件项的内容居左 */
}
.file-name {
  margin-bottom: 8px; /* 添加底部间距，让文件名与关键词有些分隔 */
}
.keyword-input {
  margin-top: 5px; /* 每个关键词输入框之间的间距 */
}
.custom-input {
  width: 80%; /* 使输入框占满父容器的宽度 */
  padding: 12px 15px; /* 内边距，与 el-input 接近 */
  border: 1px solid #dcdfe6; /* 边框颜色 */
  border-radius: 4px; /* 圆角边框 */
  font-size: 13px; /* 字体大小 */
  color: #606266; /* 字体颜色 */
  background-color: #f5f7fa; /* 背景颜色 */
  transition: border-color 0.3s; /* 添加平滑过渡效果 */

  /* 去除默认的外观 */
  outline: none;
  box-shadow: none;
}

.custom-input::placeholder {
  color: #c0c4cc; /* 占位符颜色 */
  opacity: 1; /* 占位符透明度 */
}

.custom-input:focus {
  border-color: #488aff; /* 聚焦时的边框颜色 */
}

.custom-input:disabled {
  background-color: #f5f7fa; /* 禁用状态样式 */
  cursor: not-allowed; /* 禁用光标 */
}

</style>

